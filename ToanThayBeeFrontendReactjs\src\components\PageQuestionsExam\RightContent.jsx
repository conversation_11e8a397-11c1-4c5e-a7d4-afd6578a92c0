import NavigateBar from "../PageAddExam/NavigateBar";
import { Eye, Edit, Sparkles } from "lucide-react";
import { useSelector, useDispatch } from "react-redux";
import { setViewRightContent, putQuestionsExam, setQuestions, setAllQuestions } from "src/features/questionsExam/questionsExamSlice";
import QuestionView from "./QuestionView";
import ImageView from "./ImageView";
import LoadingSpinner from "../loading/LoadingSpinner";
import { classifyQuestions } from "src/features/ai/aiSlice";
import { useEffect } from "react";
import { setErrorMessage } from "src/features/state/stateApiSlice";
const RightContent = ({ examId }) => {
    const { view, loadingPut, questionsExam } = useSelector((state) => state.questionsExam);
    const { classifyResult, loading: aiLoading } = useSelector((state) => state.ai);
    const dispatch = useDispatch();

    const handlePutQuestions = () => {
        if (!examId) return;

        const questionsData = questionsExam.map((question, index) => {
            return {
                id: question.id,
                content: question.content,
                correctAnswer: question.correctAnswer || null,
                difficulty: question.difficulty || null,
                chapter: question.chapter || null,
                class: question.class,
                solution: question.solution,
                solutionUrl: question.solutionUrl || null,
                imageUrl: question.imageUrl || null,
                solutionImageUrl: question.solutionImageUrl || null,
                statements: question.statements.map((statement) => ({
                    id: statement.id,
                    content: statement.content,
                    isCorrect: statement.isCorrect,
                    difficulty: statement.difficulty,
                    imageUrl: statement.imageUrl,
                    isNewStatement: statement.isNewStatement || false,
                })),
                ExamQuestions: {
                    order: question.ExamQuestions?.order || index,
                },
                isNewQuestion: question.isNewQuestion || false,
                typeOfQuestion: question.typeOfQuestion,
                description: question.description,
            }
        })
        // console.log("questionsData", questionsData)

        dispatch(putQuestionsExam({ examId, questions: questionsData }));
    }

    const handleClassifyQuestions = async () => {
        // console.log(questionsExam)
        const questionsData = questionsExam?.map((question, index) => {
            return {
                originalIndex: index,
                class: question.class || "",
                chapter: question.chapter || "",
                difficulty: question.difficulty || "",
                typeOfQuestion: question.typeOfQuestion || "TN",
                content: question.content || "",
                statements: question.statements && question.statements.length > 0 ? question.statements.map((statement) => statement.content) : [],
            };
        });
        const data = await dispatch(classifyQuestions(questionsData)).unwrap();
        const result = data.data;
        if (!result) {
            dispatch(setErrorMessage("Không thể phân loại câu hỏi!"));
            return;
        }
        const updatedQuestions = questionsExam.map((question, index) => {
            const classifiedQuestion = result.find(cq => cq.originalIndex === index);
            if (classifiedQuestion) {
                return {
                    ...question,
                    class: classifiedQuestion.class,
                    chapter: classifiedQuestion.chapter,
                    difficulty: classifiedQuestion.difficulty
                };
            }
            return question;
        });
        dispatch(setAllQuestions(updatedQuestions));
    };

    return (
        <div className="flex flex-col h-[calc(100vh_-_138px)] bg-gray-50">
            {/* Compact Preview Header */}
            <div className="flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10">
                <div className="flex items-center gap-2">
                    <Eye className="w-3 h-3 text-gray-600" />
                    <h2 className="text-xs font-semibold text-gray-900">Xem trước</h2>
                </div>
                <div className="flex items-center gap-2">
                    <button
                        onClick={handleClassifyQuestions}
                        disabled={aiLoading || questionsExam.length === 0}
                        className="flex items-center gap-1 px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        title="Tự động phân loại câu hỏi bằng AI"
                    >
                        {aiLoading ? (
                            <>
                                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                                Đang xử lý...
                            </>
                        ) : (
                            <>
                                <Sparkles className="w-3 h-3" />
                                Phân loại AI
                            </>
                        )}
                    </button>
                    <button
                        disabled={loadingPut}
                        onClick={handlePutQuestions}
                        className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-orange-600 hover:bg-orange-700 text-white`}
                    >
                        {loadingPut ? <LoadingSpinner className="w-3 h-3" minHeight="min-h-0" /> : <Edit className="w-3 h-3" />}

                        Cập nhật
                    </button>
                </div>
            </div>

            {/* Scrollable Preview Content */}
            <div className="flex-1 overflow-y-auto p-3">
                <div className="bg-white rounded border border-gray-200 ">
                    <NavigateBar
                        list={[
                            {
                                id: 1,
                                name: 'Câu hỏi',
                                value: 'question'
                            },
                            {
                                id: 2,
                                name: 'Ảnh',
                                value: 'image'
                            }
                        ]}
                        active={view}
                        setActive={(value) => dispatch(setViewRightContent(value))}
                    />
                    {view === 'question' && <QuestionView />}
                    {view === 'image' && <ImageView />}
                </div>
            </div>
        </div>
    )
}

export default RightContent;