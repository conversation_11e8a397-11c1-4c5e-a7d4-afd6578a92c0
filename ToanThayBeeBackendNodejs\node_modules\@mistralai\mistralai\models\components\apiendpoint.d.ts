import * as z from "zod";
import { OpenEnum } from "../../types/enums.js";
export declare const ApiEndpoint: {
    readonly RootV1ChatCompletions: "/v1/chat/completions";
    readonly RootV1Embeddings: "/v1/embeddings";
    readonly RootV1FimCompletions: "/v1/fim/completions";
    readonly RootV1Moderations: "/v1/moderations";
    readonly RootV1ChatModerations: "/v1/chat/moderations";
};
export type ApiEndpoint = OpenEnum<typeof ApiEndpoint>;
/** @internal */
export declare const ApiEndpoint$inboundSchema: z.ZodType<ApiEndpoint, z.ZodTypeDef, unknown>;
/** @internal */
export declare const ApiEndpoint$outboundSchema: z.ZodType<ApiEndpoint, z.ZodTypeDef, ApiEndpoint>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ApiEndpoint$ {
    /** @deprecated use `ApiEndpoint$inboundSchema` instead. */
    const inboundSchema: z.ZodType<ApiEndpoint, z.ZodTypeDef, unknown>;
    /** @deprecated use `ApiEndpoint$outboundSchema` instead. */
    const outboundSchema: z.ZodType<ApiEndpoint, z.ZodTypeDef, ApiEndpoint>;
}
//# sourceMappingURL=apiendpoint.d.ts.map