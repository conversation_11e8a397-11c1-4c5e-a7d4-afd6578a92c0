/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type Prediction = {
  type?: "content" | undefined;
  content?: string | undefined;
};

/** @internal */
export const Prediction$inboundSchema: z.ZodType<
  Prediction,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: z.literal("content").default("content"),
  content: z.string().default(""),
});

/** @internal */
export type Prediction$Outbound = {
  type: "content";
  content: string;
};

/** @internal */
export const Prediction$outboundSchema: z.ZodType<
  Prediction$Outbound,
  z.ZodTypeDef,
  Prediction
> = z.object({
  type: z.literal("content").default("content" as const),
  content: z.string().default(""),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Prediction$ {
  /** @deprecated use `Prediction$inboundSchema` instead. */
  export const inboundSchema = Prediction$inboundSchema;
  /** @deprecated use `Prediction$outboundSchema` instead. */
  export const outboundSchema = Prediction$outboundSchema;
  /** @deprecated use `Prediction$Outbound` instead. */
  export type Outbound = Prediction$Outbound;
}

export function predictionToJSON(prediction: Prediction): string {
  return JSON.stringify(Prediction$outboundSchema.parse(prediction));
}

export function predictionFromJSON(
  jsonString: string,
): SafeParseResult<Prediction, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Prediction$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Prediction' from JSON`,
  );
}
