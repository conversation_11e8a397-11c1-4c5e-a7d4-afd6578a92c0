import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
export declare class Classifiers extends ClientSDK {
    /**
     * Moderations
     */
    moderate(request: components.ClassificationRequest, options?: RequestOptions): Promise<components.ModerationResponse>;
    /**
     * Chat Moderations
     */
    moderateChat(request: components.ChatModerationRequest, options?: RequestOptions): Promise<components.ModerationResponse>;
    /**
     * Classifications
     */
    classify(request: components.ClassificationRequest, options?: RequestOptions): Promise<components.ClassificationResponse>;
    /**
     * Chat Classifications
     */
    classifyChat(request: components.ChatClassificationRequest, options?: RequestOptions): Promise<components.ClassificationResponse>;
}
//# sourceMappingURL=classifiers.d.ts.map