import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as examApi from "../../services/examApi";
// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from "../filter/filterSlice";
import { apiHandler } from "../../utils/apiHandler";
import { ocrPdfWithMistralAPI } from "../../services/ocrExamApi";

export const postExam = createAsyncThunk(
    "addExam/postExam",
    async ({ examData, examImage, questions, examFile }, { dispatch }) => {
        return await apiHandler(dispatch, examApi.postExamAPI, { examData, examImage, questions, examFile }, () => { }, true, false);
    }
);

export const ocrPdfWithMistral = createAsyncThunk(
    "addExam/ocrPdfWithMistral",
    async (file, { dispatch }) => {
        return await apiHandler(dispatch, ocrPdfWithMistralAPI, file, () => { }, true, false);
    }
);

const initialState = {
    loading: false,
    step: 1,
    view: "exam",
    selectedIndex: 0,
    questionTNContent: "",
    questionDSContent: "",
    questionTLNContent: "",
    folder: "questionImage",
    correctAnswerTN: "",
    correctAnswerDS: "",
    correctAnswerTLN: "",
    showAddImagesModal: false,
    examData:
    {
        name: "",
        typeOfExam: null,
        class: null,
        chapter: null,
        year: null,
        description: "",
        testDuration: null,
        passRate: null,
        solutionUrl: "",
        imageUrl: "",
        public: false,
        isClassroomExam: false,
    },
    examImage: null,
    questions: [],
    examFile: null,
    markDownExam: "",
    loadingOcr: false,
    ocrFile: null,
    base64Images: [],
}

const addExamSlice = createSlice({
    name: "addExam",
    initialState,
    reducers: {
        nextStep: (state) => {
            state.step++;
        },
        prevStep: (state) => {
            state.step--;
        },
        setViewRightContent: (state, action) => {
            state.view = action.payload;
        },
        setSelectedIndex: (state, action) => {
            console.log("setSelectedIndex", action.payload);
            state.selectedIndex = action.payload;
        },
        setLoading: (state, action) => {
            state.loading = action.payload;
        },
        setQuestionTNContent: (state, action) => {
            state.questionTNContent = action.payload;
        },
        setQuestionDSContent: (state, action) => {
            state.questionDSContent = action.payload;
        },
        setQuestionTLNContent: (state, action) => {
            state.questionTLNContent = action.payload;
        },
        setCorrectAnswerTN: (state, action) => {
            state.correctAnswerTN = action.payload;
        },
        setCorrectAnswerDS: (state, action) => {
            state.correctAnswerDS = action.payload;
        },
        setCorrectAnswerTLN: (state, action) => {
            state.correctAnswerTLN = action.payload;
        },

        setExamData: (state, action) => {
            const { field, value } = action.payload;
            // console.log('Setting exam data in slice:', field, value);
            state.examData[field] = value;
        },
        setExamImage: (state, action) => {
            state.examImage = action.payload;
        },
        setExamFile: (state, action) => {
            state.examFile = action.payload;
        },
        setStep: (state, action) => {
            state.step = action.payload;
        },
        resetData: (state) => {
            state = initialState;
        },
        setQuestions: (state, action) => {
            state.questions = action.payload;
        },
        setMarkDownExam: (state, action) => {
            state.markDownExam = action.payload;
        },
        setBase64Images: (state, action) => {
            state.base64Images = action.payload;
        },
        setShowAddImagesModal: (state, action) => {
            state.showAddImagesModal = action.payload;
        },
        setOcrFile: (state, action) => {
            state.ocrFile = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(postExam.pending, (state) => {
                state.loading = true;
            })
            .addCase(postExam.fulfilled, () => initialState)
            .addCase(postExam.rejected, (state) => {
                state.loading = false;
            })
            .addCase(ocrPdfWithMistral.pending, (state) => {
                state.loadingOcr = true;
            })
            .addCase(ocrPdfWithMistral.fulfilled, (state, action) => {
                state.markDownExam = action.payload.markdown;
                state.base64Images = action.payload.base64Images;
                state.loadingOcr = false;
            })
            .addCase(ocrPdfWithMistral.rejected, (state) => {
                state.loadingOcr = false;
            })
    }
});

export const {
    setLoading,
    resetData,
    nextStep,
    prevStep,
    setExamData,
    setStep,
    setExamImage,
    setExamFile,
    setQuestionTNContent,
    setQuestionDSContent,
    setQuestionTLNContent,
    setCorrectAnswerTN,
    setCorrectAnswerDS,
    setCorrectAnswerTLN,
    setQuestions,
    setSelectedIndex,
    setShowAddImagesModal,
    setViewRightContent,
    setOcrFile,
    setMarkDownExam,
    setBase64Images,
} = addExamSlice.actions;
export default addExamSlice.reducer;