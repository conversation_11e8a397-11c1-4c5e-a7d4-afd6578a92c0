{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\RightContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$(),\n  _s7 = $RefreshSig$(),\n  _s8 = $RefreshSig$();\n// Optimized Preview Panel Component\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { Eye, FileText, Image, File, ImagePlus } from \"lucide-react\";\nimport { useEffect, useState } from \"react\";\nimport PdfViewer from \"../ViewPdf\";\nimport NavigateBar from \"./NavigateBar\";\nimport * as questionUntil from \"src/utils/question/questionUtils\";\nimport useDebouncedEffect from \"src/hooks/useDebouncedEffect\";\nimport { setQuestions, setSelectedIndex, setShowAddImagesModal, setViewRightContent } from \"src/features/addExam/addExamSlice\";\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageExamView = () => {\n  _s();\n  const {\n    examImage\n  } = useSelector(state => state.addExam);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: examImage ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: URL.createObjectURL(examImage),\n        alt: \"exam\",\n        className: \"w-full object-contain\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col items-center\",\n      children: [/*#__PURE__*/_jsxDEV(Image, {\n        className: \"w-6 h-6 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-500\",\n        children: \"Ch\\u01B0a c\\xF3 \\u1EA3nh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 17\n    }, this)\n  }, void 0, false);\n};\n_s(ImageExamView, \"n7yNOFYIxX43G5cs3TSUaLVZo44=\", false, function () {\n  return [useSelector];\n});\n_c = ImageExamView;\nconst PdfView = () => {\n  _s2();\n  const {\n    examFile\n  } = useSelector(state => state.addExam);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: examFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: /*#__PURE__*/_jsxDEV(PdfViewer, {\n        url: URL.createObjectURL(examFile)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col items-center\",\n      children: [/*#__PURE__*/_jsxDEV(File, {\n        className: \"w-6 h-6 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-500\",\n        children: \"Ch\\u01B0a c\\xF3 file PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 17\n    }, this)\n  }, void 0, false);\n};\n_s2(PdfView, \"Z7mKjY/U71p40B1bogstxoIyP1g=\", false, function () {\n  return [useSelector];\n});\n_c2 = PdfView;\nconst ImageView = () => {\n  _s3();\n  var _images$folder;\n  const {\n    images\n  } = useSelector(state => state.images);\n  const {\n    folder\n  } = useSelector(state => state.addExam);\n  const dispatch = useDispatch();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-xs grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => dispatch(setShowAddImagesModal(true)),\n      className: \"border-dashed border-2 hover:bg-gray-100 border-gray-300 hover:shadow-lg rounded-lg p-8 flex flex-col items-center justify-center gap-2\",\n      children: \"Th\\xEAm \\u1EA3nh\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this), images === null || images === void 0 ? void 0 : (_images$folder = images[folder]) === null || _images$folder === void 0 ? void 0 : _images$folder.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 group relative w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: [\"H\\xECnh \\u1EA3nh \", index + 1, \":\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative w-full h-48 rounded-lg overflow-hidden border transition-all duration-300 \\r hover:border-sky-400 hover:shadow-lg group cursor-move\",\n        draggable: true,\n        onDragStart: e => {\n          e.dataTransfer.setData(\"text/plain\", image);\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: image,\n          alt: \"image-\".concat(index),\n          className: \"w-full h-full object-contain transition-all duration-300 group-hover:brightness-75\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\",\n          children: /*#__PURE__*/_jsxDEV(ImagePlus, {\n            className: \"w-8 h-8 text-white bg-sky-500 p-1 rounded-full shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 21\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 17\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 9\n  }, this);\n};\n_s3(ImageView, \"Pmjg4fgXkLCx9z/NxyHwysJT9Lc=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c3 = ImageView;\nconst ExamView = () => {\n  _s4();\n  const {\n    examData,\n    examImage,\n    examFile\n  } = useSelector(state => state.addExam);\n  const [view, setView] = useState('image');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      div: true,\n      className: \"mb-3 p-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-bold text-gray-900 mb-2\",\n        children: examData.name || \"Tên đề thi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-1 text-xs text-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"Ki\\u1EC3u:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 26\n          }, this), \" \", examData.typeOfExam || \"Chưa chọn\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"L\\u1EDBp:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 26\n          }, this), \" \", examData.class || \"Chưa chọn\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"N\\u0103m:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 26\n          }, this), \" \", examData.year || \"Chưa chọn\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 21\n        }, this), examData.chapter && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"Ch\\u01B0\\u01A1ng:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 47\n          }, this), \" \", examData.chapter]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 42\n        }, this), examData.testDuration && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"Th\\u1EDDi gian:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 52\n          }, this), \" \", examData.testDuration, \"p\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 47\n        }, this), examData.passRate && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"\\u0110i\\u1EC3m \\u0111\\u1EA1t:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 48\n          }, this), \" \", examData.passRate, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 43\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this), examData.solutionUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600\",\n          children: [\"Link l\\u1EDDi gi\\u1EA3i: \", examData.solutionUrl]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 25\n      }, this), examData.description && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600\",\n          children: examData.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-1\",\n        children: [examData.public && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded-full\",\n          children: \"C\\xF4ng khai\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 25\n        }, this), examData.isClassroomExam && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-0.5 bg-blue-100 text-blue-800 text-xs font-medium rounded-full\",\n          children: \"\\u0110\\u1EC1 thi l\\u1EDBp\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 25\n        }, this), !examData.public && !examData.isClassroomExam && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-0.5 bg-gray-100 text-gray-800 text-xs font-medium rounded-full\",\n          children: \"Ri\\xEAng t\\u01B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(NavigateBar, {\n      list: [{\n        id: 1,\n        name: 'Ảnh đề thi',\n        value: 'image'\n      }, {\n        id: 2,\n        name: 'File đề thi',\n        value: 'pdf'\n      }],\n      active: view,\n      setActive: setView\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 13\n    }, this), view === 'image' && /*#__PURE__*/_jsxDEV(ImageExamView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 34\n    }, this), view === 'pdf' && /*#__PURE__*/_jsxDEV(PdfView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 32\n    }, this)]\n  }, void 0, true);\n};\n_s4(ExamView, \"2rrSv/c6+wbxcLl4f1+uyIHx6fc=\", false, function () {\n  return [useSelector];\n});\n_c4 = ExamView;\nconst QuestionViewHeader = _ref => {\n  let {\n    title,\n    count,\n    noQuestionText\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \" p-3 \",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-1 mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-3 h-3 text-gray-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-xs font-semibold text-gray-900\",\n        children: [title, \" (\", count, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 13\n    }, this), count === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-4 text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-6 h-6 mx-auto mb-1 opacity-50\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs\",\n        children: noQuestionText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 9\n  }, this);\n};\n_c5 = QuestionViewHeader;\nconst QuestionTNView = () => {\n  _s5();\n  const {\n    questions,\n    step,\n    selectedIndex\n  } = useSelector(state => state.addExam);\n  const [questionsTN, setQuestionsTN] = useState([]);\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const dispatch = useDispatch();\n  useEffect(() => {\n    setQuestionsTN(questions.filter(q => q.questionData.typeOfQuestion === 'TN'));\n  }, [questions]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi tr\\u1EAFc nghi\\u1EC7m\",\n      count: questionsTN.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi tr\\u1EAFc nghi\\u1EC7m\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: questionsTN.map((question, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 p-1 \".concat(step === 4 && selectedIndex === index ? 'border border-blue-500 rounded-lg bg-blue-50' : 'border border-gray-300 rounded-lg bg-gray-50', \" \").concat(step === 4 ? 'cursor-pointer' : ''),\n        onClick: () => step === 4 && dispatch(setSelectedIndex(index)),\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"text-xs font-bold\",\n          children: [\"C\\xE2u h\\u1ECFi \", index + 1, /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-500 font-normal\",\n            children: [\" (\", question.questionData.class || \"Chưa chọn\", \" - \", question.questionData.chapter || \"Chưa chọn\", \" - \", question.questionData.difficulty || \"Chưa chọn\", \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n          text: question.questionData.content,\n          className: \"text-xs break-words w-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-2 mt-2\",\n          children: question.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs font-bold whitespace-nowrap\",\n              children: prefixTN[index]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n              text: statement.content,\n              className: \"text-xs break-words w-full \".concat(statement.isCorrect ? 'text-green-500' : '')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 37\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 33\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 25\n        }, this), question.questionData.solution && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"text-xs font-bold\",\n            children: \"L\\u1EDDi gi\\u1EA3i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n            content: question.questionData.solution,\n            className: \" w-full\",\n            style: {\n              fontSize: '12px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 29\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s5(QuestionTNView, \"XDS5Iq+nmdra/BuxsGlnxQlLbnI=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c6 = QuestionTNView;\nconst QuestionDSView = () => {\n  _s6();\n  const {\n    questions,\n    step,\n    selectedIndex\n  } = useSelector(state => state.addExam);\n  const [questionsDS, setQuestionsDS] = useState([]);\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const dispatch = useDispatch();\n  useEffect(() => {\n    setQuestionsDS(questions.filter(q => q.questionData.typeOfQuestion === 'DS'));\n  }, [questions]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi \\u0111\\xFAng sai\",\n      count: questionsDS.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi \\u0111\\xFAng sai\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: questionsDS.map((question, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 p-1 \".concat(step === 4 && selectedIndex === index ? 'border border-blue-500 rounded-lg bg-blue-50' : 'border border-gray-300 rounded-lg bg-gray-50', \" \").concat(step === 4 ? 'cursor-pointer' : ''),\n        onClick: () => step === 4 && dispatch(setSelectedIndex(index)),\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"text-xs font-bold\",\n          children: [\"C\\xE2u h\\u1ECFi \", index + 1, /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-500 font-normal\",\n            children: [\" (\", question.questionData.class || \"Chưa chọn\", \" - \", question.questionData.chapter || \"Chưa chọn\", \" - \", question.questionData.difficulty || \"Chưa chọn\", \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n          text: question.questionData.content,\n          className: \"text-xs break-words w-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-2 mt-2\",\n          children: question.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs font-bold whitespace-nowrap\",\n              children: prefixDS[index]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n              text: statement.content,\n              className: \"text-xs break-words w-full \".concat(statement.isCorrect ? 'text-green-500' : 'text-red-500')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 37\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 33\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 25\n        }, this), question.questionData.solution && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"text-xs font-bold\",\n            children: \"L\\u1EDDi gi\\u1EA3i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n            content: question.questionData.solution,\n            className: \" w-full\",\n            style: {\n              fontSize: '0.75rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 29\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s6(QuestionDSView, \"3wd9/0PCVFrhPM/697eZiXFGko4=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c7 = QuestionDSView;\nconst QuestionTLNView = () => {\n  _s7();\n  const {\n    questions,\n    step,\n    selectedIndex\n  } = useSelector(state => state.addExam);\n  const dispatch = useDispatch();\n  const [questionsTLN, setQuestionsTLN] = useState([]);\n  useEffect(() => {\n    setQuestionsTLN(questions.filter(q => q.questionData.typeOfQuestion === 'TLN'));\n  }, [questions]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\",\n      count: questionsTLN.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: questionsTLN.map((question, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 p-1 \".concat(step === 4 && selectedIndex === index ? 'border border-blue-500 rounded-lg bg-blue-50' : 'border border-gray-300 rounded-lg bg-gray-50', \" \").concat(step === 4 ? 'cursor-pointer' : ''),\n        onClick: () => step === 4 && dispatch(setSelectedIndex(index)),\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"text-xs font-bold\",\n          children: [\"C\\xE2u h\\u1ECFi \", index + 1, /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-500 font-normal\",\n            children: [\" (\", question.questionData.class || \"Chưa chọn\", \" - \", question.questionData.chapter || \"Chưa chọn\", \" - \", question.questionData.difficulty || \"Chưa chọn\", \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n          text: question.questionData.content,\n          className: \"text-xs break-words w-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs font-bold mt-2\",\n          children: \"\\u0110\\xE1p \\xE1n:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n          text: question.questionData.correctAnswer,\n          className: \"text-xs break-words w-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 25\n        }, this), question.questionData.solution && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"text-xs font-bold\",\n            children: \"L\\u1EDDi gi\\u1EA3i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n            content: question.questionData.solution,\n            className: \" w-full\",\n            style: {\n              fontSize: '0.75rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 29\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s7(QuestionTLNView, \"pEUVXMIrMKx3w/8LW6JV8GlXu1Q=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c8 = QuestionTLNView;\nconst QuestionView = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionTNView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"border-gray-200\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(QuestionDSView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"border-gray-200\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(QuestionTLNView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_c9 = QuestionView;\nconst RightContent = () => {\n  _s8();\n  const {\n    examData,\n    view\n  } = useSelector(state => state.addExam);\n  const dispatch = useDispatch();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_42px)] bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-start bg-white border-b border-gray-200 px-3 py-2 h-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Eye, {\n          className: \"w-3 h-3 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xs font-semibold text-gray-900\",\n          children: \"Xem tr\\u01B0\\u1EDBc\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded border border-gray-200 \",\n        children: [/*#__PURE__*/_jsxDEV(NavigateBar, {\n          list: [{\n            id: 1,\n            name: 'Đề thi',\n            value: 'exam'\n          }, {\n            id: 2,\n            name: 'Câu hỏi',\n            value: 'question'\n          }, {\n            id: 3,\n            name: 'Ảnh',\n            value: 'image'\n          }],\n          active: view,\n          setActive: value => dispatch(setViewRightContent(value))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 21\n        }, this), view === 'exam' && /*#__PURE__*/_jsxDEV(ExamView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 41\n        }, this), view === 'question' && /*#__PURE__*/_jsxDEV(QuestionView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 45\n        }, this), view === 'image' && /*#__PURE__*/_jsxDEV(ImageView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 42\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 336,\n    columnNumber: 9\n  }, this);\n};\n_s8(RightContent, \"OV7sSbm5OKa6lmbqT/x8oO+ugjc=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c10 = RightContent;\nexport default RightContent;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10;\n$RefreshReg$(_c, \"ImageExamView\");\n$RefreshReg$(_c2, \"PdfView\");\n$RefreshReg$(_c3, \"ImageView\");\n$RefreshReg$(_c4, \"ExamView\");\n$RefreshReg$(_c5, \"QuestionViewHeader\");\n$RefreshReg$(_c6, \"QuestionTNView\");\n$RefreshReg$(_c7, \"QuestionDSView\");\n$RefreshReg$(_c8, \"QuestionTLNView\");\n$RefreshReg$(_c9, \"QuestionView\");\n$RefreshReg$(_c10, \"RightContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "Eye", "FileText", "Image", "File", "ImagePlus", "useEffect", "useState", "PdfViewer", "NavigateBar", "questionUntil", "useDebouncedEffect", "setQuestions", "setSelectedIndex", "setShowAddImagesModal", "setViewRightContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkdownPreviewWithMath", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageExamView", "_s", "examImage", "state", "addExam", "children", "className", "src", "URL", "createObjectURL", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "PdfView", "_s2", "examFile", "url", "_c2", "ImageView", "_s3", "_images$folder", "images", "folder", "dispatch", "onClick", "map", "image", "index", "draggable", "onDragStart", "e", "dataTransfer", "setData", "concat", "_c3", "<PERSON><PERSON><PERSON>ie<PERSON>", "_s4", "examData", "view", "<PERSON><PERSON><PERSON><PERSON>", "div", "name", "typeOfExam", "class", "year", "chapter", "testDuration", "passRate", "solutionUrl", "description", "public", "isClassroomExam", "list", "id", "value", "active", "setActive", "_c4", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "title", "count", "noQuestionText", "_c5", "QuestionTNView", "_s5", "questions", "step", "selectedIndex", "questionsTN", "setQuestionsTN", "prefixTN", "filter", "q", "questionData", "typeOfQuestion", "length", "question", "difficulty", "text", "content", "statements", "statement", "isCorrect", "solution", "style", "fontSize", "_c6", "QuestionDSView", "_s6", "questionsDS", "setQuestionsDS", "prefixDS", "_c7", "QuestionTLNView", "_s7", "questionsTLN", "setQuestionsTLN", "<PERSON><PERSON><PERSON><PERSON>", "_c8", "Question<PERSON>iew", "_c9", "RightContent", "_s8", "_c10", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/RightContent.jsx"], "sourcesContent": ["// Optimized Preview Panel Component\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { Eye, FileText, Image, File, ImagePlus } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport PdfViewer from \"../ViewPdf\";\r\nimport NavigateBar from \"./NavigateBar\";\r\nimport * as questionUntil from \"src/utils/question/questionUtils\";\r\nimport useDebouncedEffect from \"src/hooks/useDebouncedEffect\";\r\nimport { setQuestions, setSelectedIndex, setShowAddImagesModal, setViewRightContent } from \"src/features/addExam/addExamSlice\";\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\r\n\r\nconst ImageExamView = () => {\r\n    const { examImage } = useSelector((state) => state.addExam);\r\n\r\n    return (\r\n        <>\r\n            {examImage ? (\r\n                <div className=\"p-3\">\r\n                    <img src={URL.createObjectURL(examImage)} alt=\"exam\" className=\"w-full object-contain\" />\r\n                </div>\r\n            ) : (\r\n                <div className=\"p-3 flex flex-col items-center\">\r\n                    <Image className=\"w-6 h-6 text-gray-400\" />\r\n                    <p className=\"text-sm text-gray-500\">Chưa có ảnh</p>\r\n                </div>\r\n            )}\r\n        </>\r\n    )\r\n}\r\n\r\nconst PdfView = () => {\r\n    const { examFile } = useSelector((state) => state.addExam);\r\n\r\n    return (\r\n        <>\r\n            {examFile ? (\r\n                <div className=\"p-3\">\r\n                    <PdfViewer url={URL.createObjectURL(examFile)} />\r\n                </div>\r\n            ) : (\r\n                <div className=\"p-3 flex flex-col items-center\">\r\n                    <File className=\"w-6 h-6 text-gray-400\" />\r\n                    <p className=\"text-sm text-gray-500\">Chưa có file PDF</p>\r\n                </div>\r\n            )}\r\n        </>\r\n    )\r\n}\r\n\r\nconst ImageView = () => {\r\n    const { images } = useSelector((state) => state.images);\r\n    const { folder } = useSelector((state) => state.addExam);\r\n    const dispatch = useDispatch();\r\n\r\n    return (\r\n        <div className=\"text-xs grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-3\">\r\n            <button\r\n                onClick={() => dispatch(setShowAddImagesModal(true))}\r\n                className=\"border-dashed border-2 hover:bg-gray-100 border-gray-300 hover:shadow-lg rounded-lg p-8 flex flex-col items-center justify-center gap-2\"\r\n            >\r\n                Thêm ảnh\r\n            </button>\r\n            {images?.[folder]?.map((image, index) => (\r\n                <div key={index} className=\"mb-6 group relative w-full\">\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                        Hình ảnh {index + 1}:\r\n                    </label>\r\n\r\n                    <div\r\n                        className=\"relative w-full h-48 rounded-lg overflow-hidden border transition-all duration-300 \r\n                   hover:border-sky-400 hover:shadow-lg group cursor-move\"\r\n                        draggable\r\n                        onDragStart={(e) => {\r\n                            e.dataTransfer.setData(\"text/plain\", image);\r\n                        }}\r\n                    >\r\n                        <img\r\n                            src={image}\r\n                            alt={`image-${index}`}\r\n                            className=\"w-full h-full object-contain transition-all duration-300 group-hover:brightness-75\"\r\n                        />\r\n\r\n                        {/* Icon hiện khi hover */}\r\n                        <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\r\n                            <ImagePlus className=\"w-8 h-8 text-white bg-sky-500 p-1 rounded-full shadow-lg\" />\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            ))}\r\n        </div>\r\n    );\r\n}\r\n\r\n\r\nconst ExamView = () => {\r\n    const { examData, examImage, examFile } = useSelector((state) => state.addExam);\r\n    const [view, setView] = useState('image');\r\n\r\n    return (\r\n        <>\r\n            {/* Compact Exam Header */}\r\n            <div div className=\"mb-3 p-3\" >\r\n                <h3 className=\"text-sm font-bold text-gray-900 mb-2\">\r\n                    {examData.name || \"Tên đề thi\"}\r\n                </h3>\r\n                <div className=\"grid grid-cols-2 gap-1 text-xs text-gray-600\">\r\n                    <div><span className=\"font-medium\">Kiểu:</span> {examData.typeOfExam || \"Chưa chọn\"}</div>\r\n                    <div><span className=\"font-medium\">Lớp:</span> {examData.class || \"Chưa chọn\"}</div>\r\n                    <div><span className=\"font-medium\">Năm:</span> {examData.year || \"Chưa chọn\"}</div>\r\n                    {examData.chapter && <div><span className=\"font-medium\">Chương:</span> {examData.chapter}</div>}\r\n                    {examData.testDuration && <div><span className=\"font-medium\">Thời gian:</span> {examData.testDuration}p</div>}\r\n                    {examData.passRate && <div><span className=\"font-medium\">Điểm đạt:</span> {examData.passRate}%</div>}\r\n                </div>\r\n                {\r\n                    examData.solutionUrl && (\r\n                        <div className=\"mt-2\">\r\n                            <p className=\"text-xs text-gray-600\">Link lời giải: {examData.solutionUrl}</p>\r\n                        </div>\r\n                    )\r\n                }\r\n                {\r\n                    examData.description && (\r\n                        <div className=\"mt-2\">\r\n                            <p className=\"text-xs text-gray-600\">{examData.description}</p>\r\n                        </div>\r\n                    )\r\n                }\r\n            </div>\r\n\r\n            {/* Compact Status Badges */}\r\n            <div className=\"p-3\" >\r\n                <div className=\"flex flex-wrap gap-1\">\r\n                    {examData.public && (\r\n                        <span className=\"px-2 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded-full\">\r\n                            Công khai\r\n                        </span>\r\n                    )}\r\n                    {examData.isClassroomExam && (\r\n                        <span className=\"px-2 py-0.5 bg-blue-100 text-blue-800 text-xs font-medium rounded-full\">\r\n                            Đề thi lớp\r\n                        </span>\r\n                    )}\r\n                    {!examData.public && !examData.isClassroomExam && (\r\n                        <span className=\"px-2 py-0.5 bg-gray-100 text-gray-800 text-xs font-medium rounded-full\">\r\n                            Riêng tư\r\n                        </span>\r\n                    )}\r\n                </div>\r\n            </div>\r\n            <NavigateBar\r\n                list={[{\r\n                    id: 1,\r\n                    name: 'Ảnh đề thi',\r\n                    value: 'image'\r\n                },\r\n                {\r\n                    id: 2,\r\n                    name: 'File đề thi',\r\n                    value: 'pdf'\r\n                }\r\n                ]}\r\n                active={view}\r\n                setActive={setView}\r\n            />\r\n            {view === 'image' && <ImageExamView />}\r\n            {view === 'pdf' && <PdfView />}\r\n\r\n        </>\r\n    )\r\n}\r\nconst QuestionViewHeader = ({ title, count, noQuestionText }) => {\r\n    return (\r\n        <div className=\" p-3 \">\r\n            <div className=\"flex items-center gap-1 mb-2\">\r\n                <FileText className=\"w-3 h-3 text-gray-600\" />\r\n                <h4 className=\"text-xs font-semibold text-gray-900\">{title} ({count})</h4>\r\n            </div>\r\n            {count === 0 && (\r\n                <div className=\"text-center py-4 text-gray-500\">\r\n                    <FileText className=\"w-6 h-6 mx-auto mb-1 opacity-50\" />\r\n                    <p className=\"text-xs\">{noQuestionText}</p>\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst QuestionTNView = () => {\r\n    const { questions, step, selectedIndex } = useSelector((state) => state.addExam);\r\n    const [questionsTN, setQuestionsTN] = useState([]);\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const dispatch = useDispatch();\r\n\r\n    useEffect(() => {\r\n        setQuestionsTN(questions.filter(q => q.questionData.typeOfQuestion === 'TN'));\r\n    }, [questions]);\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi trắc nghiệm\" count={questionsTN.length} noQuestionText=\"Chưa có câu hỏi trắc nghiệm\" />\r\n            <div className=\"p-3\">\r\n                {questionsTN.map((question, index) => (\r\n                    <div\r\n                        key={index} className={`mb-4 p-1 ${step === 4 && selectedIndex === index ? 'border border-blue-500 rounded-lg bg-blue-50' : 'border border-gray-300 rounded-lg bg-gray-50'} ${step === 4 ? 'cursor-pointer' : ''}`}\r\n                        onClick={() => step === 4 && dispatch(setSelectedIndex(index))}\r\n                    >\r\n                        <h5 className=\"text-xs font-bold\">Câu hỏi {index + 1}\r\n                            <span className=\"text-gray-500 font-normal\"> ({question.questionData.class || \"Chưa chọn\"} - {question.questionData.chapter || \"Chưa chọn\"} - {question.questionData.difficulty || \"Chưa chọn\"})</span>\r\n                        </h5>\r\n                        <LatexRenderer text={question.questionData.content} className=\"text-xs break-words w-full\" />\r\n                        <div className=\"flex flex-col gap-2 mt-2\">\r\n                            {question.statements.map((statement, index) => (\r\n                                <div key={index} className=\"flex items-center gap-1\">\r\n                                    <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                        {prefixTN[index]}\r\n                                    </p>\r\n                                    <LatexRenderer text={statement.content} className={`text-xs break-words w-full ${statement.isCorrect ? 'text-green-500' : ''}`} />\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                        {question.questionData.solution && (\r\n                            <div className=\"mt-2\">\r\n                                <h6 className=\"text-xs font-bold\">Lời giải</h6>\r\n                                <MarkdownPreviewWithMath content={question.questionData.solution} className=\" w-full\" style={{ fontSize: '12px' }} />\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionDSView = () => {\r\n    const { questions, step, selectedIndex } = useSelector((state) => state.addExam);\r\n    const [questionsDS, setQuestionsDS] = useState([]);\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    const dispatch = useDispatch();\r\n\r\n    useEffect(() => {\r\n        setQuestionsDS(questions.filter(q => q.questionData.typeOfQuestion === 'DS'));\r\n    }, [questions]);\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi đúng sai\" count={questionsDS.length} noQuestionText=\"Chưa có câu hỏi đúng sai\" />\r\n            <div className=\"p-3\">\r\n                {questionsDS.map((question, index) => (\r\n                    <div\r\n                        key={index} className={`mb-4 p-1 ${step === 4 && selectedIndex === index ? 'border border-blue-500 rounded-lg bg-blue-50' : 'border border-gray-300 rounded-lg bg-gray-50'} ${step === 4 ? 'cursor-pointer' : ''}`}\r\n                        onClick={() => step === 4 && dispatch(setSelectedIndex(index))}\r\n                    >\r\n                        <h5 className=\"text-xs font-bold\">Câu hỏi {index + 1}\r\n                            <span className=\"text-gray-500 font-normal\"> ({question.questionData.class || \"Chưa chọn\"} - {question.questionData.chapter || \"Chưa chọn\"} - {question.questionData.difficulty || \"Chưa chọn\"})</span>\r\n                        </h5>\r\n                        <LatexRenderer text={question.questionData.content} className=\"text-xs break-words w-full\" />\r\n                        <div className=\"flex flex-col gap-2 mt-2\">\r\n                            {question.statements.map((statement, index) => (\r\n                                <div key={index} className=\"flex items-center gap-1\">\r\n                                    <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                        {prefixDS[index]}\r\n                                    </p>\r\n                                    <LatexRenderer text={statement.content} className={`text-xs break-words w-full ${statement.isCorrect ? 'text-green-500' : 'text-red-500'}`} />\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                        {question.questionData.solution && (\r\n                            <div className=\"mt-2\">\r\n                                <h6 className=\"text-xs font-bold\">Lời giải</h6>\r\n                                <MarkdownPreviewWithMath content={question.questionData.solution} className=\" w-full\" style={{ fontSize: '0.75rem' }} />\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionTLNView = () => {\r\n    const { questions, step, selectedIndex } = useSelector((state) => state.addExam);\r\n    const dispatch = useDispatch();\r\n    const [questionsTLN, setQuestionsTLN] = useState([]);\r\n\r\n    useEffect(() => {\r\n        setQuestionsTLN(questions.filter(q => q.questionData.typeOfQuestion === 'TLN'));\r\n    }, [questions]);\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi trả lời ngắn\" count={questionsTLN.length} noQuestionText=\"Chưa có câu hỏi trả lời ngắn\" />\r\n            <div className=\"p-3\">\r\n                {questionsTLN.map((question, index) => (\r\n                    <div\r\n                        key={index} className={`mb-4 p-1 ${step === 4 && selectedIndex === index ? 'border border-blue-500 rounded-lg bg-blue-50' : 'border border-gray-300 rounded-lg bg-gray-50'} ${step === 4 ? 'cursor-pointer' : ''}`}\r\n                        onClick={() => step === 4 && dispatch(setSelectedIndex(index))}\r\n                    >\r\n                        <h5 className=\"text-xs font-bold\">Câu hỏi {index + 1}\r\n                            <span className=\"text-gray-500 font-normal\"> ({question.questionData.class || \"Chưa chọn\"} - {question.questionData.chapter || \"Chưa chọn\"} - {question.questionData.difficulty || \"Chưa chọn\"})</span>\r\n                        </h5>\r\n                        <LatexRenderer text={question.questionData.content} className=\"text-xs break-words w-full\" />\r\n                        <p className=\"text-xs font-bold mt-2\">Đáp án:</p>\r\n                        <LatexRenderer text={question.questionData.correctAnswer} className=\"text-xs break-words w-full\" />\r\n                        {question.questionData.solution && (\r\n                            <div className=\"mt-2\">\r\n                                <h6 className=\"text-xs font-bold\">Lời giải</h6>\r\n                                <MarkdownPreviewWithMath content={question.questionData.solution} className=\" w-full\" style={{ fontSize: '0.75rem' }} />\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionView = () => {\r\n\r\n    return (\r\n        <>\r\n            <QuestionTNView />\r\n            <hr className=\"border-gray-200\" />\r\n            <QuestionDSView />\r\n            <hr className=\"border-gray-200\" />\r\n            <QuestionTLNView />\r\n        </>\r\n    )\r\n}\r\n\r\nconst RightContent = () => {\r\n    const { examData, view } = useSelector((state) => state.addExam);\r\n    const dispatch = useDispatch();\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_42px)] bg-gray-50\">\r\n            {/* Compact Preview Header */}\r\n            <div className=\"flex items-center justify-start bg-white border-b border-gray-200 px-3 py-2 h-10\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <Eye className=\"w-3 h-3 text-gray-600\" />\r\n                    <h2 className=\"text-xs font-semibold text-gray-900\">Xem trước</h2>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Scrollable Preview Content */}\r\n            <div className=\"flex-1 overflow-y-auto p-3\">\r\n                <div className=\"bg-white rounded border border-gray-200 \">\r\n                    <NavigateBar\r\n                        list={[{\r\n                            id: 1,\r\n                            name: 'Đề thi',\r\n                            value: 'exam'\r\n                        },\r\n                        {\r\n                            id: 2,\r\n                            name: 'Câu hỏi',\r\n                            value: 'question'\r\n                        },\r\n                        {\r\n                            id: 3,\r\n                            name: 'Ảnh',\r\n                            value: 'image'\r\n                        }\r\n                        ]}\r\n                        active={view}\r\n                        setActive={(value) => dispatch(setViewRightContent(value))}\r\n                    />\r\n                    {view === 'exam' && <ExamView />}\r\n                    {view === 'question' && <QuestionView />}\r\n                    {view === 'image' && <ImageView />}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\n\r\nexport default RightContent;"], "mappings": ";;;;;;;;;AAAA;AACA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,GAAG,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAEC,SAAS,QAAQ,cAAc;AACpE,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,KAAKC,aAAa,MAAM,kCAAkC;AACjE,OAAOC,kBAAkB,MAAM,8BAA8B;AAC7D,SAASC,YAAY,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,mBAAmB,QAAQ,mCAAmC;AAC9H,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,uBAAuB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/D,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAU,CAAC,GAAGzB,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAE3D,oBACIP,OAAA,CAAAE,SAAA;IAAAM,QAAA,EACKH,SAAS,gBACNL,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,eAChBR,OAAA;QAAKU,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACP,SAAS,CAAE;QAACQ,GAAG,EAAC,MAAM;QAACJ,SAAS,EAAC;MAAuB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxF,CAAC,gBAENjB,OAAA;MAAKS,SAAS,EAAC,gCAAgC;MAAAD,QAAA,gBAC3CR,OAAA,CAAChB,KAAK;QAACyB,SAAS,EAAC;MAAuB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3CjB,OAAA;QAAGS,SAAS,EAAC,uBAAuB;QAAAD,QAAA,EAAC;MAAW;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD;EACR,gBACH,CAAC;AAEX,CAAC;AAAAb,EAAA,CAjBKD,aAAa;EAAA,QACOvB,WAAW;AAAA;AAAAsC,EAAA,GAD/Bf,aAAa;AAmBnB,MAAMgB,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAClB,MAAM;IAAEC;EAAS,CAAC,GAAGzC,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAE1D,oBACIP,OAAA,CAAAE,SAAA;IAAAM,QAAA,EACKa,QAAQ,gBACLrB,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,eAChBR,OAAA,CAACX,SAAS;QAACiC,GAAG,EAAEX,GAAG,CAACC,eAAe,CAACS,QAAQ;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,gBAENjB,OAAA;MAAKS,SAAS,EAAC,gCAAgC;MAAAD,QAAA,gBAC3CR,OAAA,CAACf,IAAI;QAACwB,SAAS,EAAC;MAAuB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1CjB,OAAA;QAAGS,SAAS,EAAC,uBAAuB;QAAAD,QAAA,EAAC;MAAgB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EACR,gBACH,CAAC;AAEX,CAAC;AAAAG,GAAA,CAjBKD,OAAO;EAAA,QACYvC,WAAW;AAAA;AAAA2C,GAAA,GAD9BJ,OAAO;AAmBb,MAAMK,SAAS,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,cAAA;EACpB,MAAM;IAAEC;EAAO,CAAC,GAAG/C,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACqB,MAAM,CAAC;EACvD,MAAM;IAAEC;EAAO,CAAC,GAAGhD,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EACxD,MAAMsB,QAAQ,GAAGhD,WAAW,CAAC,CAAC;EAE9B,oBACImB,OAAA;IAAKS,SAAS,EAAC,iFAAiF;IAAAD,QAAA,gBAC5FR,OAAA;MACI8B,OAAO,EAAEA,CAAA,KAAMD,QAAQ,CAAClC,qBAAqB,CAAC,IAAI,CAAC,CAAE;MACrDc,SAAS,EAAC,yIAAyI;MAAAD,QAAA,EACtJ;IAED;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EACRU,MAAM,aAANA,MAAM,wBAAAD,cAAA,GAANC,MAAM,CAAGC,MAAM,CAAC,cAAAF,cAAA,uBAAhBA,cAAA,CAAkBK,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAChCjC,OAAA;MAAiBS,SAAS,EAAC,4BAA4B;MAAAD,QAAA,gBACnDR,OAAA;QAAOS,SAAS,EAAC,8CAA8C;QAAAD,QAAA,GAAC,mBACnD,EAACyB,KAAK,GAAG,CAAC,EAAC,GACxB;MAAA;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERjB,OAAA;QACIS,SAAS,EAAC,8IACwC;QAClDyB,SAAS;QACTC,WAAW,EAAGC,CAAC,IAAK;UAChBA,CAAC,CAACC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEN,KAAK,CAAC;QAC/C,CAAE;QAAAxB,QAAA,gBAEFR,OAAA;UACIU,GAAG,EAAEsB,KAAM;UACXnB,GAAG,WAAA0B,MAAA,CAAWN,KAAK,CAAG;UACtBxB,SAAS,EAAC;QAAoF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG,CAAC,eAGFjB,OAAA;UAAKS,SAAS,EAAC,iHAAiH;UAAAD,QAAA,eAC5HR,OAAA,CAACd,SAAS;YAACuB,SAAS,EAAC;UAA0D;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,GAvBAgB,KAAK;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAwBV,CACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAAQ,GAAA,CA1CKD,SAAS;EAAA,QACQ5C,WAAW,EACXA,WAAW,EACbC,WAAW;AAAA;AAAA2D,GAAA,GAH1BhB,SAAS;AA6Cf,MAAMiB,QAAQ,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACnB,MAAM;IAAEC,QAAQ;IAAEtC,SAAS;IAAEgB;EAAS,CAAC,GAAGzC,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC/E,MAAM,CAACqC,IAAI,EAAEC,OAAO,CAAC,GAAGzD,QAAQ,CAAC,OAAO,CAAC;EAEzC,oBACIY,OAAA,CAAAE,SAAA;IAAAM,QAAA,gBAEIR,OAAA;MAAK8C,GAAG;MAACrC,SAAS,EAAC,UAAU;MAAAD,QAAA,gBACzBR,OAAA;QAAIS,SAAS,EAAC,sCAAsC;QAAAD,QAAA,EAC/CmC,QAAQ,CAACI,IAAI,IAAI;MAAY;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACLjB,OAAA;QAAKS,SAAS,EAAC,8CAA8C;QAAAD,QAAA,gBACzDR,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC0B,QAAQ,CAACK,UAAU,IAAI,WAAW;QAAA;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1FjB,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC0B,QAAQ,CAACM,KAAK,IAAI,WAAW;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpFjB,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC0B,QAAQ,CAACO,IAAI,IAAI,WAAW;QAAA;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAClF0B,QAAQ,CAACQ,OAAO,iBAAInD,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAO;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC0B,QAAQ,CAACQ,OAAO;QAAA;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC9F0B,QAAQ,CAACS,YAAY,iBAAIpD,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAU;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC0B,QAAQ,CAACS,YAAY,EAAC,GAAC;QAAA;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC5G0B,QAAQ,CAACU,QAAQ,iBAAIrD,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC0B,QAAQ,CAACU,QAAQ,EAAC,GAAC;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC,EAEF0B,QAAQ,CAACW,WAAW,iBAChBtD,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAAD,QAAA,eACjBR,OAAA;UAAGS,SAAS,EAAC,uBAAuB;UAAAD,QAAA,GAAC,2BAAe,EAACmC,QAAQ,CAACW,WAAW;QAAA;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CACR,EAGD0B,QAAQ,CAACY,WAAW,iBAChBvD,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAAD,QAAA,eACjBR,OAAA;UAAGS,SAAS,EAAC,uBAAuB;UAAAD,QAAA,EAAEmC,QAAQ,CAACY;QAAW;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEJ,CAAC,eAGNjB,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,eAChBR,OAAA;QAAKS,SAAS,EAAC,sBAAsB;QAAAD,QAAA,GAChCmC,QAAQ,CAACa,MAAM,iBACZxD,OAAA;UAAMS,SAAS,EAAC,0EAA0E;UAAAD,QAAA,EAAC;QAE3F;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA0B,QAAQ,CAACc,eAAe,iBACrBzD,OAAA;UAAMS,SAAS,EAAC,wEAAwE;UAAAD,QAAA,EAAC;QAEzF;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAC0B,QAAQ,CAACa,MAAM,IAAI,CAACb,QAAQ,CAACc,eAAe,iBAC1CzD,OAAA;UAAMS,SAAS,EAAC,wEAAwE;UAAAD,QAAA,EAAC;QAEzF;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNjB,OAAA,CAACV,WAAW;MACRoE,IAAI,EAAE,CAAC;QACHC,EAAE,EAAE,CAAC;QACLZ,IAAI,EAAE,YAAY;QAClBa,KAAK,EAAE;MACX,CAAC,EACD;QACID,EAAE,EAAE,CAAC;QACLZ,IAAI,EAAE,aAAa;QACnBa,KAAK,EAAE;MACX,CAAC,CACC;MACFC,MAAM,EAAEjB,IAAK;MACbkB,SAAS,EAAEjB;IAAQ;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,EACD2B,IAAI,KAAK,OAAO,iBAAI5C,OAAA,CAACG,aAAa;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACrC2B,IAAI,KAAK,KAAK,iBAAI5C,OAAA,CAACmB,OAAO;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eAEhC,CAAC;AAEX,CAAC;AAAAyB,GAAA,CA3EKD,QAAQ;EAAA,QACgC7D,WAAW;AAAA;AAAAmF,GAAA,GADnDtB,QAAQ;AA4Ed,MAAMuB,kBAAkB,GAAGC,IAAA,IAAsC;EAAA,IAArC;IAAEC,KAAK;IAAEC,KAAK;IAAEC;EAAe,CAAC,GAAAH,IAAA;EACxD,oBACIjE,OAAA;IAAKS,SAAS,EAAC,OAAO;IAAAD,QAAA,gBAClBR,OAAA;MAAKS,SAAS,EAAC,8BAA8B;MAAAD,QAAA,gBACzCR,OAAA,CAACjB,QAAQ;QAAC0B,SAAS,EAAC;MAAuB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9CjB,OAAA;QAAIS,SAAS,EAAC,qCAAqC;QAAAD,QAAA,GAAE0D,KAAK,EAAC,IAAE,EAACC,KAAK,EAAC,GAAC;MAAA;QAAArD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CAAC,EACLkD,KAAK,KAAK,CAAC,iBACRnE,OAAA;MAAKS,SAAS,EAAC,gCAAgC;MAAAD,QAAA,gBAC3CR,OAAA,CAACjB,QAAQ;QAAC0B,SAAS,EAAC;MAAiC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDjB,OAAA;QAAGS,SAAS,EAAC,SAAS;QAAAD,QAAA,EAAE4D;MAAc;QAAAtD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAoD,GAAA,GAfKL,kBAAkB;AAiBxB,MAAMM,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAM;IAAEC,SAAS;IAAEC,IAAI;IAAEC;EAAc,CAAC,GAAG9F,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAChF,MAAM,CAACoE,WAAW,EAAEC,cAAc,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMyF,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMhD,QAAQ,GAAGhD,WAAW,CAAC,CAAC;EAE9BM,SAAS,CAAC,MAAM;IACZyF,cAAc,CAACJ,SAAS,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,CAACC,cAAc,KAAK,IAAI,CAAC,CAAC;EACjF,CAAC,EAAE,CAACT,SAAS,CAAC,CAAC;EAEf,oBACIxE,OAAA,CAAAE,SAAA;IAAAM,QAAA,gBACIR,OAAA,CAACgE,kBAAkB;MAACE,KAAK,EAAC,uCAAqB;MAACC,KAAK,EAAEQ,WAAW,CAACO,MAAO;MAACd,cAAc,EAAC;IAA6B;MAAAtD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1HjB,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,EACfmE,WAAW,CAAC5C,GAAG,CAAC,CAACoD,QAAQ,EAAElD,KAAK,kBAC7BjC,OAAA;QACgBS,SAAS,cAAA8B,MAAA,CAAckC,IAAI,KAAK,CAAC,IAAIC,aAAa,KAAKzC,KAAK,GAAG,8CAA8C,GAAG,8CAA8C,OAAAM,MAAA,CAAIkC,IAAI,KAAK,CAAC,GAAG,gBAAgB,GAAG,EAAE,CAAG;QACnN3C,OAAO,EAAEA,CAAA,KAAM2C,IAAI,KAAK,CAAC,IAAI5C,QAAQ,CAACnC,gBAAgB,CAACuC,KAAK,CAAC,CAAE;QAAAzB,QAAA,gBAE/DR,OAAA;UAAIS,SAAS,EAAC,mBAAmB;UAAAD,QAAA,GAAC,kBAAQ,EAACyB,KAAK,GAAG,CAAC,eAChDjC,OAAA;YAAMS,SAAS,EAAC,2BAA2B;YAAAD,QAAA,GAAC,IAAE,EAAC2E,QAAQ,CAACH,YAAY,CAAC/B,KAAK,IAAI,WAAW,EAAC,KAAG,EAACkC,QAAQ,CAACH,YAAY,CAAC7B,OAAO,IAAI,WAAW,EAAC,KAAG,EAACgC,QAAQ,CAACH,YAAY,CAACI,UAAU,IAAI,WAAW,EAAC,GAAC;UAAA;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvM,CAAC,eACLjB,OAAA,CAACH,aAAa;UAACwF,IAAI,EAAEF,QAAQ,CAACH,YAAY,CAACM,OAAQ;UAAC7E,SAAS,EAAC;QAA4B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7FjB,OAAA;UAAKS,SAAS,EAAC,0BAA0B;UAAAD,QAAA,EACpC2E,QAAQ,CAACI,UAAU,CAACxD,GAAG,CAAC,CAACyD,SAAS,EAAEvD,KAAK,kBACtCjC,OAAA;YAAiBS,SAAS,EAAC,yBAAyB;YAAAD,QAAA,gBAChDR,OAAA;cAAGS,SAAS,EAAC,qCAAqC;cAAAD,QAAA,EAC7CqE,QAAQ,CAAC5C,KAAK;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACJjB,OAAA,CAACH,aAAa;cAACwF,IAAI,EAAEG,SAAS,CAACF,OAAQ;cAAC7E,SAAS,gCAAA8B,MAAA,CAAgCiD,SAAS,CAACC,SAAS,GAAG,gBAAgB,GAAG,EAAE;YAAG;cAAA3E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAJ5HgB,KAAK;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKV,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLkE,QAAQ,CAACH,YAAY,CAACU,QAAQ,iBAC3B1F,OAAA;UAAKS,SAAS,EAAC,MAAM;UAAAD,QAAA,gBACjBR,OAAA;YAAIS,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/CjB,OAAA,CAACF,uBAAuB;YAACwF,OAAO,EAAEH,QAAQ,CAACH,YAAY,CAACU,QAAS;YAACjF,SAAS,EAAC,SAAS;YAACkF,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpH,CACR;MAAA,GAtBIgB,KAAK;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuBT,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAsD,GAAA,CA5CKD,cAAc;EAAA,QAC2B1F,WAAW,EAGrCC,WAAW;AAAA;AAAAgH,GAAA,GAJ1BvB,cAAc;AA8CpB,MAAMwB,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAM;IAAEvB,SAAS;IAAEC,IAAI;IAAEC;EAAc,CAAC,GAAG9F,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAChF,MAAM,CAACyF,WAAW,EAAEC,cAAc,CAAC,GAAG7G,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM8G,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMrE,QAAQ,GAAGhD,WAAW,CAAC,CAAC;EAE9BM,SAAS,CAAC,MAAM;IACZ8G,cAAc,CAACzB,SAAS,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,CAACC,cAAc,KAAK,IAAI,CAAC,CAAC;EACjF,CAAC,EAAE,CAACT,SAAS,CAAC,CAAC;EAEf,oBACIxE,OAAA,CAAAE,SAAA;IAAAM,QAAA,gBACIR,OAAA,CAACgE,kBAAkB;MAACE,KAAK,EAAC,kCAAkB;MAACC,KAAK,EAAE6B,WAAW,CAACd,MAAO;MAACd,cAAc,EAAC;IAA0B;MAAAtD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpHjB,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,EACfwF,WAAW,CAACjE,GAAG,CAAC,CAACoD,QAAQ,EAAElD,KAAK,kBAC7BjC,OAAA;QACgBS,SAAS,cAAA8B,MAAA,CAAckC,IAAI,KAAK,CAAC,IAAIC,aAAa,KAAKzC,KAAK,GAAG,8CAA8C,GAAG,8CAA8C,OAAAM,MAAA,CAAIkC,IAAI,KAAK,CAAC,GAAG,gBAAgB,GAAG,EAAE,CAAG;QACnN3C,OAAO,EAAEA,CAAA,KAAM2C,IAAI,KAAK,CAAC,IAAI5C,QAAQ,CAACnC,gBAAgB,CAACuC,KAAK,CAAC,CAAE;QAAAzB,QAAA,gBAE/DR,OAAA;UAAIS,SAAS,EAAC,mBAAmB;UAAAD,QAAA,GAAC,kBAAQ,EAACyB,KAAK,GAAG,CAAC,eAChDjC,OAAA;YAAMS,SAAS,EAAC,2BAA2B;YAAAD,QAAA,GAAC,IAAE,EAAC2E,QAAQ,CAACH,YAAY,CAAC/B,KAAK,IAAI,WAAW,EAAC,KAAG,EAACkC,QAAQ,CAACH,YAAY,CAAC7B,OAAO,IAAI,WAAW,EAAC,KAAG,EAACgC,QAAQ,CAACH,YAAY,CAACI,UAAU,IAAI,WAAW,EAAC,GAAC;UAAA;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvM,CAAC,eACLjB,OAAA,CAACH,aAAa;UAACwF,IAAI,EAAEF,QAAQ,CAACH,YAAY,CAACM,OAAQ;UAAC7E,SAAS,EAAC;QAA4B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7FjB,OAAA;UAAKS,SAAS,EAAC,0BAA0B;UAAAD,QAAA,EACpC2E,QAAQ,CAACI,UAAU,CAACxD,GAAG,CAAC,CAACyD,SAAS,EAAEvD,KAAK,kBACtCjC,OAAA;YAAiBS,SAAS,EAAC,yBAAyB;YAAAD,QAAA,gBAChDR,OAAA;cAAGS,SAAS,EAAC,qCAAqC;cAAAD,QAAA,EAC7C0F,QAAQ,CAACjE,KAAK;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACJjB,OAAA,CAACH,aAAa;cAACwF,IAAI,EAAEG,SAAS,CAACF,OAAQ;cAAC7E,SAAS,gCAAA8B,MAAA,CAAgCiD,SAAS,CAACC,SAAS,GAAG,gBAAgB,GAAG,cAAc;YAAG;cAAA3E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAJxIgB,KAAK;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKV,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLkE,QAAQ,CAACH,YAAY,CAACU,QAAQ,iBAC3B1F,OAAA;UAAKS,SAAS,EAAC,MAAM;UAAAD,QAAA,gBACjBR,OAAA;YAAIS,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/CjB,OAAA,CAACF,uBAAuB;YAACwF,OAAO,EAAEH,QAAQ,CAACH,YAAY,CAACU,QAAS;YAACjF,SAAS,EAAC,SAAS;YAACkF,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAU;UAAE;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvH,CACR;MAAA,GAtBIgB,KAAK;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuBT,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAA8E,GAAA,CA5CKD,cAAc;EAAA,QAC2BlH,WAAW,EAGrCC,WAAW;AAAA;AAAAsH,GAAA,GAJ1BL,cAAc;AA8CpB,MAAMM,eAAe,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC1B,MAAM;IAAE7B,SAAS;IAAEC,IAAI;IAAEC;EAAc,CAAC,GAAG9F,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAChF,MAAMsB,QAAQ,GAAGhD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACyH,YAAY,EAAEC,eAAe,CAAC,GAAGnH,QAAQ,CAAC,EAAE,CAAC;EAEpDD,SAAS,CAAC,MAAM;IACZoH,eAAe,CAAC/B,SAAS,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,CAACC,cAAc,KAAK,KAAK,CAAC,CAAC;EACnF,CAAC,EAAE,CAACT,SAAS,CAAC,CAAC;EAEf,oBACIxE,OAAA,CAAAE,SAAA;IAAAM,QAAA,gBACIR,OAAA,CAACgE,kBAAkB;MAACE,KAAK,EAAC,6CAAsB;MAACC,KAAK,EAAEmC,YAAY,CAACpB,MAAO;MAACd,cAAc,EAAC;IAA8B;MAAAtD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7HjB,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,EACf8F,YAAY,CAACvE,GAAG,CAAC,CAACoD,QAAQ,EAAElD,KAAK,kBAC9BjC,OAAA;QACgBS,SAAS,cAAA8B,MAAA,CAAckC,IAAI,KAAK,CAAC,IAAIC,aAAa,KAAKzC,KAAK,GAAG,8CAA8C,GAAG,8CAA8C,OAAAM,MAAA,CAAIkC,IAAI,KAAK,CAAC,GAAG,gBAAgB,GAAG,EAAE,CAAG;QACnN3C,OAAO,EAAEA,CAAA,KAAM2C,IAAI,KAAK,CAAC,IAAI5C,QAAQ,CAACnC,gBAAgB,CAACuC,KAAK,CAAC,CAAE;QAAAzB,QAAA,gBAE/DR,OAAA;UAAIS,SAAS,EAAC,mBAAmB;UAAAD,QAAA,GAAC,kBAAQ,EAACyB,KAAK,GAAG,CAAC,eAChDjC,OAAA;YAAMS,SAAS,EAAC,2BAA2B;YAAAD,QAAA,GAAC,IAAE,EAAC2E,QAAQ,CAACH,YAAY,CAAC/B,KAAK,IAAI,WAAW,EAAC,KAAG,EAACkC,QAAQ,CAACH,YAAY,CAAC7B,OAAO,IAAI,WAAW,EAAC,KAAG,EAACgC,QAAQ,CAACH,YAAY,CAACI,UAAU,IAAI,WAAW,EAAC,GAAC;UAAA;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvM,CAAC,eACLjB,OAAA,CAACH,aAAa;UAACwF,IAAI,EAAEF,QAAQ,CAACH,YAAY,CAACM,OAAQ;UAAC7E,SAAS,EAAC;QAA4B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7FjB,OAAA;UAAGS,SAAS,EAAC,wBAAwB;UAAAD,QAAA,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjDjB,OAAA,CAACH,aAAa;UAACwF,IAAI,EAAEF,QAAQ,CAACH,YAAY,CAACwB,aAAc;UAAC/F,SAAS,EAAC;QAA4B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAClGkE,QAAQ,CAACH,YAAY,CAACU,QAAQ,iBAC3B1F,OAAA;UAAKS,SAAS,EAAC,MAAM;UAAAD,QAAA,gBACjBR,OAAA;YAAIS,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/CjB,OAAA,CAACF,uBAAuB;YAACwF,OAAO,EAAEH,QAAQ,CAACH,YAAY,CAACU,QAAS;YAACjF,SAAS,EAAC,SAAS;YAACkF,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAU;UAAE;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvH,CACR;MAAA,GAdIgB,KAAK;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeT,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAoF,GAAA,CAnCKD,eAAe;EAAA,QAC0BxH,WAAW,EACrCC,WAAW;AAAA;AAAA4H,GAAA,GAF1BL,eAAe;AAqCrB,MAAMM,YAAY,GAAGA,CAAA,KAAM;EAEvB,oBACI1G,OAAA,CAAAE,SAAA;IAAAM,QAAA,gBACIR,OAAA,CAACsE,cAAc;MAAAxD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClBjB,OAAA;MAAIS,SAAS,EAAC;IAAiB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClCjB,OAAA,CAAC8F,cAAc;MAAAhF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClBjB,OAAA;MAAIS,SAAS,EAAC;IAAiB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClCjB,OAAA,CAACoG,eAAe;MAAAtF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACrB,CAAC;AAEX,CAAC;AAAA0F,GAAA,GAXKD,YAAY;AAalB,MAAME,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAM;IAAElE,QAAQ;IAAEC;EAAK,CAAC,GAAGhE,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAChE,MAAMsB,QAAQ,GAAGhD,WAAW,CAAC,CAAC;EAE9B,oBACImB,OAAA;IAAKS,SAAS,EAAC,iDAAiD;IAAAD,QAAA,gBAE5DR,OAAA;MAAKS,SAAS,EAAC,kFAAkF;MAAAD,QAAA,eAC7FR,OAAA;QAAKS,SAAS,EAAC,yBAAyB;QAAAD,QAAA,gBACpCR,OAAA,CAAClB,GAAG;UAAC2B,SAAS,EAAC;QAAuB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCjB,OAAA;UAAIS,SAAS,EAAC,qCAAqC;UAAAD,QAAA,EAAC;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNjB,OAAA;MAAKS,SAAS,EAAC,4BAA4B;MAAAD,QAAA,eACvCR,OAAA;QAAKS,SAAS,EAAC,0CAA0C;QAAAD,QAAA,gBACrDR,OAAA,CAACV,WAAW;UACRoE,IAAI,EAAE,CAAC;YACHC,EAAE,EAAE,CAAC;YACLZ,IAAI,EAAE,QAAQ;YACda,KAAK,EAAE;UACX,CAAC,EACD;YACID,EAAE,EAAE,CAAC;YACLZ,IAAI,EAAE,SAAS;YACfa,KAAK,EAAE;UACX,CAAC,EACD;YACID,EAAE,EAAE,CAAC;YACLZ,IAAI,EAAE,KAAK;YACXa,KAAK,EAAE;UACX,CAAC,CACC;UACFC,MAAM,EAAEjB,IAAK;UACbkB,SAAS,EAAGF,KAAK,IAAK/B,QAAQ,CAACjC,mBAAmB,CAACgE,KAAK,CAAC;QAAE;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,EACD2B,IAAI,KAAK,MAAM,iBAAI5C,OAAA,CAACyC,QAAQ;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC/B2B,IAAI,KAAK,UAAU,iBAAI5C,OAAA,CAAC0G,YAAY;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACvC2B,IAAI,KAAK,OAAO,iBAAI5C,OAAA,CAACwB,SAAS;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC4F,GAAA,CA5CID,YAAY;EAAA,QACahI,WAAW,EACrBC,WAAW;AAAA;AAAAiI,IAAA,GAF1BF,YAAY;AA+ClB,eAAeA,YAAY;AAAC,IAAA1F,EAAA,EAAAK,GAAA,EAAAiB,GAAA,EAAAuB,GAAA,EAAAM,GAAA,EAAAwB,GAAA,EAAAM,GAAA,EAAAM,GAAA,EAAAE,GAAA,EAAAG,IAAA;AAAAC,YAAA,CAAA7F,EAAA;AAAA6F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAAhD,GAAA;AAAAgD,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}