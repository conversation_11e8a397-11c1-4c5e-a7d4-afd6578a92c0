import * as z from "zod";
import { OpenEnum } from "../../types/enums.js";
export declare const SampleType: {
    readonly Pretrain: "pretrain";
    readonly Instruct: "instruct";
    readonly BatchRequest: "batch_request";
    readonly BatchResult: "batch_result";
    readonly BatchError: "batch_error";
};
export type SampleType = OpenEnum<typeof SampleType>;
/** @internal */
export declare const SampleType$inboundSchema: z.ZodType<SampleType, z.ZodTypeDef, unknown>;
/** @internal */
export declare const SampleType$outboundSchema: z.ZodType<SampleType, z.ZodTypeDef, SampleType>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace SampleType$ {
    /** @deprecated use `SampleType$inboundSchema` instead. */
    const inboundSchema: z.ZodType<SampleType, z.ZodTypeDef, unknown>;
    /** @deprecated use `SampleType$outboundSchema` instead. */
    const outboundSchema: z.ZodType<SampleType, z.ZodTypeDef, SampleType>;
}
//# sourceMappingURL=sampletype.d.ts.map