import { useEffect, useRef, useState } from "react";
import { Info } from "lucide-react";

const TextArea = ({
    value,
    onChange,
    placeholder,
    label,
    Icon = null,
    hint = "", // nội dung tooltip
    buttonFilterText = false
}) => {
    const textAreaRef = useRef(null);
    const [showTooltip, setShowTooltip] = useState(false);

    useEffect(() => {
        if (textAreaRef.current) {
            textAreaRef.current.style.height = "auto";
            textAreaRef.current.style.height = textAreaRef.current.scrollHeight + "px";
        }
    }, [value]);

    return (
        <div className="flex flex-col gap-2 relative w-full">
            <div className="flex flex-row justify-between items-center">
                <label className="text-xs font-medium text-gray-700 flex items-center gap-1 relative">
                    {Icon && <Icon className="w-3 h-3 inline" />}
                    {label}
                    {hint && (
                        <div
                            className="relative flex items-center"
                            onMouseEnter={() => setShowTooltip(true)}
                            onMouseLeave={() => setShowTooltip(false)}
                        >
                            <Info className="w-3 h-3 text-gray-400 cursor-pointer" />
                            {showTooltip && (
                                <div className="absolute top-6 left-0 z-50 w-64 p-2 text-xs text-white bg-gray-800 rounded shadow-md">
                                    {hint}
                                </div>
                            )}
                        </div>
                    )}
                </label>
                {buttonFilterText && (
                    <button
                        onClick={buttonFilterText.onClick}
                        className="text-xs font-medium text-white hover:underline px-2 py-1 bg-blue-500 rounded"
                    >
                        {buttonFilterText.text}
                    </button>
                )}
            </div>
            <textarea
                ref={textAreaRef}
                value={value}
                onChange={onChange}
                className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none overflow-hidden"
                placeholder={placeholder}
                rows={1}
            />
        </div>
    );
};

export default TextArea;
