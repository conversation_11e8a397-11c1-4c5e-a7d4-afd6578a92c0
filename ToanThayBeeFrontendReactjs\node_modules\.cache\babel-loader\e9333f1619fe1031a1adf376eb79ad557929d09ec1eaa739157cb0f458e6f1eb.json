{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$(),\n  _s7 = $RefreshSig$(),\n  _s8 = $RefreshSig$();\n// Optimized Form Panel Component\nimport { useEffect, useState, useRef } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { setExamData, postExam, nextStep, prevStep, setExamImage, setExamFile, setQuestionTNContent, setQuestionDSContent, setQuestionTLNContent, setCorrectAnswerTN, setCorrectAnswerDS, setCorrectAnswerTLN, setQuestions, setSelectedIndex, setOcrFile, setMarkDownExam, setBase64Images } from \"src/features/addExam/addExamSlice\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport CompactStepHeader from \"./CompactStepHeader\";\nimport { Clock, Users, BookOpen, Image as ImageIcon, Upload, FileText, CheckCircle, ChevronRight, ChevronLeft, Plus, Save, Trash2, Info } from \"lucide-react\";\nimport ImageUpload from \"src/components/image/UploadImage\";\nimport UploadPdf from \"src/components/UploadPdf\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport NavigateBar from \"./NavigateBar\";\nimport SolutionEditor from \"./SolutionEditor\";\nimport { normalizeText, validateExamData, splitMarkdownToParts } from \"src/utils/question/questionUtils\";\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\nimport TextArea from \"src/components/input/TextArea\";\nimport PdfViewer from \"../ViewPdf\";\nimport { ocrPdfWithMistral } from \"src/features/addExam/addExamSlice\";\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport { uploadBase64Images } from \"src/features/image/imageSlice\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Step1Form = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    examData,\n    examImage,\n    examFile\n  } = useSelector(state => state.addExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const updateExamData = (field, value) => {\n    dispatch(setExamData({\n      field,\n      value\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [\"T\\xEAn \\u0111\\u1EC1 thi \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 36\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: examData.name || '',\n          onChange: e => updateExamData('name', e.target.value),\n          className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n          placeholder: \"Nh\\u1EADp t\\xEAn \\u0111\\u1EC1 thi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [\"Ki\\u1EC3u \\u0111\\u1EC1 \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: examData.typeOfExam,\n          onChange: option => updateExamData('typeOfExam', option),\n          options: Array.isArray(codes[\"exam type\"]) ? codes[\"exam type\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [\"L\\u1EDBp \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: examData.class,\n          onChange: option => updateExamData('class', option),\n          options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [\"N\\u0103m \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: examData.year,\n          onChange: option => updateExamData('year', option),\n          options: Array.isArray(codes[\"year\"]) ? codes[\"year\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(Clock, {\n            className: \"w-3 h-3 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 25\n          }, this), \"Th\\u1EDDi gian (ph\\xFAt)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          value: examData.testDuration || '',\n          onChange: e => updateExamData('testDuration', e.target.value),\n          className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n          placeholder: \"90\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(Users, {\n            className: \"w-3 h-3 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 25\n          }, this), \"\\u0110i\\u1EC3m \\u0111\\u1EA1t (%)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          value: examData.passRate || '',\n          onChange: e => updateExamData('passRate', e.target.value),\n          className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n          placeholder: \"50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this), examData.typeOfExam === \"OT\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-xs font-medium text-gray-700 mb-1\",\n        children: [/*#__PURE__*/_jsxDEV(BookOpen, {\n          className: \"w-3 h-3 inline mr-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 25\n        }, this), \"Ch\\u01B0\\u01A1ng\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n        selectedOption: examData.chapter,\n        onChange: option => updateExamData('chapter', option),\n        options: Array.isArray(codes[\"chapter\"]) ? codes[\"chapter\"] : [],\n        className: \"text-xs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-xs font-medium text-gray-700 mb-1\",\n        children: \"Link l\\u1EDDi gi\\u1EA3i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        value: examData.solutionUrl,\n        onChange: e => updateExamData('solutionUrl', e.target.value),\n        className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n        placeholder: \"Nh\\u1EADp link l\\u1EDDi gi\\u1EA3i vd: youtube, ...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-xs font-medium text-gray-700 mb-1\",\n        children: \"M\\xF4 t\\u1EA3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        value: examData.description || '',\n        onChange: e => updateExamData('description', e.target.value),\n        rows: 2,\n        className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n        placeholder: \"M\\xF4 t\\u1EA3 ng\\u1EAFn v\\u1EC1 \\u0111\\u1EC1 thi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"flex items-center text-xs\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: examData.public || false,\n          onChange: e => updateExamData('public', e.target.checked),\n          className: \"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-1 text-gray-700\",\n          children: \"C\\xF4ng khai\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"flex items-center text-xs\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: examData.isClassroomExam || false,\n          onChange: e => updateExamData('isClassroomExam', e.target.checked),\n          className: \"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-1 text-gray-700\",\n          children: \"\\u0110\\u1EC1 thi l\\u1EDBp\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(ImageIcon, {\n            className: \"w-3 h-3 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 25\n          }, this), \"\\u1EA2nh \\u0111\\u1EC1 thi\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ImageUpload, {\n          image: examImage,\n          setImage: img => dispatch(setExamImage(img)),\n          inputId: \"exam-image-compact\",\n          compact: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(Upload, {\n            className: \"w-3 h-3 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 25\n          }, this), \"File PDF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(UploadPdf, {\n          setPdf: pdf => dispatch(setExamFile(pdf)),\n          deleteButton: false,\n          compact: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 9\n  }, this);\n};\n_s(Step1Form, \"P4fhebAumt8V+6XTy3zQSZoxQZU=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = Step1Form;\nconst ButtonAddQuestion = _ref => {\n  let {\n    text,\n    onClick\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: onClick,\n    className: \"w-full px-3 py-2 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors text-xs\",\n    children: [/*#__PURE__*/_jsxDEV(Plus, {\n      className: \"w-3 h-3 inline mr-1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 13\n    }, this), text]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 9\n  }, this);\n};\n_c2 = ButtonAddQuestion;\nconst AddQuestionForm = _ref2 => {\n  let {\n    questionContent,\n    correctAnswerContent,\n    handleContentChange,\n    handleCorrectAnswerChange,\n    hintAnswer,\n    hintContent\n  } = _ref2;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-3 flex flex-col gap-4\",\n    children: [/*#__PURE__*/_jsxDEV(TextArea, {\n      value: correctAnswerContent,\n      onChange: handleCorrectAnswerChange,\n      placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n      label: \"\\u0110\\xE1p \\xE1n\",\n      Icon: CheckCircle,\n      hint: hintAnswer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n      value: questionContent,\n      onChange: handleContentChange,\n      placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n      label: \"C\\xE2u h\\u1ECFi\",\n      Icon: Plus,\n      hint: hintContent,\n      buttonFilterText: {\n        text: \"Lọc\",\n        onClick: () => {\n          handleContentChange({\n            target: {\n              value: normalizeText(questionContent)\n            }\n          });\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 238,\n    columnNumber: 9\n  }, this);\n};\n_c3 = AddQuestionForm;\nconst AddTNQuestion = () => {\n  _s2();\n  const dispatch = useDispatch();\n  const {\n    questionTNContent,\n    correctAnswerTN\n  } = useSelector(state => state.addExam);\n  const handleContentChange = e => {\n    dispatch(setQuestionTNContent(e.target.value));\n  };\n  const handleCorrectAnswerChange = e => {\n    dispatch(setCorrectAnswerTN(e.target.value));\n  };\n  return /*#__PURE__*/_jsxDEV(AddQuestionForm, {\n    questionContent: questionTNContent,\n    correctAnswerContent: correctAnswerTN,\n    handleContentChange: handleContentChange,\n    handleCorrectAnswerChange: handleCorrectAnswerChange,\n    hintAnswer: \"\\u0110\\xE1p \\xE1n tr\\u1EAFc nghi\\u1EC7m: A b c D ...\",\n    hintContent: \"N\\u1ED9i dung tr\\u1EAFc nghi\\u1EC7m: Paste c\\u1EA3 c\\xE2u h\\u1ECFi m\\u1EC7nh \\u0111\\u1EC1 v\\xE0 l\\u1EDDi gi\\u1EA3i\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 283,\n    columnNumber: 9\n  }, this);\n};\n_s2(AddTNQuestion, \"TUrm8UjG+jst7xNoz/PmxzyVdbI=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c4 = AddTNQuestion;\nconst AddDSQuestion = () => {\n  _s3();\n  const dispatch = useDispatch();\n  const {\n    questionDSContent,\n    correctAnswerDS\n  } = useSelector(state => state.addExam);\n  const handleContentChange = e => {\n    dispatch(setQuestionDSContent(e.target.value));\n  };\n  const handleCorrectAnswerChange = e => {\n    dispatch(setCorrectAnswerDS(e.target.value));\n  };\n  return /*#__PURE__*/_jsxDEV(AddQuestionForm, {\n    questionContent: questionDSContent,\n    correctAnswerContent: correctAnswerDS,\n    handleContentChange: handleContentChange,\n    handleCorrectAnswerChange: handleCorrectAnswerChange,\n    hintAnswer: \"\\u0110\\xE1p \\xE1n \\u0111\\xFAng sai: \\u0110\\u0110SS dsss DSDS ...\",\n    hintContent: \"N\\u1ED9i dung c\\xE2u h\\u1ECFi: Paste c\\u1EA3 c\\xE2u h\\u1ECFi m\\u1EC7nh \\u0111\\u1EC1 v\\xE0 l\\u1EDDi gi\\u1EA3i\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 307,\n    columnNumber: 9\n  }, this);\n};\n_s3(AddDSQuestion, \"OhFBIlLHYM7nXLOS6xnOobx4j7k=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c5 = AddDSQuestion;\nconst AddTLNQuestion = () => {\n  _s4();\n  const dispatch = useDispatch();\n  const {\n    questionTLNContent,\n    correctAnswerTLN\n  } = useSelector(state => state.addExam);\n  const handleContentChange = e => {\n    dispatch(setQuestionTLNContent(e.target.value));\n  };\n  const handleCorrectAnswerChange = e => {\n    dispatch(setCorrectAnswerTLN(e.target.value));\n  };\n  return /*#__PURE__*/_jsxDEV(AddQuestionForm, {\n    questionContent: questionTLNContent,\n    correctAnswerContent: correctAnswerTLN,\n    handleContentChange: handleContentChange,\n    handleCorrectAnswerChange: handleCorrectAnswerChange,\n    hintAnswer: \"\\u0110\\xE1p \\xE1n tr\\u1EA3 l\\u1EDDi ng\\u1EAFn: 3,14 1.5 3,2\",\n    hintContent: \"N\\u1ED9i dung c\\xE2u h\\u1ECFi: Paste c\\u1EA3 c\\xE2u h\\u1ECFi m\\u1EC7nh \\u0111\\u1EC1 v\\xE0 l\\u1EDDi gi\\u1EA3i\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 331,\n    columnNumber: 9\n  }, this);\n};\n_s4(AddTLNQuestion, \"2wyCIdvmC1fR2wIxMXSCGwa2CEM=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c6 = AddTLNQuestion;\nconst convertFileToBase64 = file => {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onloadend = () => resolve(reader.result);\n    reader.onerror = reject;\n    reader.readAsDataURL(file);\n  });\n};\nconst Step2Form = () => {\n  _s5();\n  const dispatch = useDispatch();\n  const {\n    ocrFile,\n    markDownExam,\n    loadingOcr,\n    base64Images\n  } = useSelector(state => state.addExam);\n  const fileInputRef = useRef(null);\n  const handleFileChange = file => {\n    dispatch(setOcrFile(file));\n  };\n  const handleAddImages = async event => {\n    const files = event.target.files;\n    if (!files || files.length === 0) return;\n    const base64List = await Promise.all(Array.from(files).map(file => convertFileToBase64(file)));\n    dispatch(setBase64Images([...base64Images, ...base64List]));\n  };\n  const handleOcr = () => {\n    dispatch(ocrPdfWithMistral(ocrFile));\n  };\n  const handleContentChange = e => {\n    dispatch(setMarkDownExam(e.target.value));\n  };\n  const handleRemoveImage = indexToRemove => {\n    const newImages = base64Images.filter((_, i) => i !== indexToRemove);\n    dispatch(setBase64Images(newImages));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-3 flex flex-col gap-4\",\n    children: [/*#__PURE__*/_jsxDEV(UploadPdf, {\n      setPdf: handleFileChange,\n      deleteButton: false,\n      compact: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleOcr,\n      disabled: loadingOcr || !ocrFile,\n      className: \"flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-xs disabled:opacity-50 disabled:cursor-not-allowed\",\n      children: loadingOcr ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 25\n        }, this), \"\\u0110ang x\\u1EED l\\xFD...\"]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 25\n        }, this), \"X\\u1EED l\\xFD \\u0111\\u1EC1 thi\"]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 13\n    }, this), base64Images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          multiple: true,\n          accept: \"image/*\",\n          ref: fileInputRef,\n          style: {\n            display: 'none'\n          },\n          onChange: handleAddImages\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => fileInputRef.current.click(),\n          className: \"text-xs border-dashed border-2 hover:bg-gray-100 border-gray-300 hover:shadow-lg rounded-lg px-3 py-1 flex flex-col items-center justify-center\",\n          children: \"Th\\xEAm \\u1EA3nh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 25\n        }, this), base64Images.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative group\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleRemoveImage(index),\n            className: \"h-4 w-4 absolute top-0 right-0 bg-red-600 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity z-10\",\n            title: \"Xo\\xE1 \\u1EA3nh\",\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            src: image,\n            alt: \"Image \".concat(index),\n            className: \"w-24 h-24 object-contain rounded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 33\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 29\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 17\n    }, this), markDownExam && /*#__PURE__*/_jsxDEV(TextArea, {\n      value: markDownExam,\n      onChange: handleContentChange,\n      placeholder: \"N\\u1ED9i dung \\u0111\\u1EC1 thi\",\n      label: \"N\\u1ED9i dung \\u0111\\u1EC1 thi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 387,\n    columnNumber: 9\n  }, this);\n};\n_s5(Step2Form, \"v8DN1y4GFB+TbPpsI0UGvUDfEX4=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c7 = Step2Form;\nconst Step3Form = () => {\n  _s6();\n  const [isViewAdd, setIsViewAdd] = useState(true);\n  const [view, setView] = useState('TN');\n  const {\n    markDownExam,\n    questionTNContent,\n    questionDSContent,\n    questionTLNContent,\n    correctAnswerTN,\n    correctAnswerDS,\n    correctAnswerTLN\n  } = useSelector(state => state.addExam);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    if (!isViewAdd) return;\n    if (questionTNContent.trim() !== \"\" || correctAnswerTN.trim() !== \"\") {\n      setIsViewAdd(false);\n      setView('TN');\n    } else if (questionDSContent.trim() !== \"\" || correctAnswerDS.trim() !== \"\") {\n      setIsViewAdd(false);\n      setView('DS');\n    } else if (questionTLNContent.trim() !== \"\" || correctAnswerTLN.trim() !== \"\") {\n      setIsViewAdd(false);\n      setView('TLN');\n    }\n  }, [questionTNContent, correctAnswerTN, questionDSContent, correctAnswerDS, questionTLNContent, correctAnswerTLN]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3\",\n    children: [isViewAdd ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-4 px-3\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-8 h-8 mx-auto text-gray-400 mb-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-medium text-gray-900 mb-1\",\n        children: \"Th\\xEAm c\\xE2u h\\u1ECFi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-600 mb-3\",\n        children: \"T\\u1EA1o c\\xE2u h\\u1ECFi cho \\u0111\\u1EC1 thi c\\u1EE7a b\\u1EA1n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(ButtonAddQuestion, {\n          text: \"Th\\xEAm c\\xE2u tr\\u1EAFc nghi\\u1EC7m\",\n          onClick: () => {\n            setIsViewAdd(false);\n            setView('TN');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(ButtonAddQuestion, {\n          text: \"Th\\xEAm c\\xE2u \\u0111\\xFAng sai\",\n          onClick: () => {\n            setIsViewAdd(false);\n            setView('DS');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(ButtonAddQuestion, {\n          text: \"Th\\xEAm c\\xE2u tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\",\n          onClick: () => {\n            setIsViewAdd(false);\n            setView('TLN');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 486,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(NavigateBar, {\n      list: [{\n        id: 1,\n        name: 'Trắc nghiệm',\n        value: 'TN'\n      }, {\n        id: 2,\n        name: 'Đúng sai',\n        value: 'DS'\n      }, {\n        id: 3,\n        name: 'Trả lời ngắn',\n        value: 'TLN'\n      }],\n      active: view,\n      setActive: setView\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 17\n    }, this), view === 'TN' && !isViewAdd && /*#__PURE__*/_jsxDEV(AddTNQuestion, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 17\n    }, this), view === 'DS' && !isViewAdd && /*#__PURE__*/_jsxDEV(AddDSQuestion, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 543,\n      columnNumber: 17\n    }, this), view === 'TLN' && !isViewAdd && /*#__PURE__*/_jsxDEV(AddTLNQuestion, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 484,\n    columnNumber: 9\n  }, this);\n};\n_s6(Step3Form, \"r8vKu7qMQXpz+y2GBB/W7oUlP0w=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c8 = Step3Form;\nconst ListQuestions = _ref3 => {\n  let {\n    count,\n    title,\n    onClick,\n    i\n  } = _ref3;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex  flex-row w-full justify-start items-center border-b border-[#e3e4e5] pb-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#090a0a] text-xs font-bold font-bevietnam leading-loose whitespace-nowrap mr-4 flex-shrink-0 min-w-[6.2rem]\",\n      children: [title, \":\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-row gap-5 w-full overflow-x-auto min-h-max \",\n      children: Array.from({\n        length: count\n      }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: () => onClick(index),\n        className: \"cursor-pointer border text-xs border-[#e3e4e5] rounded flex justify-center whitespace-nowrap items-center gap-2.5 \".concat(i === index ? 'bg-[#253f61] text-white' : 'bg-white text-[#253f61]', \" px-2 py-1\"),\n        children: [\"C\\xE2u h\\u1ECFi \", index + 1]\n      }, index + title, true, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 554,\n    columnNumber: 9\n  }, this);\n};\n_c9 = ListQuestions;\nconst Step4Form = () => {\n  _s7();\n  const {\n    questions,\n    selectedIndex,\n    view\n  } = useSelector(state => state.addExam);\n  const dispatch = useDispatch();\n  const [questionCount, setQuestionCount] = useState({\n    TN: 0,\n    DS: 0,\n    TLN: 0\n  });\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const [optionChapter, setOptionChapter] = useState([]);\n  useEffect(() => {\n    if (questions) {\n      const counts = questions.reduce((acc, q) => {\n        const type = q.questionData.typeOfQuestion;\n        if (acc[type] !== undefined) acc[type]++;\n        return acc;\n      }, {\n        TN: 0,\n        DS: 0,\n        TLN: 0\n      });\n      setQuestionCount(counts);\n    }\n  }, [questions]);\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      var _questions$selectedIn, _questions$selectedIn2, _questions$selectedIn3, _questions$selectedIn4;\n      if ((_questions$selectedIn = questions[selectedIndex]) !== null && _questions$selectedIn !== void 0 && (_questions$selectedIn2 = _questions$selectedIn.questionData) !== null && _questions$selectedIn2 !== void 0 && _questions$selectedIn2.class && ((_questions$selectedIn3 = questions[selectedIndex]) === null || _questions$selectedIn3 === void 0 ? void 0 : (_questions$selectedIn4 = _questions$selectedIn3.questionData) === null || _questions$selectedIn4 === void 0 ? void 0 : _questions$selectedIn4.class.trim()) !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => {\n          var _questions$selectedIn5, _questions$selectedIn6;\n          return code.code.startsWith((_questions$selectedIn5 = questions[selectedIndex]) === null || _questions$selectedIn5 === void 0 ? void 0 : (_questions$selectedIn6 = _questions$selectedIn5.questionData) === null || _questions$selectedIn6 === void 0 ? void 0 : _questions$selectedIn6.class) && code.code.length === 5;\n        }));\n      } else {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, questions, selectedIndex]);\n  const handleQuestionChange = (e, field) => {\n    const newQuestions = questions.map((question, qIndex) => {\n      if (qIndex === selectedIndex) {\n        return {\n          ...question,\n          questionData: {\n            ...question.questionData,\n            [field]: e.target.value\n          }\n        };\n      }\n      return question;\n    });\n    dispatch(setQuestions(newQuestions));\n  };\n  const handleSolutionQuestionChange = newSolution => {\n    const newQuestions = questions.map((question, qIndex) => {\n      if (qIndex === selectedIndex) {\n        return {\n          ...question,\n          questionData: {\n            ...question.questionData,\n            solution: newSolution\n          }\n        };\n      }\n      return question;\n    });\n    dispatch(setQuestions(newQuestions));\n  };\n  const handleStatementChange = (index, value, field) => {\n    const newQuestions = questions.map((question, qIndex) => {\n      if (qIndex === selectedIndex) {\n        return {\n          ...question,\n          statements: question.statements.map((stmt, sIndex) => {\n            if (sIndex === index) {\n              return {\n                ...stmt,\n                [field]: value\n              };\n            }\n            return stmt;\n          })\n        };\n      }\n      return question;\n    });\n    dispatch(setQuestions(newQuestions));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3 w-full\",\n    children: [/*#__PURE__*/_jsxDEV(ListQuestions, {\n      count: questionCount.TN,\n      title: 'Trắc nghiệm',\n      onClick: index => dispatch(setSelectedIndex(index)),\n      i: selectedIndex\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 667,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ListQuestions, {\n      count: questionCount.DS,\n      title: 'Đúng sai',\n      onClick: index => dispatch(setSelectedIndex(index + questionCount.TN)),\n      i: selectedIndex - questionCount.TN\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ListQuestions, {\n      count: questionCount.TLN,\n      title: 'Trả lời ngắn',\n      onClick: index => dispatch(setSelectedIndex(index + questionCount.DS + questionCount.TN)),\n      i: selectedIndex - (questionCount.DS + questionCount.TN)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 669,\n      columnNumber: 13\n    }, this), questions && questions[selectedIndex] && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Ph\\xE2n lo\\u1EA1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-row gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: questions[selectedIndex].questionData.class,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'class'),\n            options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n            className: \"text-xs\",\n            placeholder: \"Ch\\u1ECDn l\\u1EDBp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n            selectedOption: questions[selectedIndex].questionData.chapter,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'chapter'),\n            options: optionChapter,\n            className: \"text-xs\",\n            placeholder: \"Ch\\u1ECDn ch\\u01B0\\u01A1ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: questions[selectedIndex].questionData.difficulty,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'difficulty'),\n            options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n            className: \"text-xs\",\n            placeholder: \"Ch\\u1ECDn \\u0111\\u1ED9 kh\\xF3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 673,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \" bg-gray-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 699,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Th\\xF4ng tin c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(TextArea, {\n            value: questions[selectedIndex].questionData.content,\n            onChange: e => handleQuestionChange(e, 'content'),\n            placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n            label: \"C\\xE2u h\\u1ECFi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 29\n          }, this), (view === 'image' || questions[selectedIndex].questionData.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n            imageUrl: questions[selectedIndex].questionData.imageUrl,\n            onImageDrop: image => handleQuestionChange({\n              target: {\n                value: image\n              }\n            }, 'imageUrl'),\n            onImageRemove: () => handleQuestionChange({\n              target: {\n                value: ''\n              }\n            }, 'imageUrl')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 33\n          }, this), questions[selectedIndex].questionData.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: questions[selectedIndex].statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-2 items-center w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row gap-2 items-center w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs font-bold whitespace-nowrap\",\n                  children: questions[selectedIndex].questionData.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n                  value: statement.content,\n                  onChange: e => handleStatementChange(index, e.target.value, 'content'),\n                  placeholder: \"Nh\\u1EADp n\\u1ED9i dung m\\u1EC7nh \\u0111\\u1EC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 45\n              }, this), (view === 'image' || statement.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n                imageUrl: statement.imageUrl,\n                onImageDrop: image => handleStatementChange(index, image, 'imageUrl'),\n                onImageRemove: () => handleStatementChange(index, '', 'imageUrl')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 49\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 41\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 33\n          }, this), questions[selectedIndex].questionData.typeOfQuestion === 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              value: questions[selectedIndex].questionData.correctAnswer,\n              onChange: e => handleQuestionChange(e, 'correctAnswer'),\n              placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n              label: \"\\u0110\\xE1p \\xE1n\",\n              Icon: CheckCircle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(SolutionEditor, {\n            solution: questions[selectedIndex].questionData.solution,\n            onSolutionChange: handleSolutionQuestionChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 700,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 672,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 666,\n    columnNumber: 9\n  }, this);\n};\n_s7(Step4Form, \"mEGCa3uGuxnLJSepP+eV1Z59L8Y=\", false, function () {\n  return [useSelector, useDispatch, useSelector];\n});\n_c10 = Step4Form;\nconst LeftContent = () => {\n  _s8();\n  const dispatch = useDispatch();\n  const {\n    step,\n    examData,\n    loading,\n    examImage,\n    examFile,\n    questions\n  } = useSelector(state => state.addExam);\n  const handleNext = () => {\n    if (step < 4) dispatch(nextStep());\n  };\n  const handlePrev = () => {\n    if (step > 1) dispatch(prevStep());\n  };\n  const handleSubmit = async () => {\n    if (!validateExamData(examData, dispatch)) return;\n    await dispatch(postExam({\n      examData,\n      examImage,\n      questions: questions || [],\n      examFile\n    })).unwrap();\n    // Handle success\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_42px)]\",\n    children: [/*#__PURE__*/_jsxDEV(CompactStepHeader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 800,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto\",\n      children: [step === 1 && /*#__PURE__*/_jsxDEV(Step1Form, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 806,\n        columnNumber: 21\n      }, this), step === 2 && /*#__PURE__*/_jsxDEV(Step2Form, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 21\n      }, this), step === 3 && /*#__PURE__*/_jsxDEV(Step3Form, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 815,\n        columnNumber: 21\n      }, this), step === 4 && /*#__PURE__*/_jsxDEV(Step4Form, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 820,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 803,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-t border-gray-200 p-2 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePrev,\n          disabled: step === 1,\n          className: \"flex items-center gap-1 px-2 py-1 text-xs text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed\",\n          children: [/*#__PURE__*/_jsxDEV(ChevronLeft, {\n            className: \"w-3 h-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 25\n          }, this), \"Quay l\\u1EA1i\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 827,\n          columnNumber: 21\n        }, this), step < 3 ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleNext,\n          className: \"flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-xs\",\n          children: [\"Ti\\u1EBFp theo\", /*#__PURE__*/_jsxDEV(ChevronRight, {\n            className: \"w-3 h-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 842,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSubmit,\n          disabled: loading,\n          className: \"flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50 text-xs\",\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              minHeight: \"min-h-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 37\n            }, this), \"\\u0110ang t\\u1EA1o...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Save, {\n              className: \"w-3 h-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 37\n            }, this), \"T\\u1EA1o \\u0111\\u1EC1 thi\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 845,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 826,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 825,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 798,\n    columnNumber: 9\n  }, this);\n};\n_s8(LeftContent, \"JFH9vutr6E5+I+wgibQN8WmKR6Q=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c11 = LeftContent;\nexport default LeftContent;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11;\n$RefreshReg$(_c, \"Step1Form\");\n$RefreshReg$(_c2, \"ButtonAddQuestion\");\n$RefreshReg$(_c3, \"AddQuestionForm\");\n$RefreshReg$(_c4, \"AddTNQuestion\");\n$RefreshReg$(_c5, \"AddDSQuestion\");\n$RefreshReg$(_c6, \"AddTLNQuestion\");\n$RefreshReg$(_c7, \"Step2Form\");\n$RefreshReg$(_c8, \"Step3Form\");\n$RefreshReg$(_c9, \"ListQuestions\");\n$RefreshReg$(_c10, \"Step4Form\");\n$RefreshReg$(_c11, \"LeftContent\");", "map": {"version": 3, "names": ["useEffect", "useState", "useRef", "useDispatch", "useSelector", "setExamData", "postExam", "nextStep", "prevStep", "setExamImage", "setExamFile", "setQuestionTNContent", "setQuestionDSContent", "setQuestionTLNContent", "setCorrectAnswerTN", "setCorrectAnswerDS", "setCorrectAnswerTLN", "setQuestions", "setSelectedIndex", "setOcrFile", "setMarkDownExam", "setBase64Images", "DropMenuBarAdmin", "SuggestInputBarAdmin", "CompactStepHeader", "Clock", "Users", "BookOpen", "Image", "ImageIcon", "Upload", "FileText", "CheckCircle", "ChevronRight", "ChevronLeft", "Plus", "Save", "Trash2", "Info", "ImageUpload", "UploadPdf", "LoadingSpinner", "NavigateBar", "SolutionEditor", "normalizeText", "validateExamData", "splitMarkdownToParts", "ImageDropZone", "TextArea", "PdfViewer", "ocrPdfWithMistral", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadBase64Images", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Step1Form", "_s", "dispatch", "examData", "examImage", "examFile", "state", "addExam", "codes", "updateExamData", "field", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "name", "onChange", "e", "target", "placeholder", "selectedOption", "typeOfExam", "option", "options", "Array", "isArray", "class", "year", "testDuration", "passRate", "chapter", "solutionUrl", "description", "rows", "checked", "public", "isClassroomExam", "image", "setImage", "img", "inputId", "compact", "setPdf", "pdf", "deleteButton", "_c", "ButtonAddQuestion", "_ref", "text", "onClick", "_c2", "AddQuestionForm", "_ref2", "questionContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleContentChange", "handleCorrectAnswerChange", "hintAnswer", "hintContent", "label", "Icon", "hint", "buttonFilterText", "_c3", "AddTNQuestion", "_s2", "questionT<PERSON>ontent", "correctAnswerTN", "_c4", "AddDSQuestion", "_s3", "question<PERSON><PERSON><PERSON><PERSON>", "correctAnswerDS", "_c5", "AddTLNQuestion", "_s4", "questionTLNContent", "correctAnswerTLN", "_c6", "convertFileToBase64", "file", "Promise", "resolve", "reject", "reader", "FileReader", "onloadend", "result", "onerror", "readAsDataURL", "Step2Form", "_s5", "ocrFile", "markDownExam", "loadingOcr", "base64Images", "fileInputRef", "handleFileChange", "handleAddImages", "event", "files", "length", "base64List", "all", "from", "map", "handleOcr", "handleRemoveImage", "indexToRemove", "newImages", "filter", "_", "i", "disabled", "multiple", "accept", "ref", "style", "display", "current", "click", "index", "title", "src", "alt", "concat", "_c7", "Step3Form", "_s6", "isViewAdd", "setIsViewAdd", "view", "<PERSON><PERSON><PERSON><PERSON>", "trim", "list", "id", "active", "setActive", "_c8", "ListQuestions", "_ref3", "count", "_c9", "Step4Form", "_s7", "questions", "selectedIndex", "questionCount", "setQuestionCount", "TN", "DS", "TLN", "prefixTN", "prefixDS", "optionChapter", "setOptionChapter", "counts", "reduce", "acc", "q", "questionData", "typeOfQuestion", "undefined", "_questions$selectedIn", "_questions$selectedIn2", "_questions$selectedIn3", "_questions$selectedIn4", "code", "_questions$selectedIn5", "_questions$selectedIn6", "startsWith", "handleQuestionChange", "newQuestions", "question", "qIndex", "handleSolutionQuestionChange", "newSolution", "solution", "handleStatementChange", "statements", "stmt", "sIndex", "difficulty", "content", "imageUrl", "onImageDrop", "onImageRemove", "statement", "<PERSON><PERSON><PERSON><PERSON>", "onSolutionChange", "_c10", "LeftContent", "_s8", "step", "loading", "handleNext", "handlePrev", "handleSubmit", "unwrap", "minHeight", "_c11", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/LeftContent.jsx"], "sourcesContent": ["// Optimized Form Panel Component\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport {\r\n    setExamData,\r\n    postExam,\r\n    nextStep,\r\n    prevStep,\r\n    setExamImage,\r\n    setExamFile,\r\n    setQuestionTNContent,\r\n    setQuestionDSContent,\r\n    setQuestionTLNContent,\r\n    setCorrectAnswerTN,\r\n    setCorrectAnswerDS,\r\n    setCorrectAnswerTLN,\r\n    setQuestions,\r\n    setSelectedIndex,\r\n    setOcrFile,\r\n    setMarkDownExam,\r\n    setBase64Images,\r\n} from \"src/features/addExam/addExamSlice\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport CompactStepHeader from \"./CompactStepHeader\";\r\nimport { Clock, Users, BookOpen, Image as ImageIcon, Upload, FileText, CheckCircle, ChevronRight, ChevronLeft, Plus, Save, Trash2, Info } from \"lucide-react\";\r\nimport ImageUpload from \"src/components/image/UploadImage\";\r\nimport UploadPdf from \"src/components/UploadPdf\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\nimport NavigateBar from \"./NavigateBar\";\r\nimport SolutionEditor from \"./SolutionEditor\";\r\nimport { normalizeText, validateExamData, splitMarkdownToParts } from \"src/utils/question/questionUtils\";\r\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\r\nimport TextArea from \"src/components/input/TextArea\";\r\nimport PdfViewer from \"../ViewPdf\";\r\nimport { ocrPdfWithMistral } from \"src/features/addExam/addExamSlice\";\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\nimport { uploadBase64Images } from \"src/features/image/imageSlice\";\r\n\r\nconst Step1Form = () => {\r\n    const dispatch = useDispatch();\r\n    const { examData, examImage, examFile } = useSelector((state) => state.addExam);\r\n    const { codes } = useSelector(state => state.codes);\r\n\r\n    const updateExamData = (field, value) => {\r\n        dispatch(setExamData({ field, value }));\r\n    };\r\n\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3\">\r\n            {/* Compact Name & Type Row */}\r\n            <div className=\"grid grid-cols-2 gap-2\">\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        Tên đề thi <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input\r\n                        type=\"text\"\r\n                        value={examData.name || ''}\r\n                        onChange={(e) => updateExamData('name', e.target.value)}\r\n                        className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                        placeholder=\"Nhập tên đề thi\"\r\n                    />\r\n                </div>\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        Kiểu đề <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={examData.typeOfExam}\r\n                        onChange={(option) => updateExamData('typeOfExam', option)}\r\n                        options={Array.isArray(codes[\"exam type\"]) ? codes[\"exam type\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n            </div>\r\n\r\n            {/* Compact Class & Year Row */}\r\n            <div className=\"grid grid-cols-2 gap-2\">\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        Lớp <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={examData.class}\r\n                        onChange={(option) => updateExamData('class', option)}\r\n                        options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        Năm <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={examData.year}\r\n                        onChange={(option) => updateExamData('year', option)}\r\n                        options={Array.isArray(codes[\"year\"]) ? codes[\"year\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n            </div>\r\n\r\n            {/* Compact Duration & Pass Rate Row */}\r\n            <div className=\"grid grid-cols-2 gap-2\">\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <Clock className=\"w-3 h-3 inline mr-1\" />\r\n                        Thời gian (phút)\r\n                    </label>\r\n                    <input\r\n                        type=\"number\"\r\n                        value={examData.testDuration || ''}\r\n                        onChange={(e) => updateExamData('testDuration', e.target.value)}\r\n                        className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                        placeholder=\"90\"\r\n                    />\r\n                </div>\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <Users className=\"w-3 h-3 inline mr-1\" />\r\n                        Điểm đạt (%)\r\n                    </label>\r\n                    <input\r\n                        type=\"number\"\r\n                        value={examData.passRate || ''}\r\n                        onChange={(e) => updateExamData('passRate', e.target.value)}\r\n                        className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                        placeholder=\"50\"\r\n                    />\r\n                </div>\r\n            </div>\r\n\r\n            {/* Chapter (conditional) */}\r\n            {examData.typeOfExam === \"OT\" && (\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <BookOpen className=\"w-3 h-3 inline mr-1\" />\r\n                        Chương\r\n                    </label>\r\n                    <SuggestInputBarAdmin\r\n                        selectedOption={examData.chapter}\r\n                        onChange={(option) => updateExamData('chapter', option)}\r\n                        options={Array.isArray(codes[\"chapter\"]) ? codes[\"chapter\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n            )}\r\n            <div>\r\n                <label className=\"block text-xs font-medium text-gray-700 mb-1\">Link lời giải</label>\r\n                <textarea\r\n                    value={examData.solutionUrl}\r\n                    onChange={(e) => updateExamData('solutionUrl', e.target.value)}\r\n                    className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                    placeholder=\"Nhập link lời giải vd: youtube, ...\"\r\n                />\r\n            </div>\r\n            {/* Compact Description */}\r\n            <div>\r\n                <label className=\"block text-xs font-medium text-gray-700 mb-1\">Mô tả</label>\r\n                <textarea\r\n                    value={examData.description || ''}\r\n                    onChange={(e) => updateExamData('description', e.target.value)}\r\n                    rows={2}\r\n                    className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                    placeholder=\"Mô tả ngắn về đề thi...\"\r\n                />\r\n            </div>\r\n\r\n            {/* Compact Checkboxes */}\r\n            <div className=\"flex items-center gap-3\">\r\n                <label className=\"flex items-center text-xs\">\r\n                    <input\r\n                        type=\"checkbox\"\r\n                        checked={examData.public || false}\r\n                        onChange={(e) => updateExamData('public', e.target.checked)}\r\n                        className=\"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\r\n                    />\r\n                    <span className=\"ml-1 text-gray-700\">Công khai</span>\r\n                </label>\r\n                <label className=\"flex items-center text-xs\">\r\n                    <input\r\n                        type=\"checkbox\"\r\n                        checked={examData.isClassroomExam || false}\r\n                        onChange={(e) => updateExamData('isClassroomExam', e.target.checked)}\r\n                        className=\"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\r\n                    />\r\n                    <span className=\"ml-1 text-gray-700\">Đề thi lớp</span>\r\n                </label>\r\n            </div>\r\n\r\n            {/* Compact File Uploads */}\r\n            <div className=\"flex flex-col gap-2\">\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <ImageIcon className=\"w-3 h-3 inline mr-1\" />\r\n                        Ảnh đề thi\r\n                    </label>\r\n                    <ImageUpload\r\n                        image={examImage}\r\n                        setImage={(img) => dispatch(setExamImage(img))}\r\n                        inputId=\"exam-image-compact\"\r\n                        compact={true}\r\n                    />\r\n                </div>\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <Upload className=\"w-3 h-3 inline mr-1\" />\r\n                        File PDF\r\n                    </label>\r\n                    <UploadPdf\r\n                        setPdf={(pdf) => dispatch(setExamFile(pdf))}\r\n                        deleteButton={false}\r\n                        compact={true}\r\n                    />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nconst ButtonAddQuestion = ({ text, onClick }) => {\r\n    return (\r\n        <button\r\n            onClick={onClick}\r\n            className=\"w-full px-3 py-2 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors text-xs\">\r\n            <Plus className=\"w-3 h-3 inline mr-1\" />\r\n            {text}\r\n        </button>\r\n    )\r\n}\r\n\r\n\r\n\r\nconst AddQuestionForm = ({ questionContent, correctAnswerContent, handleContentChange, handleCorrectAnswerChange, hintAnswer, hintContent }) => {\r\n    return (\r\n        <div className=\"p-3 flex flex-col gap-4\">\r\n            <TextArea\r\n                value={correctAnswerContent}\r\n                onChange={handleCorrectAnswerChange}\r\n                placeholder=\"Nhập đáp án\"\r\n                label=\"Đáp án\"\r\n                Icon={CheckCircle}\r\n                hint={hintAnswer}\r\n            />\r\n            <TextArea\r\n                value={questionContent}\r\n                onChange={handleContentChange}\r\n                placeholder=\"Nhập nội dung câu hỏi\"\r\n                label=\"Câu hỏi\"\r\n                Icon={Plus}\r\n                hint={hintContent}\r\n                buttonFilterText={{\r\n                    text: \"Lọc\",\r\n                    onClick: () => {\r\n                        handleContentChange({\r\n                            target: {\r\n                                value: normalizeText(questionContent)\r\n                            }\r\n                        });\r\n                    }\r\n                }}\r\n            />\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nconst AddTNQuestion = () => {\r\n    const dispatch = useDispatch();\r\n    const { questionTNContent, correctAnswerTN } = useSelector((state) => state.addExam);\r\n\r\n    const handleContentChange = (e) => {\r\n        dispatch(setQuestionTNContent(e.target.value));\r\n    };\r\n\r\n    const handleCorrectAnswerChange = (e) => {\r\n        dispatch(setCorrectAnswerTN(e.target.value));\r\n    };\r\n\r\n    return (\r\n        <AddQuestionForm\r\n            questionContent={questionTNContent}\r\n            correctAnswerContent={correctAnswerTN}\r\n            handleContentChange={handleContentChange}\r\n            handleCorrectAnswerChange={handleCorrectAnswerChange}\r\n            hintAnswer=\"Đáp án trắc nghiệm: A b c D ...\"\r\n            hintContent=\"Nội dung trắc nghiệm: Paste cả câu hỏi mệnh đề và lời giải\"\r\n        />\r\n    )\r\n}\r\n\r\nconst AddDSQuestion = () => {\r\n    const dispatch = useDispatch();\r\n    const { questionDSContent, correctAnswerDS } = useSelector((state) => state.addExam);\r\n\r\n    const handleContentChange = (e) => {\r\n        dispatch(setQuestionDSContent(e.target.value));\r\n    };\r\n\r\n    const handleCorrectAnswerChange = (e) => {\r\n        dispatch(setCorrectAnswerDS(e.target.value));\r\n    };\r\n\r\n    return (\r\n        <AddQuestionForm\r\n            questionContent={questionDSContent}\r\n            correctAnswerContent={correctAnswerDS}\r\n            handleContentChange={handleContentChange}\r\n            handleCorrectAnswerChange={handleCorrectAnswerChange}\r\n            hintAnswer=\"Đáp án đúng sai: ĐĐSS dsss DSDS ...\"\r\n            hintContent=\"Nội dung câu hỏi: Paste cả câu hỏi mệnh đề và lời giải\"\r\n        />\r\n    )\r\n}\r\n\r\nconst AddTLNQuestion = () => {\r\n    const dispatch = useDispatch();\r\n    const { questionTLNContent, correctAnswerTLN } = useSelector((state) => state.addExam);\r\n\r\n    const handleContentChange = (e) => {\r\n        dispatch(setQuestionTLNContent(e.target.value));\r\n    };\r\n\r\n    const handleCorrectAnswerChange = (e) => {\r\n        dispatch(setCorrectAnswerTLN(e.target.value));\r\n    };\r\n\r\n    return (\r\n        <AddQuestionForm\r\n            questionContent={questionTLNContent}\r\n            correctAnswerContent={correctAnswerTLN}\r\n            handleContentChange={handleContentChange}\r\n            handleCorrectAnswerChange={handleCorrectAnswerChange}\r\n            hintAnswer=\"Đáp án trả lời ngắn: 3,14 1.5 3,2\"\r\n            hintContent=\"Nội dung câu hỏi: Paste cả câu hỏi mệnh đề và lời giải\"\r\n        />\r\n    )\r\n}\r\n\r\nconst convertFileToBase64 = (file) => {\r\n    return new Promise((resolve, reject) => {\r\n        const reader = new FileReader();\r\n        reader.onloadend = () => resolve(reader.result);\r\n        reader.onerror = reject;\r\n        reader.readAsDataURL(file);\r\n    });\r\n};\r\n\r\n\r\nconst Step2Form = () => {\r\n    const dispatch = useDispatch();\r\n    const { ocrFile, markDownExam, loadingOcr, base64Images } = useSelector((state) => state.addExam);\r\n    const fileInputRef = useRef(null);\r\n\r\n    const handleFileChange = (file) => {\r\n        dispatch(setOcrFile(file));\r\n    };\r\n\r\n    const handleAddImages = async (event) => {\r\n        const files = event.target.files;\r\n\r\n        if (!files || files.length === 0) return;\r\n\r\n        const base64List = await Promise.all(\r\n            Array.from(files).map(file => convertFileToBase64(file))\r\n        );\r\n\r\n        dispatch(setBase64Images([...base64Images, ...base64List]));\r\n    };\r\n\r\n    const handleOcr = () => {\r\n        dispatch(ocrPdfWithMistral(ocrFile));\r\n    };\r\n\r\n    const handleContentChange = (e) => {\r\n        dispatch(setMarkDownExam(e.target.value));\r\n    };\r\n\r\n    const handleRemoveImage = (indexToRemove) => {\r\n        const newImages = base64Images.filter((_, i) => i !== indexToRemove);\r\n        dispatch(setBase64Images(newImages));\r\n    };\r\n\r\n    return (\r\n        <div className=\"p-3 flex flex-col gap-4\">\r\n            <UploadPdf\r\n                setPdf={handleFileChange}\r\n                deleteButton={false}\r\n                compact={true}\r\n            />\r\n            <button\r\n                onClick={handleOcr}\r\n                disabled={loadingOcr || !ocrFile}\r\n                className=\"flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-xs disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n                {loadingOcr ? (\r\n                    <>\r\n                        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\r\n                        Đang xử lý...\r\n                    </>\r\n                ) : (\r\n                    <>\r\n                        <FileText className=\"w-4 h-4\" />\r\n                        Xử lý đề thi\r\n                    </>\r\n                )}\r\n            </button>\r\n            {base64Images.length > 0 && (\r\n                <div className=\"flex flex-col gap-2\">\r\n                    <div className=\"flex flex-wrap gap-2\">\r\n                        <input\r\n                            type=\"file\"\r\n                            multiple\r\n                            accept=\"image/*\"\r\n                            ref={fileInputRef}\r\n                            style={{ display: 'none' }}\r\n                            onChange={handleAddImages}\r\n                        />\r\n\r\n                        <button\r\n                            onClick={() => fileInputRef.current.click()}\r\n                            className=\"text-xs border-dashed border-2 hover:bg-gray-100 border-gray-300 hover:shadow-lg rounded-lg px-3 py-1 flex flex-col items-center justify-center\"\r\n                        >\r\n                            Thêm ảnh\r\n                        </button>\r\n                        {base64Images.map((image, index) => (\r\n                            <div key={index} className=\"relative group\">\r\n                                {/* Nút X */}\r\n                                <button\r\n                                    onClick={() => handleRemoveImage(index)}\r\n                                    className=\"h-4 w-4 absolute top-0 right-0 bg-red-600 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity z-10\"\r\n                                    title=\"Xoá ảnh\"\r\n                                >\r\n                                    ×\r\n                                </button>\r\n\r\n                                {/* Ảnh */}\r\n                                <img\r\n                                    src={image}\r\n                                    alt={`Image ${index}`}\r\n                                    className=\"w-24 h-24 object-contain rounded\"\r\n                                />\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n            {markDownExam && (\r\n                <TextArea\r\n                    value={markDownExam}\r\n                    onChange={handleContentChange}\r\n                    placeholder=\"Nội dung đề thi\"\r\n                    label=\"Nội dung đề thi\"\r\n                />\r\n            )}\r\n        </div>\r\n\r\n    )\r\n}\r\n\r\nconst Step3Form = () => {\r\n    const [isViewAdd, setIsViewAdd] = useState(true);\r\n    const [view, setView] = useState('TN');\r\n    const { markDownExam, questionTNContent, questionDSContent, questionTLNContent, correctAnswerTN, correctAnswerDS, correctAnswerTLN } = useSelector((state) => state.addExam);\r\n    const dispatch = useDispatch();\r\n    useEffect(() => {\r\n        if (!isViewAdd) return\r\n        if (questionTNContent.trim() !== \"\" || correctAnswerTN.trim() !== \"\") {\r\n            setIsViewAdd(false);\r\n            setView('TN')\r\n        } else if (questionDSContent.trim() !== \"\" || correctAnswerDS.trim() !== \"\") {\r\n            setIsViewAdd(false);\r\n            setView('DS')\r\n        } else if (questionTLNContent.trim() !== \"\" || correctAnswerTLN.trim() !== \"\") {\r\n            setIsViewAdd(false);\r\n            setView('TLN')\r\n        }\r\n    }, [questionTNContent, correctAnswerTN, questionDSContent, correctAnswerDS, questionTLNContent, correctAnswerTLN]);\r\n\r\n    return (\r\n        <div className=\"space-y-3\">\r\n            {isViewAdd ? (\r\n                <div className=\"text-center py-4 px-3\">\r\n                    <FileText className=\"w-8 h-8 mx-auto text-gray-400 mb-2\" />\r\n                    <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thêm câu hỏi</h3>\r\n                    <p className=\"text-xs text-gray-600 mb-3\">\r\n                        Tạo câu hỏi cho đề thi của bạn\r\n                    </p>\r\n                    <div className=\"space-y-2\">\r\n                        <ButtonAddQuestion\r\n                            text=\"Thêm câu trắc nghiệm\"\r\n                            onClick={() => {\r\n                                setIsViewAdd(false);\r\n                                setView('TN')\r\n                            }}\r\n                        />\r\n                        <ButtonAddQuestion\r\n                            text=\"Thêm câu đúng sai\"\r\n                            onClick={() => {\r\n                                setIsViewAdd(false);\r\n                                setView('DS')\r\n                            }}\r\n                        />\r\n                        <ButtonAddQuestion\r\n                            text=\"Thêm câu trả lời ngắn\"\r\n                            onClick={() => {\r\n                                setIsViewAdd(false);\r\n                                setView('TLN')\r\n                            }}\r\n                        />\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <NavigateBar\r\n                    list={[{\r\n                        id: 1,\r\n                        name: 'Trắc nghiệm',\r\n                        value: 'TN'\r\n                    },\r\n                    {\r\n                        id: 2,\r\n                        name: 'Đúng sai',\r\n                        value: 'DS'\r\n                    },\r\n                    {\r\n                        id: 3,\r\n                        name: 'Trả lời ngắn',\r\n                        value: 'TLN'\r\n                    }\r\n                    ]}\r\n                    active={view}\r\n                    setActive={setView}\r\n                />\r\n            )}\r\n\r\n            {view === 'TN' && !isViewAdd && (\r\n                <AddTNQuestion />\r\n            )}\r\n            {view === 'DS' && !isViewAdd && (\r\n                <AddDSQuestion />\r\n            )}\r\n            {view === 'TLN' && !isViewAdd && (\r\n                <AddTLNQuestion />\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst ListQuestions = ({ count, title, onClick, i }) => {\r\n    return (\r\n        <div className=\"flex  flex-row w-full justify-start items-center border-b border-[#e3e4e5] pb-2\">\r\n            <div className=\"text-[#090a0a] text-xs font-bold font-bevietnam leading-loose whitespace-nowrap mr-4 flex-shrink-0 min-w-[6.2rem]\">\r\n                {title}:\r\n            </div>\r\n            <div className=\"flex flex-row gap-5 w-full overflow-x-auto min-h-max \">\r\n                {Array.from({ length: count }).map((_, index) => (\r\n                    <div\r\n                        key={index + title}\r\n                        onClick={() => onClick(index)}\r\n                        className={`cursor-pointer border text-xs border-[#e3e4e5] rounded flex justify-center whitespace-nowrap items-center gap-2.5 ${i === index ? 'bg-[#253f61] text-white' : 'bg-white text-[#253f61]'} px-2 py-1`}>\r\n                        Câu hỏi {index + 1}\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nconst Step4Form = () => {\r\n    const { questions, selectedIndex, view } = useSelector((state) => state.addExam);\r\n    const dispatch = useDispatch();\r\n    const [questionCount, setQuestionCount] = useState({\r\n        TN: 0,\r\n        DS: 0,\r\n        TLN: 0\r\n    });\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    const { codes } = useSelector((state) => state.codes);\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n\r\n    useEffect(() => {\r\n        if (questions) {\r\n            const counts = questions.reduce(\r\n                (acc, q) => {\r\n                    const type = q.questionData.typeOfQuestion;\r\n                    if (acc[type] !== undefined) acc[type]++;\r\n                    return acc;\r\n                },\r\n                { TN: 0, DS: 0, TLN: 0 }\r\n            );\r\n\r\n            setQuestionCount(counts);\r\n        }\r\n    }, [questions]);\r\n\r\n\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (questions[selectedIndex]?.questionData?.class && questions[selectedIndex]?.questionData?.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.startsWith(questions[selectedIndex]?.questionData?.class) && code.code.length === 5)\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"].filter((code) => code.code.length === 5));\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, questions, selectedIndex]);\r\n\r\n    const handleQuestionChange = (e, field) => {\r\n        const newQuestions = questions.map((question, qIndex) => {\r\n            if (qIndex === selectedIndex) {\r\n                return {\r\n                    ...question,\r\n                    questionData: {\r\n                        ...question.questionData,\r\n                        [field]: e.target.value,\r\n                    }\r\n                };\r\n            }\r\n            return question;\r\n        });\r\n        dispatch(setQuestions(newQuestions));\r\n    };\r\n    const handleSolutionQuestionChange = (newSolution) => {\r\n        const newQuestions = questions.map((question, qIndex) => {\r\n            if (qIndex === selectedIndex) {\r\n                return {\r\n                    ...question,\r\n                    questionData: {\r\n                        ...question.questionData,\r\n                        solution: newSolution,\r\n                    }\r\n                };\r\n            }\r\n            return question;\r\n        });\r\n        dispatch(setQuestions(newQuestions));\r\n    };\r\n\r\n    const handleStatementChange = (index, value, field) => {\r\n        const newQuestions = questions.map((question, qIndex) => {\r\n            if (qIndex === selectedIndex) {\r\n                return {\r\n                    ...question,\r\n                    statements: question.statements.map((stmt, sIndex) => {\r\n                        if (sIndex === index) {\r\n                            return { ...stmt, [field]: value };\r\n                        }\r\n                        return stmt;\r\n                    })\r\n                };\r\n            }\r\n            return question;\r\n        });\r\n        dispatch(setQuestions(newQuestions));\r\n    };\r\n\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3 w-full\">\r\n            <ListQuestions count={questionCount.TN} title={'Trắc nghiệm'} onClick={(index) => dispatch(setSelectedIndex(index))} i={selectedIndex} />\r\n            <ListQuestions count={questionCount.DS} title={'Đúng sai'} onClick={(index) => dispatch(setSelectedIndex(index + questionCount.TN))} i={selectedIndex - questionCount.TN} />\r\n            <ListQuestions count={questionCount.TLN} title={'Trả lời ngắn'} onClick={(index) => dispatch(setSelectedIndex(index + questionCount.DS + questionCount.TN))} i={selectedIndex - (questionCount.DS + questionCount.TN)} />\r\n\r\n            {questions && questions[selectedIndex] && (\r\n                <div className=\"space-y-3 w-full\">\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Phân loại</h3>\r\n                        <div className=\"flex flex-row gap-2\">\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={questions[selectedIndex].questionData.class}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'class')}\r\n                                options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                                className=\"text-xs\"\r\n                                placeholder=\"Chọn lớp\"\r\n                            />\r\n                            <SuggestInputBarAdmin\r\n                                selectedOption={questions[selectedIndex].questionData.chapter}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'chapter')}\r\n                                options={optionChapter}\r\n                                className=\"text-xs\"\r\n                                placeholder=\"Chọn chương\"\r\n                            />\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={questions[selectedIndex].questionData.difficulty}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'difficulty')}\r\n                                options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                                className=\"text-xs\"\r\n                                placeholder=\"Chọn độ khó\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <hr className=\" bg-gray-200\"></hr>\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thông tin câu hỏi</h3>\r\n                        <div className=\"space-y-2\">\r\n                            <TextArea\r\n                                value={questions[selectedIndex].questionData.content}\r\n                                onChange={(e) => handleQuestionChange(e, 'content')}\r\n                                placeholder=\"Nhập nội dung câu hỏi\"\r\n                                label=\"Câu hỏi\"\r\n                            />\r\n                            {(view === 'image' || questions[selectedIndex].questionData.imageUrl) && (\r\n                                <ImageDropZone\r\n                                    imageUrl={questions[selectedIndex].questionData.imageUrl}\r\n                                    onImageDrop={(image) => handleQuestionChange({ target: { value: image } }, 'imageUrl')}\r\n                                    onImageRemove={() => handleQuestionChange({ target: { value: '' } }, 'imageUrl')}\r\n                                />\r\n                            )}\r\n                            {questions[selectedIndex].questionData.typeOfQuestion !== 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    {questions[selectedIndex].statements.map((statement, index) => (\r\n                                        <div key={index} className=\"flex flex-col gap-2 items-center w-full\">\r\n                                            <div className=\"flex flex-row gap-2 items-center w-full\">\r\n                                                <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                                    {questions[selectedIndex].questionData.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}\r\n                                                </p>\r\n                                                <TextArea\r\n                                                    value={statement.content}\r\n                                                    onChange={(e) => handleStatementChange(index, e.target.value, 'content')}\r\n                                                    placeholder=\"Nhập nội dung mệnh đề\"\r\n                                                />\r\n                                            </div>\r\n                                            {(view === 'image' || statement.imageUrl) && (\r\n                                                <ImageDropZone\r\n                                                    imageUrl={statement.imageUrl}\r\n                                                    onImageDrop={(image) => handleStatementChange(index, image, 'imageUrl')}\r\n                                                    onImageRemove={() => handleStatementChange(index, '', 'imageUrl')}\r\n                                                />\r\n                                            )}\r\n\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            )}\r\n                            {questions[selectedIndex].questionData.typeOfQuestion === 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    <TextArea\r\n                                        value={questions[selectedIndex].questionData.correctAnswer}\r\n                                        onChange={(e) => handleQuestionChange(e, 'correctAnswer')}\r\n                                        placeholder=\"Nhập đáp án\"\r\n                                        label=\"Đáp án\"\r\n                                        Icon={CheckCircle}\r\n                                    />\r\n                                </div>\r\n                            )}\r\n                            {/* <TextArea\r\n                                value={questions[selectedIndex].questionData.solution}\r\n                                onChange={(e) => handleQuestionChange(e, 'solution')}\r\n                                placeholder=\"Nhập lời giải\"\r\n                                label=\"Lời giải\"\r\n                                Icon={CheckCircle}\r\n                            /> */}\r\n                            <SolutionEditor\r\n                                solution={questions[selectedIndex].questionData.solution}\r\n                                onSolutionChange={handleSolutionQuestionChange}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nconst LeftContent = () => {\r\n    const dispatch = useDispatch();\r\n    const { step, examData, loading, examImage, examFile, questions } = useSelector((state) => state.addExam);\r\n    const handleNext = () => {\r\n        if (step < 4) dispatch(nextStep());\r\n    };\r\n\r\n    const handlePrev = () => {\r\n        if (step > 1) dispatch(prevStep());\r\n    };\r\n\r\n    const handleSubmit = async () => {\r\n        if (!validateExamData(examData, dispatch)) return;\r\n\r\n        await dispatch(postExam({\r\n            examData,\r\n            examImage,\r\n            questions: questions || [],\r\n            examFile,\r\n        })).unwrap();\r\n        // Handle success\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_42px)]\">\r\n            {/* Compact Step Header */}\r\n            <CompactStepHeader />\r\n\r\n            {/* Scrollable Form Content */}\r\n            <div className=\"flex-1 overflow-y-auto\">\r\n                {/* Step 1: Basic Information */}\r\n                {step === 1 && (\r\n                    <Step1Form />\r\n                )}\r\n                {/* Step 2: Questions */}\r\n                {step === 2 && (\r\n                    <Step2Form />\r\n                )}\r\n\r\n                {/* Step 3: Questions */}\r\n                {step === 3 && (\r\n                    <Step3Form />\r\n                )}\r\n\r\n                {/* Step 4: Confirmation */}\r\n                {step === 4 && (\r\n                    <Step4Form />\r\n                )}\r\n            </div>\r\n\r\n            {/* Compact Navigation Footer */}\r\n            <div className=\"border-t border-gray-200 p-2 bg-white\">\r\n                <div className=\"flex justify-between items-center\">\r\n                    <button\r\n                        onClick={handlePrev}\r\n                        disabled={step === 1}\r\n                        className=\"flex items-center gap-1 px-2 py-1 text-xs text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                    >\r\n                        <ChevronLeft className=\"w-3 h-3\" />\r\n                        Quay lại\r\n                    </button>\r\n\r\n                    {step < 3 ? (\r\n                        <button\r\n                            onClick={handleNext}\r\n                            className=\"flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-xs\"\r\n                        >\r\n                            Tiếp theo\r\n                            <ChevronRight className=\"w-3 h-3\" />\r\n                        </button>\r\n                    ) : (\r\n                        <button\r\n                            onClick={handleSubmit}\r\n                            disabled={loading}\r\n                            className=\"flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50 text-xs\"\r\n                        >\r\n                            {loading ? (\r\n                                <>\r\n                                    <LoadingSpinner minHeight=\"min-h-0\" />\r\n                                    Đang tạo...\r\n                                </>\r\n                            ) : (\r\n                                <>\r\n                                    <Save className=\"w-3 h-3\" />\r\n                                    Tạo đề thi\r\n                                </>\r\n                            )}\r\n                        </button>\r\n                    )}\r\n                </div>\r\n            </div>\r\n        </div >\r\n    );\r\n};\r\n\r\nexport default LeftContent;"], "mappings": ";;;;;;;;;AAAA;AACA,SAASA,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACIC,WAAW,EACXC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,YAAY,EACZC,WAAW,EACXC,oBAAoB,EACpBC,oBAAoB,EACpBC,qBAAqB,EACrBC,kBAAkB,EAClBC,kBAAkB,EAClBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,EAChBC,UAAU,EACVC,eAAe,EACfC,eAAe,QACZ,mCAAmC;AAC1C,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,YAAY,EAAEC,WAAW,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,QAAQ,cAAc;AAC7J,OAAOC,WAAW,MAAM,kCAAkC;AAC1D,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,oBAAoB,QAAQ,kCAAkC;AACxG,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,OAAOC,QAAQ,MAAM,+BAA+B;AACpD,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,OAAOC,aAAa,MAAM,sBAAsB;AAChD,SAASC,kBAAkB,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGxD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyD,QAAQ;IAAEC,SAAS;IAAEC;EAAS,CAAC,GAAG1D,WAAW,CAAE2D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC/E,MAAM;IAAEC;EAAM,CAAC,GAAG7D,WAAW,CAAC2D,KAAK,IAAIA,KAAK,CAACE,KAAK,CAAC;EAEnD,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACrCT,QAAQ,CAACtD,WAAW,CAAC;MAAE8D,KAAK;MAAEC;IAAM,CAAC,CAAC,CAAC;EAC3C,CAAC;EAGD,oBACId,OAAA;IAAKe,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE1BhB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACnChB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,0BACjD,eAAAhB,OAAA;YAAMe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACRpB,OAAA;UACIqB,IAAI,EAAC,MAAM;UACXP,KAAK,EAAER,QAAQ,CAACgB,IAAI,IAAI,EAAG;UAC3BC,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC,MAAM,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;UACxDC,SAAS,EAAC,kHAAkH;UAC5HW,WAAW,EAAC;QAAiB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNpB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,yBACpD,eAAAhB,OAAA;YAAMe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACRpB,OAAA,CAAChC,gBAAgB;UACb2D,cAAc,EAAErB,QAAQ,CAACsB,UAAW;UACpCL,QAAQ,EAAGM,MAAM,IAAKjB,cAAc,CAAC,YAAY,EAAEiB,MAAM,CAAE;UAC3DC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,WAAW,CAAC,CAAC,GAAGA,KAAK,CAAC,WAAW,CAAC,GAAG,EAAG;UACrEI,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACnChB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,WACxD,eAAAhB,OAAA;YAAMe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACRpB,OAAA,CAAChC,gBAAgB;UACb2D,cAAc,EAAErB,QAAQ,CAAC2B,KAAM;UAC/BV,QAAQ,EAAGM,MAAM,IAAKjB,cAAc,CAAC,OAAO,EAAEiB,MAAM,CAAE;UACtDC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;UAC7DI,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNpB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,WACxD,eAAAhB,OAAA;YAAMe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACRpB,OAAA,CAAChC,gBAAgB;UACb2D,cAAc,EAAErB,QAAQ,CAAC4B,IAAK;UAC9BX,QAAQ,EAAGM,MAAM,IAAKjB,cAAc,CAAC,MAAM,EAAEiB,MAAM,CAAE;UACrDC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,MAAM,CAAC,CAAC,GAAGA,KAAK,CAAC,MAAM,CAAC,GAAG,EAAG;UAC3DI,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACnChB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DhB,OAAA,CAAC7B,KAAK;YAAC4C,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRpB,OAAA;UACIqB,IAAI,EAAC,QAAQ;UACbP,KAAK,EAAER,QAAQ,CAAC6B,YAAY,IAAI,EAAG;UACnCZ,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC,cAAc,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;UAChEC,SAAS,EAAC,kHAAkH;UAC5HW,WAAW,EAAC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNpB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DhB,OAAA,CAAC5B,KAAK;YAAC2C,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oCAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRpB,OAAA;UACIqB,IAAI,EAAC,QAAQ;UACbP,KAAK,EAAER,QAAQ,CAAC8B,QAAQ,IAAI,EAAG;UAC/Bb,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC,UAAU,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;UAC5DC,SAAS,EAAC,kHAAkH;UAC5HW,WAAW,EAAC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGLd,QAAQ,CAACsB,UAAU,KAAK,IAAI,iBACzB5B,OAAA;MAAAgB,QAAA,gBACIhB,OAAA;QAAOe,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3DhB,OAAA,CAAC3B,QAAQ;UAAC0C,SAAS,EAAC;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAEhD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRpB,OAAA,CAAC/B,oBAAoB;QACjB0D,cAAc,EAAErB,QAAQ,CAAC+B,OAAQ;QACjCd,QAAQ,EAAGM,MAAM,IAAKjB,cAAc,CAAC,SAAS,EAAEiB,MAAM,CAAE;QACxDC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,SAAS,CAAC,CAAC,GAAGA,KAAK,CAAC,SAAS,CAAC,GAAG,EAAG;QACjEI,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eACDpB,OAAA;MAAAgB,QAAA,gBACIhB,OAAA;QAAOe,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrFpB,OAAA;QACIc,KAAK,EAAER,QAAQ,CAACgC,WAAY;QAC5Bf,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC,aAAa,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;QAC/DC,SAAS,EAAC,kHAAkH;QAC5HW,WAAW,EAAC;MAAqC;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENpB,OAAA;MAAAgB,QAAA,gBACIhB,OAAA;QAAOe,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC7EpB,OAAA;QACIc,KAAK,EAAER,QAAQ,CAACiC,WAAW,IAAI,EAAG;QAClChB,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC,aAAa,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;QAC/D0B,IAAI,EAAE,CAAE;QACRzB,SAAS,EAAC,kHAAkH;QAC5HW,WAAW,EAAC;MAAyB;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACpChB,OAAA;QAAOe,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxChB,OAAA;UACIqB,IAAI,EAAC,UAAU;UACfoB,OAAO,EAAEnC,QAAQ,CAACoC,MAAM,IAAI,KAAM;UAClCnB,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC,QAAQ,EAAEY,CAAC,CAACC,MAAM,CAACgB,OAAO,CAAE;UAC5D1B,SAAS,EAAC;QAAiE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACFpB,OAAA;UAAMe,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eACRpB,OAAA;QAAOe,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxChB,OAAA;UACIqB,IAAI,EAAC,UAAU;UACfoB,OAAO,EAAEnC,QAAQ,CAACqC,eAAe,IAAI,KAAM;UAC3CpB,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC,iBAAiB,EAAEY,CAAC,CAACC,MAAM,CAACgB,OAAO,CAAE;UACrE1B,SAAS,EAAC;QAAiE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACFpB,OAAA;UAAMe,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAChChB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DhB,OAAA,CAACzB,SAAS;YAACwC,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BAEjD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRpB,OAAA,CAACf,WAAW;UACR2D,KAAK,EAAErC,SAAU;UACjBsC,QAAQ,EAAGC,GAAG,IAAKzC,QAAQ,CAAClD,YAAY,CAAC2F,GAAG,CAAC,CAAE;UAC/CC,OAAO,EAAC,oBAAoB;UAC5BC,OAAO,EAAE;QAAK;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNpB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DhB,OAAA,CAACxB,MAAM;YAACuC,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRpB,OAAA,CAACd,SAAS;UACN+D,MAAM,EAAGC,GAAG,IAAK7C,QAAQ,CAACjD,WAAW,CAAC8F,GAAG,CAAC,CAAE;UAC5CC,YAAY,EAAE,KAAM;UACpBH,OAAO,EAAE;QAAK;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAhB,EAAA,CArLKD,SAAS;EAAA,QACMtD,WAAW,EACcC,WAAW,EACnCA,WAAW;AAAA;AAAAsG,EAAA,GAH3BjD,SAAS;AAuLf,MAAMkD,iBAAiB,GAAGC,IAAA,IAAuB;EAAA,IAAtB;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAAF,IAAA;EACxC,oBACItD,OAAA;IACIwD,OAAO,EAAEA,OAAQ;IACjBzC,SAAS,EAAC,wGAAwG;IAAAC,QAAA,gBAClHhB,OAAA,CAACnB,IAAI;MAACkC,SAAS,EAAC;IAAqB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACvCmC,IAAI;EAAA;IAAAtC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEjB,CAAC;AAAAqC,GAAA,GATKJ,iBAAiB;AAavB,MAAMK,eAAe,GAAGC,KAAA,IAAwH;EAAA,IAAvH;IAAEC,eAAe;IAAEC,oBAAoB;IAAEC,mBAAmB;IAAEC,yBAAyB;IAAEC,UAAU;IAAEC;EAAY,CAAC,GAAAN,KAAA;EACvI,oBACI3D,OAAA;IAAKe,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACpChB,OAAA,CAACN,QAAQ;MACLoB,KAAK,EAAE+C,oBAAqB;MAC5BtC,QAAQ,EAAEwC,yBAA0B;MACpCrC,WAAW,EAAC,6BAAa;MACzBwC,KAAK,EAAC,mBAAQ;MACdC,IAAI,EAAEzF,WAAY;MAClB0F,IAAI,EAAEJ;IAAW;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eACFpB,OAAA,CAACN,QAAQ;MACLoB,KAAK,EAAE8C,eAAgB;MACvBrC,QAAQ,EAAEuC,mBAAoB;MAC9BpC,WAAW,EAAC,yCAAuB;MACnCwC,KAAK,EAAC,iBAAS;MACfC,IAAI,EAAEtF,IAAK;MACXuF,IAAI,EAAEH,WAAY;MAClBI,gBAAgB,EAAE;QACdd,IAAI,EAAE,KAAK;QACXC,OAAO,EAAEA,CAAA,KAAM;UACXM,mBAAmB,CAAC;YAChBrC,MAAM,EAAE;cACJX,KAAK,EAAExB,aAAa,CAACsE,eAAe;YACxC;UACJ,CAAC,CAAC;QACN;MACJ;IAAE;MAAA3C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAAkD,GAAA,GA/BKZ,eAAe;AAkCrB,MAAMa,aAAa,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxB,MAAMnE,QAAQ,GAAGxD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4H,iBAAiB;IAAEC;EAAgB,CAAC,GAAG5H,WAAW,CAAE2D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAEpF,MAAMoD,mBAAmB,GAAItC,CAAC,IAAK;IAC/BnB,QAAQ,CAAChD,oBAAoB,CAACmE,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC;EAClD,CAAC;EAED,MAAMiD,yBAAyB,GAAIvC,CAAC,IAAK;IACrCnB,QAAQ,CAAC7C,kBAAkB,CAACgE,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC;EAChD,CAAC;EAED,oBACId,OAAA,CAAC0D,eAAe;IACZE,eAAe,EAAEa,iBAAkB;IACnCZ,oBAAoB,EAAEa,eAAgB;IACtCZ,mBAAmB,EAAEA,mBAAoB;IACzCC,yBAAyB,EAAEA,yBAA0B;IACrDC,UAAU,EAAC,sDAAiC;IAC5CC,WAAW,EAAC;EAA4D;IAAAhD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3E,CAAC;AAEV,CAAC;AAAAoD,GAAA,CAtBKD,aAAa;EAAA,QACE1H,WAAW,EACmBC,WAAW;AAAA;AAAA6H,GAAA,GAFxDJ,aAAa;AAwBnB,MAAMK,aAAa,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxB,MAAMxE,QAAQ,GAAGxD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiI,iBAAiB;IAAEC;EAAgB,CAAC,GAAGjI,WAAW,CAAE2D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAEpF,MAAMoD,mBAAmB,GAAItC,CAAC,IAAK;IAC/BnB,QAAQ,CAAC/C,oBAAoB,CAACkE,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC;EAClD,CAAC;EAED,MAAMiD,yBAAyB,GAAIvC,CAAC,IAAK;IACrCnB,QAAQ,CAAC5C,kBAAkB,CAAC+D,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC;EAChD,CAAC;EAED,oBACId,OAAA,CAAC0D,eAAe;IACZE,eAAe,EAAEkB,iBAAkB;IACnCjB,oBAAoB,EAAEkB,eAAgB;IACtCjB,mBAAmB,EAAEA,mBAAoB;IACzCC,yBAAyB,EAAEA,yBAA0B;IACrDC,UAAU,EAAC,kEAAqC;IAChDC,WAAW,EAAC;EAAwD;IAAAhD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvE,CAAC;AAEV,CAAC;AAAAyD,GAAA,CAtBKD,aAAa;EAAA,QACE/H,WAAW,EACmBC,WAAW;AAAA;AAAAkI,GAAA,GAFxDJ,aAAa;AAwBnB,MAAMK,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAM7E,QAAQ,GAAGxD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEsI,kBAAkB;IAAEC;EAAiB,CAAC,GAAGtI,WAAW,CAAE2D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAEtF,MAAMoD,mBAAmB,GAAItC,CAAC,IAAK;IAC/BnB,QAAQ,CAAC9C,qBAAqB,CAACiE,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC;EACnD,CAAC;EAED,MAAMiD,yBAAyB,GAAIvC,CAAC,IAAK;IACrCnB,QAAQ,CAAC3C,mBAAmB,CAAC8D,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC;EACjD,CAAC;EAED,oBACId,OAAA,CAAC0D,eAAe;IACZE,eAAe,EAAEuB,kBAAmB;IACpCtB,oBAAoB,EAAEuB,gBAAiB;IACvCtB,mBAAmB,EAAEA,mBAAoB;IACzCC,yBAAyB,EAAEA,yBAA0B;IACrDC,UAAU,EAAC,6DAAmC;IAC9CC,WAAW,EAAC;EAAwD;IAAAhD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvE,CAAC;AAEV,CAAC;AAAA8D,GAAA,CAtBKD,cAAc;EAAA,QACCpI,WAAW,EACqBC,WAAW;AAAA;AAAAuI,GAAA,GAF1DJ,cAAc;AAwBpB,MAAMK,mBAAmB,GAAIC,IAAI,IAAK;EAClC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACpC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,SAAS,GAAG,MAAMJ,OAAO,CAACE,MAAM,CAACG,MAAM,CAAC;IAC/CH,MAAM,CAACI,OAAO,GAAGL,MAAM;IACvBC,MAAM,CAACK,aAAa,CAACT,IAAI,CAAC;EAC9B,CAAC,CAAC;AACN,CAAC;AAGD,MAAMU,SAAS,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpB,MAAM7F,QAAQ,GAAGxD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEsJ,OAAO;IAAEC,YAAY;IAAEC,UAAU;IAAEC;EAAa,CAAC,GAAGxJ,WAAW,CAAE2D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EACjG,MAAM6F,YAAY,GAAG3J,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAM4J,gBAAgB,GAAIjB,IAAI,IAAK;IAC/BlF,QAAQ,CAACxC,UAAU,CAAC0H,IAAI,CAAC,CAAC;EAC9B,CAAC;EAED,MAAMkB,eAAe,GAAG,MAAOC,KAAK,IAAK;IACrC,MAAMC,KAAK,GAAGD,KAAK,CAACjF,MAAM,CAACkF,KAAK;IAEhC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IAElC,MAAMC,UAAU,GAAG,MAAMrB,OAAO,CAACsB,GAAG,CAChC/E,KAAK,CAACgF,IAAI,CAACJ,KAAK,CAAC,CAACK,GAAG,CAACzB,IAAI,IAAID,mBAAmB,CAACC,IAAI,CAAC,CAC3D,CAAC;IAEDlF,QAAQ,CAACtC,eAAe,CAAC,CAAC,GAAGuI,YAAY,EAAE,GAAGO,UAAU,CAAC,CAAC,CAAC;EAC/D,CAAC;EAED,MAAMI,SAAS,GAAGA,CAAA,KAAM;IACpB5G,QAAQ,CAACT,iBAAiB,CAACuG,OAAO,CAAC,CAAC;EACxC,CAAC;EAED,MAAMrC,mBAAmB,GAAItC,CAAC,IAAK;IAC/BnB,QAAQ,CAACvC,eAAe,CAAC0D,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC;EAC7C,CAAC;EAED,MAAMoG,iBAAiB,GAAIC,aAAa,IAAK;IACzC,MAAMC,SAAS,GAAGd,YAAY,CAACe,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,aAAa,CAAC;IACpE9G,QAAQ,CAACtC,eAAe,CAACqJ,SAAS,CAAC,CAAC;EACxC,CAAC;EAED,oBACIpH,OAAA;IAAKe,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACpChB,OAAA,CAACd,SAAS;MACN+D,MAAM,EAAEuD,gBAAiB;MACzBrD,YAAY,EAAE,KAAM;MACpBH,OAAO,EAAE;IAAK;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eACFpB,OAAA;MACIwD,OAAO,EAAEyD,SAAU;MACnBO,QAAQ,EAAEnB,UAAU,IAAI,CAACF,OAAQ;MACjCpF,SAAS,EAAC,8JAA8J;MAAAC,QAAA,EAEvKqF,UAAU,gBACPrG,OAAA,CAAAE,SAAA;QAAAc,QAAA,gBACIhB,OAAA;UAAKe,SAAS,EAAC;QAA2D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,8BAErF;MAAA,eAAE,CAAC,gBAEHpB,OAAA,CAAAE,SAAA;QAAAc,QAAA,gBACIhB,OAAA,CAACvB,QAAQ;UAACsC,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kCAEpC;MAAA,eAAE;IACL;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EACRkF,YAAY,CAACM,MAAM,GAAG,CAAC,iBACpB5G,OAAA;MAAKe,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAChChB,OAAA;QAAKe,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjChB,OAAA;UACIqB,IAAI,EAAC,MAAM;UACXoG,QAAQ;UACRC,MAAM,EAAC,SAAS;UAChBC,GAAG,EAAEpB,YAAa;UAClBqB,KAAK,EAAE;YAAEC,OAAO,EAAE;UAAO,CAAE;UAC3BtG,QAAQ,EAAEkF;QAAgB;UAAAxF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEFpB,OAAA;UACIwD,OAAO,EAAEA,CAAA,KAAM+C,YAAY,CAACuB,OAAO,CAACC,KAAK,CAAC,CAAE;UAC5ChH,SAAS,EAAC,iJAAiJ;UAAAC,QAAA,EAC9J;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRkF,YAAY,CAACU,GAAG,CAAC,CAACpE,KAAK,EAAEoF,KAAK,kBAC3BhI,OAAA;UAAiBe,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAEvChB,OAAA;YACIwD,OAAO,EAAEA,CAAA,KAAM0D,iBAAiB,CAACc,KAAK,CAAE;YACxCjH,SAAS,EAAC,qIAAqI;YAC/IkH,KAAK,EAAC,iBAAS;YAAAjH,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGTpB,OAAA;YACIkI,GAAG,EAAEtF,KAAM;YACXuF,GAAG,WAAAC,MAAA,CAAWJ,KAAK,CAAG;YACtBjH,SAAS,EAAC;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA,GAfI4G,KAAK;UAAA/G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBV,CACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAEAgF,YAAY,iBACTpG,OAAA,CAACN,QAAQ;MACLoB,KAAK,EAAEsF,YAAa;MACpB7E,QAAQ,EAAEuC,mBAAoB;MAC9BpC,WAAW,EAAC,gCAAiB;MAC7BwC,KAAK,EAAC;IAAiB;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAGd,CAAC;AAAA8E,GAAA,CA9GKD,SAAS;EAAA,QACMpJ,WAAW,EACgCC,WAAW;AAAA;AAAAuL,GAAA,GAFrEpC,SAAS;AAgHf,MAAMqC,SAAS,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9L,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC+L,IAAI,EAAEC,OAAO,CAAC,GAAGhM,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM;IAAEyJ,YAAY;IAAE3B,iBAAiB;IAAEK,iBAAiB;IAAEK,kBAAkB;IAAET,eAAe;IAAEK,eAAe;IAAEK;EAAiB,CAAC,GAAGtI,WAAW,CAAE2D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC5K,MAAML,QAAQ,GAAGxD,WAAW,CAAC,CAAC;EAC9BH,SAAS,CAAC,MAAM;IACZ,IAAI,CAAC8L,SAAS,EAAE;IAChB,IAAI/D,iBAAiB,CAACmE,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIlE,eAAe,CAACkE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAClEH,YAAY,CAAC,KAAK,CAAC;MACnBE,OAAO,CAAC,IAAI,CAAC;IACjB,CAAC,MAAM,IAAI7D,iBAAiB,CAAC8D,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI7D,eAAe,CAAC6D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACzEH,YAAY,CAAC,KAAK,CAAC;MACnBE,OAAO,CAAC,IAAI,CAAC;IACjB,CAAC,MAAM,IAAIxD,kBAAkB,CAACyD,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIxD,gBAAgB,CAACwD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC3EH,YAAY,CAAC,KAAK,CAAC;MACnBE,OAAO,CAAC,KAAK,CAAC;IAClB;EACJ,CAAC,EAAE,CAAClE,iBAAiB,EAAEC,eAAe,EAAEI,iBAAiB,EAAEC,eAAe,EAAEI,kBAAkB,EAAEC,gBAAgB,CAAC,CAAC;EAElH,oBACIpF,OAAA;IAAKe,SAAS,EAAC,WAAW;IAAAC,QAAA,GACrBwH,SAAS,gBACNxI,OAAA;MAAKe,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAClChB,OAAA,CAACvB,QAAQ;QAACsC,SAAS,EAAC;MAAoC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DpB,OAAA;QAAIe,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxEpB,OAAA;QAAGe,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJpB,OAAA;QAAKe,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtBhB,OAAA,CAACqD,iBAAiB;UACdE,IAAI,EAAC,sCAAsB;UAC3BC,OAAO,EAAEA,CAAA,KAAM;YACXiF,YAAY,CAAC,KAAK,CAAC;YACnBE,OAAO,CAAC,IAAI,CAAC;UACjB;QAAE;UAAA1H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACFpB,OAAA,CAACqD,iBAAiB;UACdE,IAAI,EAAC,iCAAmB;UACxBC,OAAO,EAAEA,CAAA,KAAM;YACXiF,YAAY,CAAC,KAAK,CAAC;YACnBE,OAAO,CAAC,IAAI,CAAC;UACjB;QAAE;UAAA1H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACFpB,OAAA,CAACqD,iBAAiB;UACdE,IAAI,EAAC,4CAAuB;UAC5BC,OAAO,EAAEA,CAAA,KAAM;YACXiF,YAAY,CAAC,KAAK,CAAC;YACnBE,OAAO,CAAC,KAAK,CAAC;UAClB;QAAE;UAAA1H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAENpB,OAAA,CAACZ,WAAW;MACRyJ,IAAI,EAAE,CAAC;QACHC,EAAE,EAAE,CAAC;QACLxH,IAAI,EAAE,aAAa;QACnBR,KAAK,EAAE;MACX,CAAC,EACD;QACIgI,EAAE,EAAE,CAAC;QACLxH,IAAI,EAAE,UAAU;QAChBR,KAAK,EAAE;MACX,CAAC,EACD;QACIgI,EAAE,EAAE,CAAC;QACLxH,IAAI,EAAE,cAAc;QACpBR,KAAK,EAAE;MACX,CAAC,CACC;MACFiI,MAAM,EAAEL,IAAK;MACbM,SAAS,EAAEL;IAAQ;MAAA1H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CACJ,EAEAsH,IAAI,KAAK,IAAI,IAAI,CAACF,SAAS,iBACxBxI,OAAA,CAACuE,aAAa;MAAAtD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACnB,EACAsH,IAAI,KAAK,IAAI,IAAI,CAACF,SAAS,iBACxBxI,OAAA,CAAC4E,aAAa;MAAA3D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACnB,EACAsH,IAAI,KAAK,KAAK,IAAI,CAACF,SAAS,iBACzBxI,OAAA,CAACiF,cAAc;MAAAhE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACpB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAmH,GAAA,CAtFKD,SAAS;EAAA,QAG4HxL,WAAW,EACjID,WAAW;AAAA;AAAAoM,GAAA,GAJ1BX,SAAS;AAwFf,MAAMY,aAAa,GAAGC,KAAA,IAAkC;EAAA,IAAjC;IAAEC,KAAK;IAAEnB,KAAK;IAAEzE,OAAO;IAAE+D;EAAE,CAAC,GAAA4B,KAAA;EAC/C,oBACInJ,OAAA;IAAKe,SAAS,EAAC,iFAAiF;IAAAC,QAAA,gBAC5FhB,OAAA;MAAKe,SAAS,EAAC,mHAAmH;MAAAC,QAAA,GAC7HiH,KAAK,EAAC,GACX;IAAA;MAAAhH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNpB,OAAA;MAAKe,SAAS,EAAC,uDAAuD;MAAAC,QAAA,EACjEe,KAAK,CAACgF,IAAI,CAAC;QAAEH,MAAM,EAAEwC;MAAM,CAAC,CAAC,CAACpC,GAAG,CAAC,CAACM,CAAC,EAAEU,KAAK,kBACxChI,OAAA;QAEIwD,OAAO,EAAEA,CAAA,KAAMA,OAAO,CAACwE,KAAK,CAAE;QAC9BjH,SAAS,uHAAAqH,MAAA,CAAuHb,CAAC,KAAKS,KAAK,GAAG,yBAAyB,GAAG,yBAAyB,eAAa;QAAAhH,QAAA,GAAC,kBACzM,EAACgH,KAAK,GAAG,CAAC;MAAA,GAHbA,KAAK,GAAGC,KAAK;QAAAhH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIjB,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAiI,GAAA,GAlBKH,aAAa;AAoBnB,MAAMI,SAAS,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpB,MAAM;IAAEC,SAAS;IAAEC,aAAa;IAAEf;EAAK,CAAC,GAAG5L,WAAW,CAAE2D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAChF,MAAML,QAAQ,GAAGxD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6M,aAAa,EAAEC,gBAAgB,CAAC,GAAGhN,QAAQ,CAAC;IAC/CiN,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,GAAG,EAAE;EACT,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAM;IAAErJ;EAAM,CAAC,GAAG7D,WAAW,CAAE2D,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EACrD,MAAM,CAACsJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGvN,QAAQ,CAAC,EAAE,CAAC;EAEtDD,SAAS,CAAC,MAAM;IACZ,IAAI8M,SAAS,EAAE;MACX,MAAMW,MAAM,GAAGX,SAAS,CAACY,MAAM,CAC3B,CAACC,GAAG,EAAEC,CAAC,KAAK;QACR,MAAMjJ,IAAI,GAAGiJ,CAAC,CAACC,YAAY,CAACC,cAAc;QAC1C,IAAIH,GAAG,CAAChJ,IAAI,CAAC,KAAKoJ,SAAS,EAAEJ,GAAG,CAAChJ,IAAI,CAAC,EAAE;QACxC,OAAOgJ,GAAG;MACd,CAAC,EACD;QAAET,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAC3B,CAAC;MAEDH,gBAAgB,CAACQ,MAAM,CAAC;IAC5B;EACJ,CAAC,EAAE,CAACX,SAAS,CAAC,CAAC;EAGf9M,SAAS,CAAC,MAAM;IACZ,IAAIqF,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MAAA,IAAA+J,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACjC,IAAI,CAAAH,qBAAA,GAAAlB,SAAS,CAACC,aAAa,CAAC,cAAAiB,qBAAA,gBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BH,YAAY,cAAAI,sBAAA,eAAtCA,sBAAA,CAAwC1I,KAAK,IAAI,EAAA2I,sBAAA,GAAApB,SAAS,CAACC,aAAa,CAAC,cAAAmB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BL,YAAY,cAAAM,sBAAA,uBAAtCA,sBAAA,CAAwC5I,KAAK,CAAC2G,IAAI,CAAC,CAAC,MAAK,EAAE,EAAE;QAC9GsB,gBAAgB,CACZvJ,KAAK,CAAC,SAAS,CAAC,CAAC0G,MAAM,CAAEyD,IAAI;UAAA,IAAAC,sBAAA,EAAAC,sBAAA;UAAA,OAAKF,IAAI,CAACA,IAAI,CAACG,UAAU,EAAAF,sBAAA,GAACvB,SAAS,CAACC,aAAa,CAAC,cAAAsB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BR,YAAY,cAAAS,sBAAA,uBAAtCA,sBAAA,CAAwC/I,KAAK,CAAC,IAAI6I,IAAI,CAACA,IAAI,CAAClE,MAAM,KAAK,CAAC;QAAA,EACnI,CAAC;MACL,CAAC,MAAM;QACHsD,gBAAgB,CAACvJ,KAAK,CAAC,SAAS,CAAC,CAAC0G,MAAM,CAAEyD,IAAI,IAAKA,IAAI,CAACA,IAAI,CAAClE,MAAM,KAAK,CAAC,CAAC,CAAC;MAC/E;IACJ,CAAC,MAAM;MACHsD,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAACvJ,KAAK,EAAE6I,SAAS,EAAEC,aAAa,CAAC,CAAC;EAErC,MAAMyB,oBAAoB,GAAGA,CAAC1J,CAAC,EAAEX,KAAK,KAAK;IACvC,MAAMsK,YAAY,GAAG3B,SAAS,CAACxC,GAAG,CAAC,CAACoE,QAAQ,EAAEC,MAAM,KAAK;MACrD,IAAIA,MAAM,KAAK5B,aAAa,EAAE;QAC1B,OAAO;UACH,GAAG2B,QAAQ;UACXb,YAAY,EAAE;YACV,GAAGa,QAAQ,CAACb,YAAY;YACxB,CAAC1J,KAAK,GAAGW,CAAC,CAACC,MAAM,CAACX;UACtB;QACJ,CAAC;MACL;MACA,OAAOsK,QAAQ;IACnB,CAAC,CAAC;IACF/K,QAAQ,CAAC1C,YAAY,CAACwN,YAAY,CAAC,CAAC;EACxC,CAAC;EACD,MAAMG,4BAA4B,GAAIC,WAAW,IAAK;IAClD,MAAMJ,YAAY,GAAG3B,SAAS,CAACxC,GAAG,CAAC,CAACoE,QAAQ,EAAEC,MAAM,KAAK;MACrD,IAAIA,MAAM,KAAK5B,aAAa,EAAE;QAC1B,OAAO;UACH,GAAG2B,QAAQ;UACXb,YAAY,EAAE;YACV,GAAGa,QAAQ,CAACb,YAAY;YACxBiB,QAAQ,EAAED;UACd;QACJ,CAAC;MACL;MACA,OAAOH,QAAQ;IACnB,CAAC,CAAC;IACF/K,QAAQ,CAAC1C,YAAY,CAACwN,YAAY,CAAC,CAAC;EACxC,CAAC;EAED,MAAMM,qBAAqB,GAAGA,CAACzD,KAAK,EAAElH,KAAK,EAAED,KAAK,KAAK;IACnD,MAAMsK,YAAY,GAAG3B,SAAS,CAACxC,GAAG,CAAC,CAACoE,QAAQ,EAAEC,MAAM,KAAK;MACrD,IAAIA,MAAM,KAAK5B,aAAa,EAAE;QAC1B,OAAO;UACH,GAAG2B,QAAQ;UACXM,UAAU,EAAEN,QAAQ,CAACM,UAAU,CAAC1E,GAAG,CAAC,CAAC2E,IAAI,EAAEC,MAAM,KAAK;YAClD,IAAIA,MAAM,KAAK5D,KAAK,EAAE;cAClB,OAAO;gBAAE,GAAG2D,IAAI;gBAAE,CAAC9K,KAAK,GAAGC;cAAM,CAAC;YACtC;YACA,OAAO6K,IAAI;UACf,CAAC;QACL,CAAC;MACL;MACA,OAAOP,QAAQ;IACnB,CAAC,CAAC;IACF/K,QAAQ,CAAC1C,YAAY,CAACwN,YAAY,CAAC,CAAC;EACxC,CAAC;EAGD,oBACInL,OAAA;IAAKe,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACjChB,OAAA,CAACkJ,aAAa;MAACE,KAAK,EAAEM,aAAa,CAACE,EAAG;MAAC3B,KAAK,EAAE,aAAc;MAACzE,OAAO,EAAGwE,KAAK,IAAK3H,QAAQ,CAACzC,gBAAgB,CAACoK,KAAK,CAAC,CAAE;MAACT,CAAC,EAAEkC;IAAc;MAAAxI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzIpB,OAAA,CAACkJ,aAAa;MAACE,KAAK,EAAEM,aAAa,CAACG,EAAG;MAAC5B,KAAK,EAAE,UAAW;MAACzE,OAAO,EAAGwE,KAAK,IAAK3H,QAAQ,CAACzC,gBAAgB,CAACoK,KAAK,GAAG0B,aAAa,CAACE,EAAE,CAAC,CAAE;MAACrC,CAAC,EAAEkC,aAAa,GAAGC,aAAa,CAACE;IAAG;MAAA3I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC5KpB,OAAA,CAACkJ,aAAa;MAACE,KAAK,EAAEM,aAAa,CAACI,GAAI;MAAC7B,KAAK,EAAE,cAAe;MAACzE,OAAO,EAAGwE,KAAK,IAAK3H,QAAQ,CAACzC,gBAAgB,CAACoK,KAAK,GAAG0B,aAAa,CAACG,EAAE,GAAGH,aAAa,CAACE,EAAE,CAAC,CAAE;MAACrC,CAAC,EAAEkC,aAAa,IAAIC,aAAa,CAACG,EAAE,GAAGH,aAAa,CAACE,EAAE;IAAE;MAAA3I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAExNoI,SAAS,IAAIA,SAAS,CAACC,aAAa,CAAC,iBAClCzJ,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BhB,OAAA;QAAKe,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChChB,OAAA;UAAIe,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEpB,OAAA;UAAKe,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAChChB,OAAA,CAAChC,gBAAgB;YACb2D,cAAc,EAAE6H,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACtI,KAAM;YAC5DV,QAAQ,EAAGM,MAAM,IAAKqJ,oBAAoB,CAAC;cAAEzJ,MAAM,EAAE;gBAAEX,KAAK,EAAEe;cAAO;YAAE,CAAC,EAAE,OAAO,CAAE;YACnFC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;YAC7DI,SAAS,EAAC,SAAS;YACnBW,WAAW,EAAC;UAAU;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACFpB,OAAA,CAAC/B,oBAAoB;YACjB0D,cAAc,EAAE6H,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAAClI,OAAQ;YAC9Dd,QAAQ,EAAGM,MAAM,IAAKqJ,oBAAoB,CAAC;cAAEzJ,MAAM,EAAE;gBAAEX,KAAK,EAAEe;cAAO;YAAE,CAAC,EAAE,SAAS,CAAE;YACrFC,OAAO,EAAEmI,aAAc;YACvBlJ,SAAS,EAAC,SAAS;YACnBW,WAAW,EAAC;UAAa;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACFpB,OAAA,CAAChC,gBAAgB;YACb2D,cAAc,EAAE6H,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACsB,UAAW;YACjEtK,QAAQ,EAAGM,MAAM,IAAKqJ,oBAAoB,CAAC;cAAEzJ,MAAM,EAAE;gBAAEX,KAAK,EAAEe;cAAO;YAAE,CAAC,EAAE,YAAY,CAAE;YACxFC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;YACvEI,SAAS,EAAC,SAAS;YACnBW,WAAW,EAAC;UAAa;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNpB,OAAA;QAAIe,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClCpB,OAAA;QAAKe,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChChB,OAAA;UAAIe,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EpB,OAAA;UAAKe,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBhB,OAAA,CAACN,QAAQ;YACLoB,KAAK,EAAE0I,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACuB,OAAQ;YACrDvK,QAAQ,EAAGC,CAAC,IAAK0J,oBAAoB,CAAC1J,CAAC,EAAE,SAAS,CAAE;YACpDE,WAAW,EAAC,yCAAuB;YACnCwC,KAAK,EAAC;UAAS;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,EACD,CAACsH,IAAI,KAAK,OAAO,IAAIc,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACwB,QAAQ,kBAChE/L,OAAA,CAACP,aAAa;YACVsM,QAAQ,EAAEvC,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACwB,QAAS;YACzDC,WAAW,EAAGpJ,KAAK,IAAKsI,oBAAoB,CAAC;cAAEzJ,MAAM,EAAE;gBAAEX,KAAK,EAAE8B;cAAM;YAAE,CAAC,EAAE,UAAU,CAAE;YACvFqJ,aAAa,EAAEA,CAAA,KAAMf,oBAAoB,CAAC;cAAEzJ,MAAM,EAAE;gBAAEX,KAAK,EAAE;cAAG;YAAE,CAAC,EAAE,UAAU;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CACJ,EACAoI,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACC,cAAc,KAAK,KAAK,iBAC3DxK,OAAA;YAAKe,SAAS,EAAC,WAAW;YAAAC,QAAA,EACrBwI,SAAS,CAACC,aAAa,CAAC,CAACiC,UAAU,CAAC1E,GAAG,CAAC,CAACkF,SAAS,EAAElE,KAAK,kBACtDhI,OAAA;cAAiBe,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBAChEhB,OAAA;gBAAKe,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBACpDhB,OAAA;kBAAGe,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAC7CwI,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACC,cAAc,KAAK,IAAI,GAAGT,QAAQ,CAAC/B,KAAK,CAAC,GAAGgC,QAAQ,CAAChC,KAAK;gBAAC;kBAAA/G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,eACJpB,OAAA,CAACN,QAAQ;kBACLoB,KAAK,EAAEoL,SAAS,CAACJ,OAAQ;kBACzBvK,QAAQ,EAAGC,CAAC,IAAKiK,qBAAqB,CAACzD,KAAK,EAAExG,CAAC,CAACC,MAAM,CAACX,KAAK,EAAE,SAAS,CAAE;kBACzEY,WAAW,EAAC;gBAAuB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACL,CAACsH,IAAI,KAAK,OAAO,IAAIwD,SAAS,CAACH,QAAQ,kBACpC/L,OAAA,CAACP,aAAa;gBACVsM,QAAQ,EAAEG,SAAS,CAACH,QAAS;gBAC7BC,WAAW,EAAGpJ,KAAK,IAAK6I,qBAAqB,CAACzD,KAAK,EAAEpF,KAAK,EAAE,UAAU,CAAE;gBACxEqJ,aAAa,EAAEA,CAAA,KAAMR,qBAAqB,CAACzD,KAAK,EAAE,EAAE,EAAE,UAAU;cAAE;gBAAA/G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CACJ;YAAA,GAjBK4G,KAAK;cAAA/G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EACAoI,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACC,cAAc,KAAK,KAAK,iBAC3DxK,OAAA;YAAKe,SAAS,EAAC,WAAW;YAAAC,QAAA,eACtBhB,OAAA,CAACN,QAAQ;cACLoB,KAAK,EAAE0I,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAAC4B,aAAc;cAC3D5K,QAAQ,EAAGC,CAAC,IAAK0J,oBAAoB,CAAC1J,CAAC,EAAE,eAAe,CAAE;cAC1DE,WAAW,EAAC,6BAAa;cACzBwC,KAAK,EAAC,mBAAQ;cACdC,IAAI,EAAEzF;YAAY;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,eAQDpB,OAAA,CAACX,cAAc;YACXmM,QAAQ,EAAEhC,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACiB,QAAS;YACzDY,gBAAgB,EAAEd;UAA6B;YAAArK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAmI,GAAA,CAvMKD,SAAS;EAAA,QACgCxM,WAAW,EACrCD,WAAW,EAQVC,WAAW;AAAA;AAAAuP,IAAA,GAV3B/C,SAAS;AA0Mf,MAAMgD,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtB,MAAMlM,QAAQ,GAAGxD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2P,IAAI;IAAElM,QAAQ;IAAEmM,OAAO;IAAElM,SAAS;IAAEC,QAAQ;IAAEgJ;EAAU,CAAC,GAAG1M,WAAW,CAAE2D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EACzG,MAAMgM,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIF,IAAI,GAAG,CAAC,EAAEnM,QAAQ,CAACpD,QAAQ,CAAC,CAAC,CAAC;EACtC,CAAC;EAED,MAAM0P,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIH,IAAI,GAAG,CAAC,EAAEnM,QAAQ,CAACnD,QAAQ,CAAC,CAAC,CAAC;EACtC,CAAC;EAED,MAAM0P,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACrN,gBAAgB,CAACe,QAAQ,EAAED,QAAQ,CAAC,EAAE;IAE3C,MAAMA,QAAQ,CAACrD,QAAQ,CAAC;MACpBsD,QAAQ;MACRC,SAAS;MACTiJ,SAAS,EAAEA,SAAS,IAAI,EAAE;MAC1BhJ;IACJ,CAAC,CAAC,CAAC,CAACqM,MAAM,CAAC,CAAC;IACZ;EACJ,CAAC;EAED,oBACI7M,OAAA;IAAKe,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBAEjDhB,OAAA,CAAC9B,iBAAiB;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGrBpB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,GAElCwL,IAAI,KAAK,CAAC,iBACPxM,OAAA,CAACG,SAAS;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACf,EAEAoL,IAAI,KAAK,CAAC,iBACPxM,OAAA,CAACiG,SAAS;QAAAhF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACf,EAGAoL,IAAI,KAAK,CAAC,iBACPxM,OAAA,CAACsI,SAAS;QAAArH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACf,EAGAoL,IAAI,KAAK,CAAC,iBACPxM,OAAA,CAACsJ,SAAS;QAAArI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACf;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eAClDhB,OAAA;QAAKe,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAC9ChB,OAAA;UACIwD,OAAO,EAAEmJ,UAAW;UACpBnF,QAAQ,EAAEgF,IAAI,KAAK,CAAE;UACrBzL,SAAS,EAAC,6HAA6H;UAAAC,QAAA,gBAEvIhB,OAAA,CAACpB,WAAW;YAACmC,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERoL,IAAI,GAAG,CAAC,gBACLxM,OAAA;UACIwD,OAAO,EAAEkJ,UAAW;UACpB3L,SAAS,EAAC,8GAA8G;UAAAC,QAAA,GAC3H,gBAEG,eAAAhB,OAAA,CAACrB,YAAY;YAACoC,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,gBAETpB,OAAA;UACIwD,OAAO,EAAEoJ,YAAa;UACtBpF,QAAQ,EAAEiF,OAAQ;UAClB1L,SAAS,EAAC,oIAAoI;UAAAC,QAAA,EAE7IyL,OAAO,gBACJzM,OAAA,CAAAE,SAAA;YAAAc,QAAA,gBACIhB,OAAA,CAACb,cAAc;cAAC2N,SAAS,EAAC;YAAS;cAAA7L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAE1C;UAAA,eAAE,CAAC,gBAEHpB,OAAA,CAAAE,SAAA;YAAAc,QAAA,gBACIhB,OAAA,CAAClB,IAAI;cAACiC,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6BAEhC;UAAA,eAAE;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEf,CAAC;AAACmL,GAAA,CA7FID,WAAW;EAAA,QACIzP,WAAW,EACwCC,WAAW;AAAA;AAAAiQ,IAAA,GAF7ET,WAAW;AA+FjB,eAAeA,WAAW;AAAC,IAAAlJ,EAAA,EAAAK,GAAA,EAAAa,GAAA,EAAAK,GAAA,EAAAK,GAAA,EAAAK,GAAA,EAAAgD,GAAA,EAAAY,GAAA,EAAAI,GAAA,EAAAgD,IAAA,EAAAU,IAAA;AAAAC,YAAA,CAAA5J,EAAA;AAAA4J,YAAA,CAAAvJ,GAAA;AAAAuJ,YAAA,CAAA1I,GAAA;AAAA0I,YAAA,CAAArI,GAAA;AAAAqI,YAAA,CAAAhI,GAAA;AAAAgI,YAAA,CAAA3H,GAAA;AAAA2H,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAA/D,GAAA;AAAA+D,YAAA,CAAA3D,GAAA;AAAA2D,YAAA,CAAAX,IAAA;AAAAW,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}