{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport TextArea from \"src/components/input/TextArea\";\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\nimport SolutionEditor from \"src/components/PageAddExam/SolutionEditor\";\nimport { setQuestion } from \"src/features/questionsExam/questionsExamSlice\";\nimport { CheckCircle, Plus, Video, Sparkles, Trash2 } from \"lucide-react\";\nimport { setQuestions, setNewQuestion, addQuestion } from \"src/features/questionsExam/questionsExamSlice\";\nimport { addStatement } from \"src/features/questionsExam/questionsExamSlice\";\nimport { fixTextAndLatex } from \"src/features/ai/aiSlice\";\nimport { setErrorMessage } from \"src/features/state/stateApiSlice\";\nimport YouTubePlayer from \"../YouTubePlayer\";\n\n// Component TextArea với nút AI\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TextAreaWithAI = _ref => {\n  _s();\n  let {\n    value,\n    onChange,\n    placeholder,\n    label,\n    Icon,\n    row = false,\n    short = false\n  } = _ref;\n  const dispatch = useDispatch();\n  const [loading, setLoading] = useState(false);\n  const handleAIFix = async () => {\n    if (!value || !value.trim()) {\n      alert('Vui lòng nhập nội dung trước khi sử dụng AI sửa lỗi');\n      return;\n    }\n    try {\n      setLoading(true);\n      const result = await dispatch(fixTextAndLatex(value)).unwrap();\n      if (result.data.hasChanges) {\n        onChange({\n          target: {\n            value: result.data.fixedText\n          }\n        });\n      } else {\n        alert('Không tìm thấy lỗi nào cần sửa');\n      }\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fixing text:', error);\n      alert('Có lỗi xảy ra khi sử dụng AI sửa lỗi');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex gap-2 \".concat(row ? 'flex-row-reverse' : 'flex-col'),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"flex items-center gap-1 text-sm font-medium text-gray-700\",\n        children: Icon && /*#__PURE__*/_jsxDEV(Icon, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 30\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAIFix,\n        disabled: loading || !(value !== null && value !== void 0 && value.trim()),\n        className: \"flex items-center gap-1 px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n        title: \"S\\u1EED d\\u1EE5ng AI \\u0111\\u1EC3 s\\u1EEDa ch\\xEDnh t\\u1EA3 v\\xE0 k\\xFD hi\\u1EC7u LaTeX\",\n        children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n          className: \"w-3 h-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 21\n        }, this), !short && (loading ? 'Đang sửa...' : 'AI Fix')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n      value: value,\n      onChange: onChange,\n      placeholder: placeholder\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 9\n  }, this);\n};\n_s(TextAreaWithAI, \"G3V0PFzKUm/QgxbjQ7d4LdD51Z8=\", false, function () {\n  return [useDispatch];\n});\n_c = TextAreaWithAI;\nconst DetailQuestionView = () => {\n  _s2();\n  const {\n    questionsExam,\n    selectedId,\n    view\n  } = useSelector(state => state.questionsExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const dispatch = useDispatch();\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const [optionChapter, setOptionChapter] = useState([]);\n  const [question, setQuestion] = useState(null);\n  useEffect(() => {\n    // console.log(selectedId);\n\n    if (selectedId && (questionsExam === null || questionsExam === void 0 ? void 0 : questionsExam.length) > 0) {\n      const selectedQuestion = questionsExam === null || questionsExam === void 0 ? void 0 : questionsExam.find(q => q.id === selectedId);\n      setQuestion(selectedQuestion);\n    }\n  }, [selectedId, questionsExam]);\n  const handleQuestionChange = (e, field) => {\n    const updatedQuestion = {\n      ...question,\n      [field]: e.target.value\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleStatementChange = (index, value, field) => {\n    const updatedStatements = [...question.statements];\n    updatedStatements[index] = {\n      ...updatedStatements[index],\n      [field]: value\n    };\n    const updatedQuestion = {\n      ...question,\n      statements: updatedStatements\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleSolutionQuestionChange = value => {\n    const updatedQuestion = {\n      ...question,\n      solution: value\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleAddStatement = () => {\n    dispatch(addStatement());\n  };\n  const handleRemoveStatement = index => {\n    const updatedStatements = [...question.statements];\n    updatedStatements.splice(index, 1);\n    const updatedQuestion = {\n      ...question,\n      statements: updatedStatements\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      if (question !== null && question !== void 0 && question.class && (question === null || question === void 0 ? void 0 : question.class.trim()) !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.startsWith(question === null || question === void 0 ? void 0 : question.class) && code.code.length === 5));\n      } else {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, question === null || question === void 0 ? void 0 : question.class]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3 w-full\",\n    children: question && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Ph\\xE2n lo\\u1EA1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-row gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: question.class,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'class'),\n            options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n            selectedOption: question.chapter,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'chapter'),\n            options: optionChapter,\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: question.difficulty,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'difficulty'),\n            options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \" bg-gray-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Th\\xF4ng tin c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(TextAreaWithAI, {\n            value: question.content,\n            onChange: e => handleQuestionChange(e, 'content'),\n            placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n            label: \"C\\xE2u h\\u1ECFi\",\n            row: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 29\n          }, this), (view === 'image' || question.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n            imageUrl: question.imageUrl,\n            onImageDrop: image => handleQuestionChange({\n              target: {\n                value: image\n              }\n            }, 'imageUrl'),\n            onImageRemove: () => handleQuestionChange({\n              target: {\n                value: ''\n              }\n            }, 'imageUrl')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 33\n          }, this), question.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [question.statements.length < 4 && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleAddStatement(),\n              className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-blue-600 hover:bg-blue-700 text-white\",\n              children: [/*#__PURE__*/_jsxDEV(Plus, {\n                className: \"w-3 h-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 45\n              }, this), \" Th\\xEAm m\\u1EC7nh \\u0111\\u1EC1\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 41\n            }, this), question.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-2 items-center w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row gap-2 items-center w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs font-bold whitespace-nowrap\",\n                  children: question.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(TextAreaWithAI, {\n                  value: statement.content,\n                  onChange: e => handleStatementChange(index, e.target.value, 'content'),\n                  placeholder: \"Nh\\u1EADp n\\u1ED9i dung m\\u1EC7nh \\u0111\\u1EC1\",\n                  short: true,\n                  row: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 49\n                }, this), question.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"flex items-center gap-2 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"statement-\".concat(index),\n                      checked: statement.isCorrect,\n                      onChange: () => handleStatementChange(index, true, 'isCorrect'),\n                      className: \"w-4 h-4 text-green-600 focus:ring-green-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 197,\n                      columnNumber: 61\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-green-600 font-medium\",\n                      children: \"\\u0110\\xFAng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 57\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"flex items-center gap-2 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"statement-\".concat(index),\n                      checked: !statement.isCorrect,\n                      onChange: () => handleStatementChange(index, false, 'isCorrect'),\n                      className: \"w-4 h-4 text-red-600 focus:ring-red-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 61\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-red-600 font-medium\",\n                      children: \"Sai\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 57\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleRemoveStatement(index),\n                  className: \"text-xs flex items-center p-1 rounded-full bg-red-50 hover:bg-red-200 text-red-600\",\n                  children: /*#__PURE__*/_jsxDEV(Trash2, {\n                    className: \"w-3 h-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 53\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 45\n              }, this), (view === 'image' || statement.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n                imageUrl: statement.imageUrl,\n                onImageDrop: image => handleStatementChange(index, image, 'imageUrl'),\n                onImageRemove: () => handleStatementChange(index, '', 'imageUrl')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 49\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 41\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 33\n          }, this), question.typeOfQuestion === 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              value: question.correctAnswer,\n              onChange: e => handleQuestionChange(e, 'correctAnswer'),\n              placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n              label: \"\\u0110\\xE1p \\xE1n\",\n              Icon: CheckCircle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n            value: question.solutionUrl || '',\n            onChange: e => handleQuestionChange(e, 'solutionUrl'),\n            placeholder: \"Nh\\u1EADp link l\\u1EDDi gi\\u1EA3i youtube\",\n            label: \"Link l\\u1EDDi gi\\u1EA3i\",\n            Icon: Video\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 29\n          }, this), question.solutionUrl && /*#__PURE__*/_jsxDEV(YouTubePlayer, {\n            url: question.solutionUrl\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(SolutionEditor, {\n            solution: question.solution,\n            onSolutionChange: handleSolutionQuestionChange,\n            preview: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 9\n  }, this);\n};\n_s2(DetailQuestionView, \"DXB6iz/PdnPpihu4Y8AFD12VQkc=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c2 = DetailQuestionView;\nconst AddQuestionView = () => {\n  _s3();\n  const dispatch = useDispatch();\n  const {\n    newQuestion\n  } = useSelector(state => state.questionsExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const [optionChapter, setOptionChapter] = useState([]);\n  const handleNewQuestionChange = (e, field) => {\n    const updatedQuestion = {\n      ...newQuestion,\n      [field]: e.target.value\n    };\n    dispatch(setNewQuestion(updatedQuestion));\n  };\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      if (newQuestion !== null && newQuestion !== void 0 && newQuestion.class && (newQuestion === null || newQuestion === void 0 ? void 0 : newQuestion.class.trim()) !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.startsWith(newQuestion === null || newQuestion === void 0 ? void 0 : newQuestion.class) && code.code.length === 5));\n      } else {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, newQuestion === null || newQuestion === void 0 ? void 0 : newQuestion.class]);\n  const handleNewStatementChange = (index, value, field) => {\n    const updatedStatements = [...newQuestion.statements];\n    updatedStatements[index] = {\n      ...updatedStatements[index],\n      [field]: value\n    };\n    const updatedQuestion = {\n      ...newQuestion,\n      statements: updatedStatements\n    };\n    dispatch(setNewQuestion(updatedQuestion));\n  };\n  const handleNewSolutionQuestionChange = value => {\n    const updatedQuestion = {\n      ...newQuestion,\n      solution: value\n    };\n    dispatch(setNewQuestion(updatedQuestion));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3 w-full\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-medium text-gray-900 mb-1\",\n        children: \"Ph\\xE2n lo\\u1EA1i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: newQuestion.typeOfQuestion,\n          onChange: option => handleNewQuestionChange({\n            target: {\n              value: option\n            }\n          }, 'typeOfQuestion'),\n          options: [{\n            code: \"TN\",\n            description: \"Trắc nghiệm\"\n          }, {\n            code: \"DS\",\n            description: \"Đúng sai\"\n          }, {\n            code: \"TLN\",\n            description: \"Trả lời ngắn\"\n          }],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: newQuestion.class,\n          onChange: option => handleNewQuestionChange({\n            target: {\n              value: option\n            }\n          }, 'class'),\n          options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n          selectedOption: newQuestion.chapter,\n          onChange: option => handleNewQuestionChange({\n            target: {\n              value: option\n            }\n          }, 'chapter'),\n          options: optionChapter,\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: newQuestion.difficulty,\n          onChange: option => handleNewQuestionChange({\n            target: {\n              value: option\n            }\n          }, 'difficulty'),\n          options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \" bg-gray-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Th\\xF4ng tin c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(TextAreaWithAI, {\n            value: newQuestion.content,\n            onChange: e => handleNewQuestionChange(e, 'content'),\n            placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n            label: \"C\\xE2u h\\u1ECFi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(ImageDropZone, {\n            imageUrl: newQuestion.imageUrl,\n            onImageDrop: image => handleNewQuestionChange({\n              target: {\n                value: image\n              }\n            }, 'imageUrl'),\n            onImageRemove: () => handleNewQuestionChange({\n              target: {\n                value: ''\n              }\n            }, 'imageUrl')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 25\n          }, this), newQuestion.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: newQuestion.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-2 items-center w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row gap-2 items-center w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs font-bold whitespace-nowrap\",\n                  children: newQuestion.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(TextAreaWithAI, {\n                  value: statement.content,\n                  onChange: e => handleNewStatementChange(index, e.target.value, 'content'),\n                  placeholder: \"Nh\\u1EADp n\\u1ED9i dung m\\u1EC7nh \\u0111\\u1EC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"flex items-center gap-2 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"statement-\".concat(index),\n                      checked: statement.isCorrect,\n                      onChange: () => handleNewStatementChange(index, true, 'isCorrect'),\n                      className: \"w-4 h-4 text-green-600 focus:ring-green-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-green-600 font-medium\",\n                      children: \"\\u0110\\xFAng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 384,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"flex items-center gap-2 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"statement-\".concat(index),\n                      checked: !statement.isCorrect,\n                      onChange: () => handleNewStatementChange(index, false, 'isCorrect'),\n                      className: \"w-4 h-4 text-red-600 focus:ring-red-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 387,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-red-600 font-medium\",\n                      children: \"Sai\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(ImageDropZone, {\n                imageUrl: statement.imageUrl,\n                onImageDrop: image => handleNewStatementChange(index, image, 'imageUrl'),\n                onImageRemove: () => handleNewStatementChange(index, '', 'imageUrl')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 41\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 29\n          }, this), newQuestion.typeOfQuestion === 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              value: newQuestion.correctAnswer,\n              onChange: e => handleNewQuestionChange(e, 'correctAnswer'),\n              placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n              label: \"\\u0110\\xE1p \\xE1n\",\n              Icon: CheckCircle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(TextArea, {\n          value: newQuestion.solutionUrl,\n          onChange: e => handleNewQuestionChange(e, 'solutionUrl'),\n          placeholder: \"Nh\\u1EADp link l\\u1EDDi gi\\u1EA3i youtube\",\n          label: \"Link l\\u1EDDi gi\\u1EA3i\",\n          Icon: Video\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 21\n        }, this), newQuestion.solutionUrl && /*#__PURE__*/_jsxDEV(YouTubePlayer, {\n          url: newQuestion.solutionUrl\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(SolutionEditor, {\n          solution: newQuestion.solution,\n          onSolutionChange: handleNewSolutionQuestionChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 318,\n    columnNumber: 9\n  }, this);\n};\n_s3(AddQuestionView, \"R7UojSO0cGUgJuTtpZmH3ejLoVQ=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c3 = AddQuestionView;\nconst LeftContent = () => {\n  _s4();\n  const [view, setView] = useState('questionDetail');\n  const {\n    newQuestion\n  } = useSelector(state => state.questionsExam);\n  const dispatch = useDispatch();\n  const handleAddQuestion = () => {\n    if (!newQuestion.content.trim()) {\n      dispatch(setErrorMessage(\"Nội dung câu hỏi không được để trống!\"));\n      return;\n    }\n    if (!newQuestion.typeOfQuestion) {\n      dispatch(setErrorMessage(\"Loại câu hỏi không được để trống!\"));\n      return;\n    }\n    if (!newQuestion.class) {\n      dispatch(setErrorMessage(\"Lớp không được để trống!\"));\n      return;\n    }\n    if (newQuestion.typeOfQuestion !== 'TLN' && newQuestion.statements.filter(statement => statement.content.trim() !== \"\").length < 4) {\n      dispatch(setErrorMessage(\"Câu hỏi TN phải có ít nhất 4 đáp án!\"));\n      return;\n    }\n    if (newQuestion.typeOfQuestion === 'TLN' && !newQuestion.correctAnswer.trim()) {\n      dispatch(setErrorMessage(\"Đáp án không được để trống!\"));\n      return;\n    }\n    dispatch(addQuestion());\n    setView('questionDetail');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_138px)] overflow-y-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xs font-semibold text-gray-900\",\n          children: \"Chi ti\\u1EBFt c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: view === 'questionDetail' ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setView('addQuestion'),\n          className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-sky-600 hover:bg-sky-700 text-white\",\n          children: \"Th\\xEAm c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddQuestion,\n            className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-emerald-600 hover:bg-emerald-700 text-white\",\n            children: \"L\\u01B0u\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setView('questionDetail'),\n            className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-red-600 hover:bg-red-700 text-white\",\n            children: \"H\\u1EE7y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 13\n    }, this), view === 'questionDetail' && /*#__PURE__*/_jsxDEV(DetailQuestionView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 43\n    }, this), view === 'addQuestion' && /*#__PURE__*/_jsxDEV(AddQuestionView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 510,\n      columnNumber: 40\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 475,\n    columnNumber: 9\n  }, this);\n};\n_s4(LeftContent, \"NcpmA1bQnPPdje5SWgr/AuDkEK4=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c4 = LeftContent;\nexport default LeftContent;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"TextAreaWithAI\");\n$RefreshReg$(_c2, \"DetailQuestionView\");\n$RefreshReg$(_c3, \"AddQuestionView\");\n$RefreshReg$(_c4, \"LeftContent\");", "map": {"version": 3, "names": ["useEffect", "useState", "useSelector", "useDispatch", "DropMenuBarAdmin", "SuggestInputBarAdmin", "TextArea", "ImageDropZone", "SolutionEditor", "setQuestion", "CheckCircle", "Plus", "Video", "<PERSON><PERSON><PERSON>", "Trash2", "setQuestions", "setNewQuestion", "addQuestion", "addStatement", "fixTextAndLatex", "setErrorMessage", "YouTubePlayer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TextAreaWithAI", "_ref", "_s", "value", "onChange", "placeholder", "label", "Icon", "row", "short", "dispatch", "loading", "setLoading", "handleAIFix", "trim", "alert", "result", "unwrap", "data", "has<PERSON><PERSON><PERSON>", "target", "fixedText", "error", "console", "className", "concat", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "title", "_c", "DetailQuestionView", "_s2", "questionsExam", "selectedId", "view", "state", "codes", "prefixTN", "prefixDS", "optionChapter", "setOptionChapter", "question", "length", "selectedQuestion", "find", "q", "id", "handleQuestionChange", "e", "field", "updatedQuestion", "handleStatementChange", "index", "updatedStatements", "statements", "handleSolutionQuestionChange", "solution", "handleAddStatement", "handleRemoveStatement", "splice", "Array", "isArray", "class", "filter", "code", "startsWith", "selectedOption", "option", "options", "chapter", "difficulty", "content", "imageUrl", "onImageDrop", "image", "onImageRemove", "typeOfQuestion", "map", "statement", "type", "name", "checked", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "solutionUrl", "url", "onSolutionChange", "preview", "_c2", "AddQuestionView", "_s3", "newQuestion", "handleNewQuestionChange", "handleNewStatementChange", "handleNewSolutionQuestionChange", "description", "_c3", "LeftContent", "_s4", "<PERSON><PERSON><PERSON><PERSON>", "handleAddQuestion", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/LeftContent.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport TextArea from \"src/components/input/TextArea\";\r\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\r\nimport SolutionEditor from \"src/components/PageAddExam/SolutionEditor\";\r\nimport { setQuestion } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport { CheckCircle, Plus, Video, Sparkles, Trash2 } from \"lucide-react\";\r\nimport { setQuestions, setNewQuestion, addQuestion } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport { addStatement } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport { fixTextAndLatex } from \"src/features/ai/aiSlice\";\r\nimport { setErrorMessage } from \"src/features/state/stateApiSlice\";\r\nimport YouTubePlayer from \"../YouTubePlayer\";\r\n\r\n// Component TextArea với nút AI\r\nconst TextAreaWithAI = ({ value, onChange, placeholder, label, Icon, row = false, short = false }) => {\r\n    const dispatch = useDispatch();\r\n    const [loading, setLoading] = useState(false);\r\n    const handleAIFix = async () => {\r\n        if (!value || !value.trim()) {\r\n            alert('Vui lòng nhập nội dung trước khi sử dụng AI sửa lỗi');\r\n            return;\r\n        }\r\n\r\n        try {\r\n            setLoading(true);\r\n            const result = await dispatch(fixTextAndLatex(value)).unwrap();\r\n            if (result.data.hasChanges) {\r\n                onChange({ target: { value: result.data.fixedText } });\r\n            } else {\r\n                alert('Không tìm thấy lỗi nào cần sửa');\r\n            }\r\n            setLoading(false);\r\n        } catch (error) {\r\n            console.error('Error fixing text:', error);\r\n            alert('Có lỗi xảy ra khi sử dụng AI sửa lỗi');\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className={`flex gap-2 ${row ? 'flex-row-reverse' : 'flex-col'}`}>\r\n            <div className=\"flex items-center justify-between\">\r\n                <label className=\"flex items-center gap-1 text-sm font-medium text-gray-700\">\r\n                    {Icon && <Icon className=\"w-4 h-4\" />}\r\n                </label>\r\n                <button\r\n                    onClick={handleAIFix}\r\n                    disabled={loading || !value?.trim()}\r\n                    className=\"flex items-center gap-1 px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                    title=\"Sử dụng AI để sửa chính tả và ký hiệu LaTeX\"\r\n                >\r\n                    <Sparkles className=\"w-3 h-3\" />\r\n                    {!short && (loading ? 'Đang sửa...' : 'AI Fix')}\r\n                </button>\r\n            </div>\r\n            <TextArea\r\n                value={value}\r\n                onChange={onChange}\r\n                placeholder={placeholder}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nconst DetailQuestionView = () => {\r\n    const { questionsExam, selectedId, view } = useSelector((state) => state.questionsExam);\r\n    const { codes } = useSelector((state) => state.codes);\r\n    const dispatch = useDispatch();\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n    const [question, setQuestion] = useState(null);\r\n\r\n    useEffect(() => {\r\n        // console.log(selectedId);\r\n\r\n        if (selectedId && questionsExam?.length > 0) {\r\n            const selectedQuestion = questionsExam?.find(q => q.id === selectedId);\r\n            setQuestion(selectedQuestion);\r\n        }\r\n    }, [selectedId, questionsExam]);\r\n\r\n    const handleQuestionChange = (e, field) => {\r\n        const updatedQuestion = { ...question, [field]: e.target.value };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleStatementChange = (index, value, field) => {\r\n        const updatedStatements = [...question.statements];\r\n        updatedStatements[index] = { ...updatedStatements[index], [field]: value };\r\n        const updatedQuestion = { ...question, statements: updatedStatements };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleSolutionQuestionChange = (value) => {\r\n        const updatedQuestion = { ...question, solution: value };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleAddStatement = () => {\r\n        dispatch(addStatement());\r\n    };\r\n\r\n    const handleRemoveStatement = (index) => {\r\n        const updatedStatements = [...question.statements];\r\n        updatedStatements.splice(index, 1);\r\n        const updatedQuestion = { ...question, statements: updatedStatements };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (question?.class && question?.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.startsWith(question?.class) && code.code.length === 5)\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"].filter((code) => code.code.length === 5));\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, question?.class]);\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3 w-full\">\r\n            {question && (\r\n                <div className=\"space-y-3 w-full\">\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Phân loại</h3>\r\n                        <div className=\"flex flex-row gap-2\">\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={question.class}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'class')}\r\n                                options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                                className=\"text-xs\"\r\n                            />\r\n                            <SuggestInputBarAdmin\r\n                                selectedOption={question.chapter}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'chapter')}\r\n                                options={optionChapter}\r\n                                className=\"text-xs\"\r\n                            />\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={question.difficulty}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'difficulty')}\r\n                                options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                                className=\"text-xs\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <hr className=\" bg-gray-200\"></hr>\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thông tin câu hỏi</h3>\r\n                        <div className=\"space-y-2\">\r\n                            <TextAreaWithAI\r\n                                value={question.content}\r\n                                onChange={(e) => handleQuestionChange(e, 'content')}\r\n                                placeholder=\"Nhập nội dung câu hỏi\"\r\n                                label=\"Câu hỏi\"\r\n                                row={false}\r\n                            />\r\n                            {(view === 'image' || question.imageUrl) && (\r\n                                <ImageDropZone\r\n                                    imageUrl={question.imageUrl}\r\n                                    onImageDrop={(image) => handleQuestionChange({ target: { value: image } }, 'imageUrl')}\r\n                                    onImageRemove={() => handleQuestionChange({ target: { value: '' } }, 'imageUrl')}\r\n                                />\r\n                            )}\r\n                            {question.typeOfQuestion !== 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    {question.statements.length < 4 && (\r\n                                        <button\r\n                                            onClick={() => handleAddStatement()}\r\n                                            className=\"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-blue-600 hover:bg-blue-700 text-white\"\r\n                                        >\r\n                                            <Plus className=\"w-3 h-3\" /> Thêm mệnh đề\r\n                                        </button>\r\n                                    )}\r\n                                    {question.statements.map((statement, index) => (\r\n                                        <div key={index} className=\"flex flex-col gap-2 items-center w-full\">\r\n                                            <div className=\"flex flex-row gap-2 items-center w-full\">\r\n                                                <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                                    {question.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}\r\n                                                </p>\r\n                                                <TextAreaWithAI\r\n                                                    value={statement.content}\r\n                                                    onChange={(e) => handleStatementChange(index, e.target.value, 'content')}\r\n                                                    placeholder=\"Nhập nội dung mệnh đề\"\r\n                                                    short={true}\r\n                                                    row={true}\r\n                                                />\r\n                                                {question.typeOfQuestion !== 'TLN' && (\r\n                                                    <div className=\"flex items-center gap-2\">\r\n                                                        <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                                            <input\r\n                                                                type=\"radio\"\r\n                                                                name={`statement-${index}`}\r\n                                                                checked={statement.isCorrect}\r\n                                                                onChange={() => handleStatementChange(index, true, 'isCorrect')}\r\n                                                                className=\"w-4 h-4 text-green-600 focus:ring-green-500\"\r\n                                                            />\r\n                                                            <span className=\"text-sm text-green-600 font-medium\">Đúng</span>\r\n                                                        </label>\r\n                                                        <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                                            <input\r\n                                                                type=\"radio\"\r\n                                                                name={`statement-${index}`}\r\n                                                                checked={!statement.isCorrect}\r\n                                                                onChange={() => handleStatementChange(index, false, 'isCorrect')}\r\n                                                                className=\"w-4 h-4 text-red-600 focus:ring-red-500\"\r\n                                                            />\r\n                                                            <span className=\"text-sm text-red-600 font-medium\">Sai</span>\r\n                                                        </label>\r\n                                                    </div>\r\n                                                )}\r\n                                                <button\r\n                                                    onClick={() => handleRemoveStatement(index)}\r\n                                                    className=\"text-xs flex items-center p-1 rounded-full bg-red-50 hover:bg-red-200 text-red-600\"\r\n                                                >\r\n                                                    <Trash2 className=\"w-3 h-3\" />\r\n                                                </button>\r\n                                            </div>\r\n                                            {(view === 'image' || statement.imageUrl) && (\r\n                                                <ImageDropZone\r\n                                                    imageUrl={statement.imageUrl}\r\n                                                    onImageDrop={(image) => handleStatementChange(index, image, 'imageUrl')}\r\n                                                    onImageRemove={() => handleStatementChange(index, '', 'imageUrl')}\r\n                                                />\r\n                                            )}\r\n\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            )}\r\n                            {question.typeOfQuestion === 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    <TextArea\r\n                                        value={question.correctAnswer}\r\n                                        onChange={(e) => handleQuestionChange(e, 'correctAnswer')}\r\n                                        placeholder=\"Nhập đáp án\"\r\n                                        label=\"Đáp án\"\r\n                                        Icon={CheckCircle}\r\n                                    />\r\n                                </div>\r\n                            )}\r\n                            {/* <TextArea\r\n                                        value={question.solution}\r\n                                        onChange={(e) => handleQuestionChange(e, 'solution')}\r\n                                        placeholder=\"Nhập lời giải\"\r\n                                        label=\"Lời giải\"\r\n                                        Icon={CheckCircle}\r\n                                    /> */}\r\n                            <TextArea\r\n                                value={question.solutionUrl || ''}\r\n                                onChange={(e) => handleQuestionChange(e, 'solutionUrl')}\r\n                                placeholder=\"Nhập link lời giải youtube\"\r\n                                label=\"Link lời giải\"\r\n                                Icon={Video}\r\n                            />\r\n                            {question.solutionUrl && (\r\n                                <YouTubePlayer url={question.solutionUrl} />\r\n                            )}\r\n                            <SolutionEditor\r\n                                solution={question.solution}\r\n                                onSolutionChange={handleSolutionQuestionChange}\r\n                                preview={false}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst AddQuestionView = () => {\r\n    const dispatch = useDispatch();\r\n    const { newQuestion } = useSelector((state) => state.questionsExam);\r\n    const { codes } = useSelector((state) => state.codes);\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n    const handleNewQuestionChange = (e, field) => {\r\n        const updatedQuestion = { ...newQuestion, [field]: e.target.value };\r\n        dispatch(setNewQuestion(updatedQuestion));\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (newQuestion?.class && newQuestion?.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.startsWith(newQuestion?.class) && code.code.length === 5)\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"].filter((code) => code.code.length === 5));\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, newQuestion?.class]);\r\n\r\n    const handleNewStatementChange = (index, value, field) => {\r\n        const updatedStatements = [...newQuestion.statements];\r\n        updatedStatements[index] = { ...updatedStatements[index], [field]: value };\r\n        const updatedQuestion = { ...newQuestion, statements: updatedStatements };\r\n        dispatch(setNewQuestion(updatedQuestion));\r\n    };\r\n\r\n    const handleNewSolutionQuestionChange = (value) => {\r\n        const updatedQuestion = { ...newQuestion, solution: value };\r\n        dispatch(setNewQuestion(updatedQuestion));\r\n    };\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3 w-full\">\r\n            <div className=\"flex flex-col gap-2\">\r\n                <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Phân loại</h3>\r\n                <div className=\"flex flex-row gap-2\">\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={newQuestion.typeOfQuestion}\r\n                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'typeOfQuestion')}\r\n                        options={[{ code: \"TN\", description: \"Trắc nghiệm\" }, { code: \"DS\", description: \"Đúng sai\" }, { code: \"TLN\", description: \"Trả lời ngắn\" }]}\r\n                        className=\"text-xs\"\r\n                    />\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={newQuestion.class}\r\n                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'class')}\r\n                        options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                    <SuggestInputBarAdmin\r\n                        selectedOption={newQuestion.chapter}\r\n                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'chapter')}\r\n                        options={optionChapter}\r\n                        className=\"text-xs\"\r\n                    />\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={newQuestion.difficulty}\r\n                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'difficulty')}\r\n                        options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n                <hr className=\" bg-gray-200\"></hr>\r\n                <div className=\"flex flex-col gap-2\">\r\n                    <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thông tin câu hỏi</h3>\r\n                    <div className=\"space-y-2\">\r\n                        <TextAreaWithAI\r\n                            value={newQuestion.content}\r\n                            onChange={(e) => handleNewQuestionChange(e, 'content')}\r\n                            placeholder=\"Nhập nội dung câu hỏi\"\r\n                            label=\"Câu hỏi\"\r\n                        />\r\n                        <ImageDropZone\r\n                            imageUrl={newQuestion.imageUrl}\r\n                            onImageDrop={(image) => handleNewQuestionChange({ target: { value: image } }, 'imageUrl')}\r\n                            onImageRemove={() => handleNewQuestionChange({ target: { value: '' } }, 'imageUrl')}\r\n                        />\r\n                        {newQuestion.typeOfQuestion !== 'TLN' && (\r\n                            <div className=\"space-y-2\">\r\n                                {newQuestion.statements.map((statement, index) => (\r\n                                    <div key={index} className=\"flex flex-col gap-2 items-center w-full\">\r\n                                        <div className=\"flex flex-row gap-2 items-center w-full\">\r\n                                            <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                                {newQuestion.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}\r\n                                            </p>\r\n                                            <TextAreaWithAI\r\n                                                value={statement.content}\r\n                                                onChange={(e) => handleNewStatementChange(index, e.target.value, 'content')}\r\n                                                placeholder=\"Nhập nội dung mệnh đề\"\r\n                                            />\r\n                                            <div className=\"flex items-center gap-2\">\r\n                                                <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                                    <input\r\n                                                        type=\"radio\"\r\n                                                        name={`statement-${index}`}\r\n                                                        checked={statement.isCorrect}\r\n                                                        onChange={() => handleNewStatementChange(index, true, 'isCorrect')}\r\n                                                        className=\"w-4 h-4 text-green-600 focus:ring-green-500\"\r\n                                                    />\r\n                                                    <span className=\"text-sm text-green-600 font-medium\">Đúng</span>\r\n                                                </label>\r\n                                                <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                                    <input\r\n                                                        type=\"radio\"\r\n                                                        name={`statement-${index}`}\r\n                                                        checked={!statement.isCorrect}\r\n                                                        onChange={() => handleNewStatementChange(index, false, 'isCorrect')}\r\n                                                        className=\"w-4 h-4 text-red-600 focus:ring-red-500\"\r\n                                                    />\r\n                                                    <span className=\"text-sm text-red-600 font-medium\">Sai</span>\r\n                                                </label>\r\n                                            </div>\r\n                                        </div>\r\n                                        <ImageDropZone\r\n                                            imageUrl={statement.imageUrl}\r\n                                            onImageDrop={(image) => handleNewStatementChange(index, image, 'imageUrl')}\r\n                                            onImageRemove={() => handleNewStatementChange(index, '', 'imageUrl')}\r\n                                        />\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        )}\r\n                        {newQuestion.typeOfQuestion === 'TLN' && (\r\n                            <div className=\"space-y-2\">\r\n                                <TextArea\r\n                                    value={newQuestion.correctAnswer}\r\n                                    onChange={(e) => handleNewQuestionChange(e, 'correctAnswer')}\r\n                                    placeholder=\"Nhập đáp án\"\r\n                                    label=\"Đáp án\"\r\n                                    Icon={CheckCircle}\r\n                                />\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n                <div className=\"space-y-2\">\r\n                    <TextArea\r\n                        value={newQuestion.solutionUrl}\r\n                        onChange={(e) => handleNewQuestionChange(e, 'solutionUrl')}\r\n                        placeholder=\"Nhập link lời giải youtube\"\r\n                        label=\"Link lời giải\"\r\n                        Icon={Video}\r\n                    />\r\n                    {newQuestion.solutionUrl && (\r\n                        <YouTubePlayer url={newQuestion.solutionUrl} />\r\n                    )}\r\n                    <SolutionEditor\r\n                        solution={newQuestion.solution}\r\n                        onSolutionChange={handleNewSolutionQuestionChange}\r\n                    />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nconst LeftContent = () => {\r\n    const [view, setView] = useState('questionDetail');\r\n    const { newQuestion } = useSelector((state) => state.questionsExam);\r\n    const dispatch = useDispatch();\r\n\r\n    const handleAddQuestion = () => {\r\n        if (!newQuestion.content.trim()) {\r\n            dispatch(setErrorMessage(\"Nội dung câu hỏi không được để trống!\"));\r\n            return;\r\n        }\r\n        if (!newQuestion.typeOfQuestion) {\r\n            dispatch(setErrorMessage(\"Loại câu hỏi không được để trống!\"));\r\n            return;\r\n        }\r\n        if (!newQuestion.class) {\r\n            dispatch(setErrorMessage(\"Lớp không được để trống!\"));\r\n            return;\r\n        }\r\n        if (newQuestion.typeOfQuestion !== 'TLN' && newQuestion.statements.filter(statement => statement.content.trim() !== \"\").length < 4) {\r\n            dispatch(setErrorMessage(\"Câu hỏi TN phải có ít nhất 4 đáp án!\"));\r\n            return;\r\n        }\r\n        if (newQuestion.typeOfQuestion === 'TLN' && !newQuestion.correctAnswer.trim()) {\r\n            dispatch(setErrorMessage(\"Đáp án không được để trống!\"));\r\n            return;\r\n        }\r\n        dispatch(addQuestion());\r\n        setView('questionDetail');\r\n    };\r\n\r\n\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_138px)] overflow-y-auto\">\r\n            {/* Compact Preview Header */}\r\n            <div className=\"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <h2 className=\"text-xs font-semibold text-gray-900\">Chi tiết câu hỏi</h2>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                    {\r\n                        view === 'questionDetail' ? (\r\n                            <button\r\n                                onClick={() => setView('addQuestion')}\r\n                                className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-sky-600 hover:bg-sky-700 text-white`}\r\n                            >\r\n                                Thêm câu hỏi\r\n                            </button>\r\n                        ) : (\r\n                            <>\r\n                                <button\r\n                                    onClick={handleAddQuestion}\r\n                                    className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-emerald-600 hover:bg-emerald-700 text-white`}\r\n                                >\r\n                                    Lưu\r\n                                </button>\r\n                                <button\r\n                                    onClick={() => setView('questionDetail')}\r\n                                    className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-red-600 hover:bg-red-700 text-white`}\r\n                                >\r\n                                    Hủy\r\n                                </button>\r\n                            </>\r\n                        )\r\n                    }\r\n                </div>\r\n            </div>\r\n            {view === 'questionDetail' && <DetailQuestionView />}\r\n            {view === 'addQuestion' && <AddQuestionView />}\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nexport default LeftContent;"], "mappings": ";;;;;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,QAAQ,MAAM,+BAA+B;AACpD,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,OAAOC,cAAc,MAAM,2CAA2C;AACtE,SAASC,WAAW,QAAQ,+CAA+C;AAC3E,SAASC,WAAW,EAAEC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,cAAc;AACzE,SAASC,YAAY,EAAEC,cAAc,EAAEC,WAAW,QAAQ,+CAA+C;AACzG,SAASC,YAAY,QAAQ,+CAA+C;AAC5E,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,aAAa,MAAM,kBAAkB;;AAE5C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAGC,IAAA,IAA+E;EAAAC,EAAA;EAAA,IAA9E;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,KAAK;IAAEC,IAAI;IAAEC,GAAG,GAAG,KAAK;IAAEC,KAAK,GAAG;EAAM,CAAC,GAAAR,IAAA;EAC7F,MAAMS,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMsC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAACV,KAAK,IAAI,CAACA,KAAK,CAACW,IAAI,CAAC,CAAC,EAAE;MACzBC,KAAK,CAAC,qDAAqD,CAAC;MAC5D;IACJ;IAEA,IAAI;MACAH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,MAAM,GAAG,MAAMN,QAAQ,CAACjB,eAAe,CAACU,KAAK,CAAC,CAAC,CAACc,MAAM,CAAC,CAAC;MAC9D,IAAID,MAAM,CAACE,IAAI,CAACC,UAAU,EAAE;QACxBf,QAAQ,CAAC;UAAEgB,MAAM,EAAE;YAAEjB,KAAK,EAAEa,MAAM,CAACE,IAAI,CAACG;UAAU;QAAE,CAAC,CAAC;MAC1D,CAAC,MAAM;QACHN,KAAK,CAAC,gCAAgC,CAAC;MAC3C;MACAH,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOU,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CP,KAAK,CAAC,sCAAsC,CAAC;IACjD;EACJ,CAAC;EAED,oBACIlB,OAAA;IAAK2B,SAAS,gBAAAC,MAAA,CAAgBjB,GAAG,GAAG,kBAAkB,GAAG,UAAU,CAAG;IAAAkB,QAAA,gBAClE7B,OAAA;MAAK2B,SAAS,EAAC,mCAAmC;MAAAE,QAAA,gBAC9C7B,OAAA;QAAO2B,SAAS,EAAC,2DAA2D;QAAAE,QAAA,EACvEnB,IAAI,iBAAIV,OAAA,CAACU,IAAI;UAACiB,SAAS,EAAC;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACRjC,OAAA;QACIkC,OAAO,EAAElB,WAAY;QACrBmB,QAAQ,EAAErB,OAAO,IAAI,EAACR,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEW,IAAI,CAAC,CAAC,CAAC;QACpCU,SAAS,EAAC,kKAAkK;QAC5KS,KAAK,EAAC,yFAA6C;QAAAP,QAAA,gBAEnD7B,OAAA,CAACV,QAAQ;UAACqC,SAAS,EAAC;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC/B,CAACrB,KAAK,KAAKE,OAAO,GAAG,aAAa,GAAG,QAAQ,CAAC;MAAA;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eACNjC,OAAA,CAACjB,QAAQ;MACLuB,KAAK,EAAEA,KAAM;MACbC,QAAQ,EAAEA,QAAS;MACnBC,WAAW,EAAEA;IAAY;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAC5B,EAAA,CA/CIF,cAAc;EAAA,QACCvB,WAAW;AAAA;AAAAyD,EAAA,GAD1BlC,cAAc;AAiDpB,MAAMmC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7B,MAAM;IAAEC,aAAa;IAAEC,UAAU;IAAEC;EAAK,CAAC,GAAG/D,WAAW,CAAEgE,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EACvF,MAAM;IAAEI;EAAM,CAAC,GAAGjE,WAAW,CAAEgE,KAAK,IAAKA,KAAK,CAACC,KAAK,CAAC;EACrD,MAAM/B,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAMiE,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuE,QAAQ,EAAE/D,WAAW,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EAE9CD,SAAS,CAAC,MAAM;IACZ;;IAEA,IAAIgE,UAAU,IAAI,CAAAD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEU,MAAM,IAAG,CAAC,EAAE;MACzC,MAAMC,gBAAgB,GAAGX,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEY,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKb,UAAU,CAAC;MACtEvD,WAAW,CAACiE,gBAAgB,CAAC;IACjC;EACJ,CAAC,EAAE,CAACV,UAAU,EAAED,aAAa,CAAC,CAAC;EAE/B,MAAMe,oBAAoB,GAAGA,CAACC,CAAC,EAAEC,KAAK,KAAK;IACvC,MAAMC,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAE,CAACQ,KAAK,GAAGD,CAAC,CAACjC,MAAM,CAACjB;IAAM,CAAC;IAChEO,QAAQ,CAACrB,YAAY,CAACkE,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAACC,KAAK,EAAEtD,KAAK,EAAEmD,KAAK,KAAK;IACnD,MAAMI,iBAAiB,GAAG,CAAC,GAAGZ,QAAQ,CAACa,UAAU,CAAC;IAClDD,iBAAiB,CAACD,KAAK,CAAC,GAAG;MAAE,GAAGC,iBAAiB,CAACD,KAAK,CAAC;MAAE,CAACH,KAAK,GAAGnD;IAAM,CAAC;IAC1E,MAAMoD,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAEa,UAAU,EAAED;IAAkB,CAAC;IACtEhD,QAAQ,CAACrB,YAAY,CAACkE,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMK,4BAA4B,GAAIzD,KAAK,IAAK;IAC5C,MAAMoD,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAEe,QAAQ,EAAE1D;IAAM,CAAC;IACxDO,QAAQ,CAACrB,YAAY,CAACkE,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMO,kBAAkB,GAAGA,CAAA,KAAM;IAC7BpD,QAAQ,CAAClB,YAAY,CAAC,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMuE,qBAAqB,GAAIN,KAAK,IAAK;IACrC,MAAMC,iBAAiB,GAAG,CAAC,GAAGZ,QAAQ,CAACa,UAAU,CAAC;IAClDD,iBAAiB,CAACM,MAAM,CAACP,KAAK,EAAE,CAAC,CAAC;IAClC,MAAMF,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAEa,UAAU,EAAED;IAAkB,CAAC;IACtEhD,QAAQ,CAACrB,YAAY,CAACkE,eAAe,CAAC,CAAC;EAC3C,CAAC;EAEDjF,SAAS,CAAC,MAAM;IACZ,IAAI2F,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MACjC,IAAIK,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEqB,KAAK,IAAI,CAAArB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqB,KAAK,CAACrD,IAAI,CAAC,CAAC,MAAK,EAAE,EAAE;QAClD+B,gBAAgB,CACZJ,KAAK,CAAC,SAAS,CAAC,CAAC2B,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,UAAU,CAACxB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqB,KAAK,CAAC,IAAIE,IAAI,CAACA,IAAI,CAACtB,MAAM,KAAK,CAAC,CACrG,CAAC;MACL,CAAC,MAAM;QACHF,gBAAgB,CAACJ,KAAK,CAAC,SAAS,CAAC,CAAC2B,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACtB,MAAM,KAAK,CAAC,CAAC,CAAC;MAC/E;IACJ,CAAC,MAAM;MACHF,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAACJ,KAAK,EAAEK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqB,KAAK,CAAC,CAAC;EAE5B,oBACItE,OAAA;IAAK2B,SAAS,EAAC,sBAAsB;IAAAE,QAAA,EAChCoB,QAAQ,iBACLjD,OAAA;MAAK2B,SAAS,EAAC,kBAAkB;MAAAE,QAAA,gBAC7B7B,OAAA;QAAK2B,SAAS,EAAC,qBAAqB;QAAAE,QAAA,gBAChC7B,OAAA;UAAI2B,SAAS,EAAC,wCAAwC;UAAAE,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEjC,OAAA;UAAK2B,SAAS,EAAC,qBAAqB;UAAAE,QAAA,gBAChC7B,OAAA,CAACnB,gBAAgB;YACb6F,cAAc,EAAEzB,QAAQ,CAACqB,KAAM;YAC/B/D,QAAQ,EAAGoE,MAAM,IAAKpB,oBAAoB,CAAC;cAAEhC,MAAM,EAAE;gBAAEjB,KAAK,EAAEqE;cAAO;YAAE,CAAC,EAAE,OAAO,CAAE;YACnFC,OAAO,EAAER,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;YAC7DjB,SAAS,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACFjC,OAAA,CAAClB,oBAAoB;YACjB4F,cAAc,EAAEzB,QAAQ,CAAC4B,OAAQ;YACjCtE,QAAQ,EAAGoE,MAAM,IAAKpB,oBAAoB,CAAC;cAAEhC,MAAM,EAAE;gBAAEjB,KAAK,EAAEqE;cAAO;YAAE,CAAC,EAAE,SAAS,CAAE;YACrFC,OAAO,EAAE7B,aAAc;YACvBpB,SAAS,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACFjC,OAAA,CAACnB,gBAAgB;YACb6F,cAAc,EAAEzB,QAAQ,CAAC6B,UAAW;YACpCvE,QAAQ,EAAGoE,MAAM,IAAKpB,oBAAoB,CAAC;cAAEhC,MAAM,EAAE;gBAAEjB,KAAK,EAAEqE;cAAO;YAAE,CAAC,EAAE,YAAY,CAAE;YACxFC,OAAO,EAAER,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;YACvEjB,SAAS,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNjC,OAAA;QAAI2B,SAAS,EAAC;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClCjC,OAAA;QAAK2B,SAAS,EAAC,qBAAqB;QAAAE,QAAA,gBAChC7B,OAAA;UAAI2B,SAAS,EAAC,wCAAwC;UAAAE,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EjC,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAE,QAAA,gBACtB7B,OAAA,CAACG,cAAc;YACXG,KAAK,EAAE2C,QAAQ,CAAC8B,OAAQ;YACxBxE,QAAQ,EAAGiD,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,SAAS,CAAE;YACpDhD,WAAW,EAAC,yCAAuB;YACnCC,KAAK,EAAC,iBAAS;YACfE,GAAG,EAAE;UAAM;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,EACD,CAACS,IAAI,KAAK,OAAO,IAAIO,QAAQ,CAAC+B,QAAQ,kBACnChF,OAAA,CAAChB,aAAa;YACVgG,QAAQ,EAAE/B,QAAQ,CAAC+B,QAAS;YAC5BC,WAAW,EAAGC,KAAK,IAAK3B,oBAAoB,CAAC;cAAEhC,MAAM,EAAE;gBAAEjB,KAAK,EAAE4E;cAAM;YAAE,CAAC,EAAE,UAAU,CAAE;YACvFC,aAAa,EAAEA,CAAA,KAAM5B,oBAAoB,CAAC;cAAEhC,MAAM,EAAE;gBAAEjB,KAAK,EAAE;cAAG;YAAE,CAAC,EAAE,UAAU;UAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CACJ,EACAgB,QAAQ,CAACmC,cAAc,KAAK,KAAK,iBAC9BpF,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAE,QAAA,GACrBoB,QAAQ,CAACa,UAAU,CAACZ,MAAM,GAAG,CAAC,iBAC3BlD,OAAA;cACIkC,OAAO,EAAEA,CAAA,KAAM+B,kBAAkB,CAAC,CAAE;cACpCtC,SAAS,EAAC,+FAA+F;cAAAE,QAAA,gBAEzG7B,OAAA,CAACZ,IAAI;gBAACuC,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mCAChC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACX,EACAgB,QAAQ,CAACa,UAAU,CAACuB,GAAG,CAAC,CAACC,SAAS,EAAE1B,KAAK,kBACtC5D,OAAA;cAAiB2B,SAAS,EAAC,yCAAyC;cAAAE,QAAA,gBAChE7B,OAAA;gBAAK2B,SAAS,EAAC,yCAAyC;gBAAAE,QAAA,gBACpD7B,OAAA;kBAAG2B,SAAS,EAAC,qCAAqC;kBAAAE,QAAA,EAC7CoB,QAAQ,CAACmC,cAAc,KAAK,IAAI,GAAGvC,QAAQ,CAACe,KAAK,CAAC,GAAGd,QAAQ,CAACc,KAAK;gBAAC;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACJjC,OAAA,CAACG,cAAc;kBACXG,KAAK,EAAEgF,SAAS,CAACP,OAAQ;kBACzBxE,QAAQ,EAAGiD,CAAC,IAAKG,qBAAqB,CAACC,KAAK,EAAEJ,CAAC,CAACjC,MAAM,CAACjB,KAAK,EAAE,SAAS,CAAE;kBACzEE,WAAW,EAAC,gDAAuB;kBACnCI,KAAK,EAAE,IAAK;kBACZD,GAAG,EAAE;gBAAK;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,EACDgB,QAAQ,CAACmC,cAAc,KAAK,KAAK,iBAC9BpF,OAAA;kBAAK2B,SAAS,EAAC,yBAAyB;kBAAAE,QAAA,gBACpC7B,OAAA;oBAAO2B,SAAS,EAAC,wCAAwC;oBAAAE,QAAA,gBACrD7B,OAAA;sBACIuF,IAAI,EAAC,OAAO;sBACZC,IAAI,eAAA5D,MAAA,CAAegC,KAAK,CAAG;sBAC3B6B,OAAO,EAAEH,SAAS,CAACI,SAAU;sBAC7BnF,QAAQ,EAAEA,CAAA,KAAMoD,qBAAqB,CAACC,KAAK,EAAE,IAAI,EAAE,WAAW,CAAE;sBAChEjC,SAAS,EAAC;oBAA6C;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACFjC,OAAA;sBAAM2B,SAAS,EAAC,oCAAoC;sBAAAE,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACRjC,OAAA;oBAAO2B,SAAS,EAAC,wCAAwC;oBAAAE,QAAA,gBACrD7B,OAAA;sBACIuF,IAAI,EAAC,OAAO;sBACZC,IAAI,eAAA5D,MAAA,CAAegC,KAAK,CAAG;sBAC3B6B,OAAO,EAAE,CAACH,SAAS,CAACI,SAAU;sBAC9BnF,QAAQ,EAAEA,CAAA,KAAMoD,qBAAqB,CAACC,KAAK,EAAE,KAAK,EAAE,WAAW,CAAE;sBACjEjC,SAAS,EAAC;oBAAyC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACFjC,OAAA;sBAAM2B,SAAS,EAAC,kCAAkC;sBAAAE,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CACR,eACDjC,OAAA;kBACIkC,OAAO,EAAEA,CAAA,KAAMgC,qBAAqB,CAACN,KAAK,CAAE;kBAC5CjC,SAAS,EAAC,oFAAoF;kBAAAE,QAAA,eAE9F7B,OAAA,CAACT,MAAM;oBAACoC,SAAS,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,EACL,CAACS,IAAI,KAAK,OAAO,IAAI4C,SAAS,CAACN,QAAQ,kBACpChF,OAAA,CAAChB,aAAa;gBACVgG,QAAQ,EAAEM,SAAS,CAACN,QAAS;gBAC7BC,WAAW,EAAGC,KAAK,IAAKvB,qBAAqB,CAACC,KAAK,EAAEsB,KAAK,EAAE,UAAU,CAAE;gBACxEC,aAAa,EAAEA,CAAA,KAAMxB,qBAAqB,CAACC,KAAK,EAAE,EAAE,EAAE,UAAU;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CACJ;YAAA,GAjDK2B,KAAK;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDV,CACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EACAgB,QAAQ,CAACmC,cAAc,KAAK,KAAK,iBAC9BpF,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAE,QAAA,eACtB7B,OAAA,CAACjB,QAAQ;cACLuB,KAAK,EAAE2C,QAAQ,CAAC0C,aAAc;cAC9BpF,QAAQ,EAAGiD,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,eAAe,CAAE;cAC1DhD,WAAW,EAAC,6BAAa;cACzBC,KAAK,EAAC,mBAAQ;cACdC,IAAI,EAAEvB;YAAY;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,eAQDjC,OAAA,CAACjB,QAAQ;YACLuB,KAAK,EAAE2C,QAAQ,CAAC2C,WAAW,IAAI,EAAG;YAClCrF,QAAQ,EAAGiD,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,aAAa,CAAE;YACxDhD,WAAW,EAAC,2CAA4B;YACxCC,KAAK,EAAC,yBAAe;YACrBC,IAAI,EAAErB;UAAM;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EACDgB,QAAQ,CAAC2C,WAAW,iBACjB5F,OAAA,CAACF,aAAa;YAAC+F,GAAG,EAAE5C,QAAQ,CAAC2C;UAAY;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC9C,eACDjC,OAAA,CAACf,cAAc;YACX+E,QAAQ,EAAEf,QAAQ,CAACe,QAAS;YAC5B8B,gBAAgB,EAAE/B,4BAA6B;YAC/CgC,OAAO,EAAE;UAAM;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAM,GAAA,CAnNKD,kBAAkB;EAAA,QACwB3D,WAAW,EACrCA,WAAW,EACZC,WAAW;AAAA;AAAAoH,GAAA,GAH1B1D,kBAAkB;AAqNxB,MAAM2D,eAAe,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC1B,MAAMrF,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuH;EAAY,CAAC,GAAGxH,WAAW,CAAEgE,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EACnE,MAAM;IAAEI;EAAM,CAAC,GAAGjE,WAAW,CAAEgE,KAAK,IAAKA,KAAK,CAACC,KAAK,CAAC;EACrD,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM0H,uBAAuB,GAAGA,CAAC5C,CAAC,EAAEC,KAAK,KAAK;IAC1C,MAAMC,eAAe,GAAG;MAAE,GAAGyC,WAAW;MAAE,CAAC1C,KAAK,GAAGD,CAAC,CAACjC,MAAM,CAACjB;IAAM,CAAC;IACnEO,QAAQ,CAACpB,cAAc,CAACiE,eAAe,CAAC,CAAC;EAC7C,CAAC;EAEDjF,SAAS,CAAC,MAAM;IACZ,IAAI2F,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MACjC,IAAIuD,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAE7B,KAAK,IAAI,CAAA6B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE7B,KAAK,CAACrD,IAAI,CAAC,CAAC,MAAK,EAAE,EAAE;QACxD+B,gBAAgB,CACZJ,KAAK,CAAC,SAAS,CAAC,CAAC2B,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,UAAU,CAAC0B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE7B,KAAK,CAAC,IAAIE,IAAI,CAACA,IAAI,CAACtB,MAAM,KAAK,CAAC,CACxG,CAAC;MACL,CAAC,MAAM;QACHF,gBAAgB,CAACJ,KAAK,CAAC,SAAS,CAAC,CAAC2B,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACtB,MAAM,KAAK,CAAC,CAAC,CAAC;MAC/E;IACJ,CAAC,MAAM;MACHF,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAACJ,KAAK,EAAEuD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE7B,KAAK,CAAC,CAAC;EAE/B,MAAM+B,wBAAwB,GAAGA,CAACzC,KAAK,EAAEtD,KAAK,EAAEmD,KAAK,KAAK;IACtD,MAAMI,iBAAiB,GAAG,CAAC,GAAGsC,WAAW,CAACrC,UAAU,CAAC;IACrDD,iBAAiB,CAACD,KAAK,CAAC,GAAG;MAAE,GAAGC,iBAAiB,CAACD,KAAK,CAAC;MAAE,CAACH,KAAK,GAAGnD;IAAM,CAAC;IAC1E,MAAMoD,eAAe,GAAG;MAAE,GAAGyC,WAAW;MAAErC,UAAU,EAAED;IAAkB,CAAC;IACzEhD,QAAQ,CAACpB,cAAc,CAACiE,eAAe,CAAC,CAAC;EAC7C,CAAC;EAED,MAAM4C,+BAA+B,GAAIhG,KAAK,IAAK;IAC/C,MAAMoD,eAAe,GAAG;MAAE,GAAGyC,WAAW;MAAEnC,QAAQ,EAAE1D;IAAM,CAAC;IAC3DO,QAAQ,CAACpB,cAAc,CAACiE,eAAe,CAAC,CAAC;EAC7C,CAAC;EAED,oBACI1D,OAAA;IAAK2B,SAAS,EAAC,sBAAsB;IAAAE,QAAA,eACjC7B,OAAA;MAAK2B,SAAS,EAAC,qBAAqB;MAAAE,QAAA,gBAChC7B,OAAA;QAAI2B,SAAS,EAAC,wCAAwC;QAAAE,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrEjC,OAAA;QAAK2B,SAAS,EAAC,qBAAqB;QAAAE,QAAA,gBAChC7B,OAAA,CAACnB,gBAAgB;UACb6F,cAAc,EAAEyB,WAAW,CAACf,cAAe;UAC3C7E,QAAQ,EAAGoE,MAAM,IAAKyB,uBAAuB,CAAC;YAAE7E,MAAM,EAAE;cAAEjB,KAAK,EAAEqE;YAAO;UAAE,CAAC,EAAE,gBAAgB,CAAE;UAC/FC,OAAO,EAAE,CAAC;YAAEJ,IAAI,EAAE,IAAI;YAAE+B,WAAW,EAAE;UAAc,CAAC,EAAE;YAAE/B,IAAI,EAAE,IAAI;YAAE+B,WAAW,EAAE;UAAW,CAAC,EAAE;YAAE/B,IAAI,EAAE,KAAK;YAAE+B,WAAW,EAAE;UAAe,CAAC,CAAE;UAC7I5E,SAAS,EAAC;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACFjC,OAAA,CAACnB,gBAAgB;UACb6F,cAAc,EAAEyB,WAAW,CAAC7B,KAAM;UAClC/D,QAAQ,EAAGoE,MAAM,IAAKyB,uBAAuB,CAAC;YAAE7E,MAAM,EAAE;cAAEjB,KAAK,EAAEqE;YAAO;UAAE,CAAC,EAAE,OAAO,CAAE;UACtFC,OAAO,EAAER,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;UAC7DjB,SAAS,EAAC;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACFjC,OAAA,CAAClB,oBAAoB;UACjB4F,cAAc,EAAEyB,WAAW,CAACtB,OAAQ;UACpCtE,QAAQ,EAAGoE,MAAM,IAAKyB,uBAAuB,CAAC;YAAE7E,MAAM,EAAE;cAAEjB,KAAK,EAAEqE;YAAO;UAAE,CAAC,EAAE,SAAS,CAAE;UACxFC,OAAO,EAAE7B,aAAc;UACvBpB,SAAS,EAAC;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACFjC,OAAA,CAACnB,gBAAgB;UACb6F,cAAc,EAAEyB,WAAW,CAACrB,UAAW;UACvCvE,QAAQ,EAAGoE,MAAM,IAAKyB,uBAAuB,CAAC;YAAE7E,MAAM,EAAE;cAAEjB,KAAK,EAAEqE;YAAO;UAAE,CAAC,EAAE,YAAY,CAAE;UAC3FC,OAAO,EAAER,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;UACvEjB,SAAS,EAAC;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNjC,OAAA;QAAI2B,SAAS,EAAC;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClCjC,OAAA;QAAK2B,SAAS,EAAC,qBAAqB;QAAAE,QAAA,gBAChC7B,OAAA;UAAI2B,SAAS,EAAC,wCAAwC;UAAAE,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EjC,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAE,QAAA,gBACtB7B,OAAA,CAACG,cAAc;YACXG,KAAK,EAAE6F,WAAW,CAACpB,OAAQ;YAC3BxE,QAAQ,EAAGiD,CAAC,IAAK4C,uBAAuB,CAAC5C,CAAC,EAAE,SAAS,CAAE;YACvDhD,WAAW,EAAC,yCAAuB;YACnCC,KAAK,EAAC;UAAS;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACFjC,OAAA,CAAChB,aAAa;YACVgG,QAAQ,EAAEmB,WAAW,CAACnB,QAAS;YAC/BC,WAAW,EAAGC,KAAK,IAAKkB,uBAAuB,CAAC;cAAE7E,MAAM,EAAE;gBAAEjB,KAAK,EAAE4E;cAAM;YAAE,CAAC,EAAE,UAAU,CAAE;YAC1FC,aAAa,EAAEA,CAAA,KAAMiB,uBAAuB,CAAC;cAAE7E,MAAM,EAAE;gBAAEjB,KAAK,EAAE;cAAG;YAAE,CAAC,EAAE,UAAU;UAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,EACDkE,WAAW,CAACf,cAAc,KAAK,KAAK,iBACjCpF,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAE,QAAA,EACrBsE,WAAW,CAACrC,UAAU,CAACuB,GAAG,CAAC,CAACC,SAAS,EAAE1B,KAAK,kBACzC5D,OAAA;cAAiB2B,SAAS,EAAC,yCAAyC;cAAAE,QAAA,gBAChE7B,OAAA;gBAAK2B,SAAS,EAAC,yCAAyC;gBAAAE,QAAA,gBACpD7B,OAAA;kBAAG2B,SAAS,EAAC,qCAAqC;kBAAAE,QAAA,EAC7CsE,WAAW,CAACf,cAAc,KAAK,IAAI,GAAGvC,QAAQ,CAACe,KAAK,CAAC,GAAGd,QAAQ,CAACc,KAAK;gBAAC;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC,eACJjC,OAAA,CAACG,cAAc;kBACXG,KAAK,EAAEgF,SAAS,CAACP,OAAQ;kBACzBxE,QAAQ,EAAGiD,CAAC,IAAK6C,wBAAwB,CAACzC,KAAK,EAAEJ,CAAC,CAACjC,MAAM,CAACjB,KAAK,EAAE,SAAS,CAAE;kBAC5EE,WAAW,EAAC;gBAAuB;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACFjC,OAAA;kBAAK2B,SAAS,EAAC,yBAAyB;kBAAAE,QAAA,gBACpC7B,OAAA;oBAAO2B,SAAS,EAAC,wCAAwC;oBAAAE,QAAA,gBACrD7B,OAAA;sBACIuF,IAAI,EAAC,OAAO;sBACZC,IAAI,eAAA5D,MAAA,CAAegC,KAAK,CAAG;sBAC3B6B,OAAO,EAAEH,SAAS,CAACI,SAAU;sBAC7BnF,QAAQ,EAAEA,CAAA,KAAM8F,wBAAwB,CAACzC,KAAK,EAAE,IAAI,EAAE,WAAW,CAAE;sBACnEjC,SAAS,EAAC;oBAA6C;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACFjC,OAAA;sBAAM2B,SAAS,EAAC,oCAAoC;sBAAAE,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACRjC,OAAA;oBAAO2B,SAAS,EAAC,wCAAwC;oBAAAE,QAAA,gBACrD7B,OAAA;sBACIuF,IAAI,EAAC,OAAO;sBACZC,IAAI,eAAA5D,MAAA,CAAegC,KAAK,CAAG;sBAC3B6B,OAAO,EAAE,CAACH,SAAS,CAACI,SAAU;sBAC9BnF,QAAQ,EAAEA,CAAA,KAAM8F,wBAAwB,CAACzC,KAAK,EAAE,KAAK,EAAE,WAAW,CAAE;sBACpEjC,SAAS,EAAC;oBAAyC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACFjC,OAAA;sBAAM2B,SAAS,EAAC,kCAAkC;sBAAAE,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNjC,OAAA,CAAChB,aAAa;gBACVgG,QAAQ,EAAEM,SAAS,CAACN,QAAS;gBAC7BC,WAAW,EAAGC,KAAK,IAAKmB,wBAAwB,CAACzC,KAAK,EAAEsB,KAAK,EAAE,UAAU,CAAE;gBAC3EC,aAAa,EAAEA,CAAA,KAAMkB,wBAAwB,CAACzC,KAAK,EAAE,EAAE,EAAE,UAAU;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC;YAAA,GArCI2B,KAAK;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsCV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EACAkE,WAAW,CAACf,cAAc,KAAK,KAAK,iBACjCpF,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAE,QAAA,eACtB7B,OAAA,CAACjB,QAAQ;cACLuB,KAAK,EAAE6F,WAAW,CAACR,aAAc;cACjCpF,QAAQ,EAAGiD,CAAC,IAAK4C,uBAAuB,CAAC5C,CAAC,EAAE,eAAe,CAAE;cAC7DhD,WAAW,EAAC,6BAAa;cACzBC,KAAK,EAAC,mBAAQ;cACdC,IAAI,EAAEvB;YAAY;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNjC,OAAA;QAAK2B,SAAS,EAAC,WAAW;QAAAE,QAAA,gBACtB7B,OAAA,CAACjB,QAAQ;UACLuB,KAAK,EAAE6F,WAAW,CAACP,WAAY;UAC/BrF,QAAQ,EAAGiD,CAAC,IAAK4C,uBAAuB,CAAC5C,CAAC,EAAE,aAAa,CAAE;UAC3DhD,WAAW,EAAC,2CAA4B;UACxCC,KAAK,EAAC,yBAAe;UACrBC,IAAI,EAAErB;QAAM;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,EACDkE,WAAW,CAACP,WAAW,iBACpB5F,OAAA,CAACF,aAAa;UAAC+F,GAAG,EAAEM,WAAW,CAACP;QAAY;UAAA9D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACjD,eACDjC,OAAA,CAACf,cAAc;UACX+E,QAAQ,EAAEmC,WAAW,CAACnC,QAAS;UAC/B8B,gBAAgB,EAAEQ;QAAgC;UAAAxE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAiE,GAAA,CAhKKD,eAAe;EAAA,QACArH,WAAW,EACJD,WAAW,EACjBA,WAAW;AAAA;AAAA6H,GAAA,GAH3BP,eAAe;AAmKrB,MAAMQ,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtB,MAAM,CAAChE,IAAI,EAAEiE,OAAO,CAAC,GAAGjI,QAAQ,CAAC,gBAAgB,CAAC;EAClD,MAAM;IAAEyH;EAAY,CAAC,GAAGxH,WAAW,CAAEgE,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EACnE,MAAM3B,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAE9B,MAAMgI,iBAAiB,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACT,WAAW,CAACpB,OAAO,CAAC9D,IAAI,CAAC,CAAC,EAAE;MAC7BJ,QAAQ,CAAChB,eAAe,CAAC,uCAAuC,CAAC,CAAC;MAClE;IACJ;IACA,IAAI,CAACsG,WAAW,CAACf,cAAc,EAAE;MAC7BvE,QAAQ,CAAChB,eAAe,CAAC,mCAAmC,CAAC,CAAC;MAC9D;IACJ;IACA,IAAI,CAACsG,WAAW,CAAC7B,KAAK,EAAE;MACpBzD,QAAQ,CAAChB,eAAe,CAAC,0BAA0B,CAAC,CAAC;MACrD;IACJ;IACA,IAAIsG,WAAW,CAACf,cAAc,KAAK,KAAK,IAAIe,WAAW,CAACrC,UAAU,CAACS,MAAM,CAACe,SAAS,IAAIA,SAAS,CAACP,OAAO,CAAC9D,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAACiC,MAAM,GAAG,CAAC,EAAE;MAChIrC,QAAQ,CAAChB,eAAe,CAAC,sCAAsC,CAAC,CAAC;MACjE;IACJ;IACA,IAAIsG,WAAW,CAACf,cAAc,KAAK,KAAK,IAAI,CAACe,WAAW,CAACR,aAAa,CAAC1E,IAAI,CAAC,CAAC,EAAE;MAC3EJ,QAAQ,CAAChB,eAAe,CAAC,6BAA6B,CAAC,CAAC;MACxD;IACJ;IACAgB,QAAQ,CAACnB,WAAW,CAAC,CAAC,CAAC;IACvBiH,OAAO,CAAC,gBAAgB,CAAC;EAC7B,CAAC;EAID,oBACI3G,OAAA;IAAK2B,SAAS,EAAC,uDAAuD;IAAAE,QAAA,gBAElE7B,OAAA;MAAK2B,SAAS,EAAC,oFAAoF;MAAAE,QAAA,gBAC/F7B,OAAA;QAAK2B,SAAS,EAAC,yBAAyB;QAAAE,QAAA,eACpC7B,OAAA;UAAI2B,SAAS,EAAC,qCAAqC;UAAAE,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACNjC,OAAA;QAAK2B,SAAS,EAAC,yBAAyB;QAAAE,QAAA,EAEhCa,IAAI,KAAK,gBAAgB,gBACrB1C,OAAA;UACIkC,OAAO,EAAEA,CAAA,KAAMyE,OAAO,CAAC,aAAa,CAAE;UACtChF,SAAS,+FAAgG;UAAAE,QAAA,EAC5G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAETjC,OAAA,CAAAE,SAAA;UAAA2B,QAAA,gBACI7B,OAAA;YACIkC,OAAO,EAAE0E,iBAAkB;YAC3BjF,SAAS,uGAAwG;YAAAE,QAAA,EACpH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjC,OAAA;YACIkC,OAAO,EAAEA,CAAA,KAAMyE,OAAO,CAAC,gBAAgB,CAAE;YACzChF,SAAS,+FAAgG;YAAAE,QAAA,EAC5G;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACX;MACL;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACLS,IAAI,KAAK,gBAAgB,iBAAI1C,OAAA,CAACsC,kBAAkB;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACnDS,IAAI,KAAK,aAAa,iBAAI1C,OAAA,CAACiG,eAAe;MAAAnE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7C,CAAC;AAEd,CAAC;AAAAyE,GAAA,CAvEKD,WAAW;EAAA,QAEW9H,WAAW,EAClBC,WAAW;AAAA;AAAAiI,GAAA,GAH1BJ,WAAW;AA0EjB,eAAeA,WAAW;AAAC,IAAApE,EAAA,EAAA2D,GAAA,EAAAQ,GAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAzE,EAAA;AAAAyE,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}