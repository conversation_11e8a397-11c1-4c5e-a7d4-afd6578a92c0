import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type LibrariesDocumentsGetExtractedTextSignedUrlV1Request = {
    libraryId: string;
    documentId: string;
};
/** @internal */
export declare const LibrariesDocumentsGetExtractedTextSignedUrlV1Request$inboundSchema: z.ZodType<LibrariesDocumentsGetExtractedTextSignedUrlV1Request, z.ZodTypeDef, unknown>;
/** @internal */
export type LibrariesDocumentsGetExtractedTextSignedUrlV1Request$Outbound = {
    library_id: string;
    document_id: string;
};
/** @internal */
export declare const LibrariesDocumentsGetExtractedTextSignedUrlV1Request$outboundSchema: z.ZodType<LibrariesDocumentsGetExtractedTextSignedUrlV1Request$Outbound, z.ZodTypeDef, LibrariesDocumentsGetExtractedTextSignedUrlV1Request>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace LibrariesDocumentsGetExtractedTextSignedUrlV1Request$ {
    /** @deprecated use `LibrariesDocumentsGetExtractedTextSignedUrlV1Request$inboundSchema` instead. */
    const inboundSchema: z.ZodType<LibrariesDocumentsGetExtractedTextSignedUrlV1Request, z.ZodTypeDef, unknown>;
    /** @deprecated use `LibrariesDocumentsGetExtractedTextSignedUrlV1Request$outboundSchema` instead. */
    const outboundSchema: z.ZodType<LibrariesDocumentsGetExtractedTextSignedUrlV1Request$Outbound, z.ZodTypeDef, LibrariesDocumentsGetExtractedTextSignedUrlV1Request>;
    /** @deprecated use `LibrariesDocumentsGetExtractedTextSignedUrlV1Request$Outbound` instead. */
    type Outbound = LibrariesDocumentsGetExtractedTextSignedUrlV1Request$Outbound;
}
export declare function librariesDocumentsGetExtractedTextSignedUrlV1RequestToJSON(librariesDocumentsGetExtractedTextSignedUrlV1Request: LibrariesDocumentsGetExtractedTextSignedUrlV1Request): string;
export declare function librariesDocumentsGetExtractedTextSignedUrlV1RequestFromJSON(jsonString: string): SafeParseResult<LibrariesDocumentsGetExtractedTextSignedUrlV1Request, SDKValidationError>;
//# sourceMappingURL=librariesdocumentsgetextractedtextsignedurlv1.d.ts.map