import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type FilesApiRoutesDownloadFileRequest = {
    fileId: string;
};
/** @internal */
export declare const FilesApiRoutesDownloadFileRequest$inboundSchema: z.ZodType<FilesApiRoutesDownloadFileRequest, z.ZodTypeDef, unknown>;
/** @internal */
export type FilesApiRoutesDownloadFileRequest$Outbound = {
    file_id: string;
};
/** @internal */
export declare const FilesApiRoutesDownloadFileRequest$outboundSchema: z.ZodType<FilesApiRoutesDownloadFileRequest$Outbound, z.ZodTypeDef, FilesApiRoutesDownloadFileRequest>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace FilesApiRoutesDownloadFileRequest$ {
    /** @deprecated use `FilesApiRoutesDownloadFileRequest$inboundSchema` instead. */
    const inboundSchema: z.ZodType<FilesApiRoutesDownloadFileRequest, z.ZodTypeDef, unknown>;
    /** @deprecated use `FilesApiRoutesDownloadFileRequest$outboundSchema` instead. */
    const outboundSchema: z.ZodType<FilesApiRoutesDownloadFileRequest$Outbound, z.ZodTypeDef, FilesApiRoutesDownloadFileRequest>;
    /** @deprecated use `FilesApiRoutesDownloadFileRequest$Outbound` instead. */
    type Outbound = FilesApiRoutesDownloadFileRequest$Outbound;
}
export declare function filesApiRoutesDownloadFileRequestToJSON(filesApiRoutesDownloadFileRequest: FilesApiRoutesDownloadFileRequest): string;
export declare function filesApiRoutesDownloadFileRequestFromJSON(jsonString: string): SafeParseResult<FilesApiRoutesDownloadFileRequest, SDKValidationError>;
//# sourceMappingURL=filesapiroutesdownloadfile.d.ts.map