import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import DropMenuBarAdmin from "src/components/dropMenu/OptionBarAdmin";
import SuggestInputBarAdmin from "src/components/input/suggestInputBarAdmin";
import TextArea from "src/components/input/TextArea";
import ImageDropZone from "src/components/image/ImageDropZone";
import SolutionEditor from "src/components/PageAddExam/SolutionEditor";
import { setQuestion } from "src/features/questionsExam/questionsExamSlice";
import { CheckCircle, Plus, Video, Sparkles, Trash2 } from "lucide-react";
import { setQuestions, setNewQuestion, addQuestion } from "src/features/questionsExam/questionsExamSlice";
import { addStatement } from "src/features/questionsExam/questionsExamSlice";
import { fixTextAndLatex } from "src/features/ai/aiSlice";
import { setErrorMessage } from "src/features/state/stateApiSlice";
import YouTubePlayer from "../YouTubePlayer";

// Component TextArea với nút AI
const TextAreaWithAI = ({ value, onChange, placeholder, label, Icon, row = false, short = false }) => {
    const dispatch = useDispatch();
    const [loading, setLoading] = useState(false);
    const handleAIFix = async () => {
        if (!value || !value.trim()) {
            alert('Vui lòng nhập nội dung trước khi sử dụng AI sửa lỗi');
            return;
        }

        try {
            setLoading(true);
            const result = await dispatch(fixTextAndLatex(value)).unwrap();
            if (result.data.hasChanges) {
                onChange({ target: { value: result.data.fixedText } });
            } else {
                alert('Không tìm thấy lỗi nào cần sửa');
            }
            setLoading(false);
        } catch (error) {
            console.error('Error fixing text:', error);
            alert('Có lỗi xảy ra khi sử dụng AI sửa lỗi');
        }
    };

    return (
        <div className={`flex gap-2 ${row ? 'flex-row-reverse' : 'flex-col'}`}>
            <div className="flex items-center justify-between">
                <label className="flex items-center gap-1 text-sm font-medium text-gray-700">
                    {Icon && <Icon className="w-4 h-4" />}
                </label>
                <button
                    onClick={handleAIFix}
                    disabled={loading || !value?.trim()}
                    className="flex items-center gap-1 px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    title="Sử dụng AI để sửa chính tả và ký hiệu LaTeX"
                >
                    <Sparkles className="w-3 h-3" />
                    {!short && (loading ? 'Đang sửa...' : 'AI Fix')}
                </button>
            </div>
            <TextArea
                value={value}
                onChange={onChange}
                placeholder={placeholder}
            />
        </div>
    );
};

const DetailQuestionView = () => {
    const { questionsExam, selectedId, view } = useSelector((state) => state.questionsExam);
    const { codes } = useSelector((state) => state.codes);
    const dispatch = useDispatch();
    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];
    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];
    const [optionChapter, setOptionChapter] = useState([]);
    const [question, setQuestion] = useState(null);

    useEffect(() => {
        // console.log(selectedId);

        if (selectedId && questionsExam?.length > 0) {
            const selectedQuestion = questionsExam?.find(q => q.id === selectedId);
            setQuestion(selectedQuestion);
        }
    }, [selectedId, questionsExam]);

    const handleQuestionChange = (e, field) => {
        const updatedQuestion = { ...question, [field]: e.target.value };
        dispatch(setQuestions(updatedQuestion));
    };

    const handleStatementChange = (index, value, field) => {
        const updatedStatements = [...question.statements];
        updatedStatements[index] = { ...updatedStatements[index], [field]: value };
        const updatedQuestion = { ...question, statements: updatedStatements };
        dispatch(setQuestions(updatedQuestion));
    };

    const handleSolutionQuestionChange = (value) => {
        const updatedQuestion = { ...question, solution: value };
        dispatch(setQuestions(updatedQuestion));
    };

    const handleAddStatement = () => {
        dispatch(addStatement());
    };

    const handleRemoveStatement = (index) => {
        const updatedStatements = [...question.statements];
        updatedStatements.splice(index, 1);
        const updatedQuestion = { ...question, statements: updatedStatements };
        dispatch(setQuestions(updatedQuestion));
    };

    useEffect(() => {
        if (Array.isArray(codes["chapter"])) {
            if (question?.class && question?.class.trim() !== "") {
                setOptionChapter(
                    codes["chapter"].filter((code) => code.code.startsWith(question?.class) && code.code.length === 5)
                );
            } else {
                setOptionChapter(codes["chapter"].filter((code) => code.code.length === 5));
            }
        } else {
            setOptionChapter([]);
        }
    }, [codes, question?.class]);

    return (
        <div className="space-y-3 p-3 w-full">
            {question && (
                <div className="space-y-3 w-full">
                    <div className="flex flex-col gap-2">
                        <h3 className="text-sm font-medium text-gray-900 mb-1">Phân loại</h3>
                        <div className="flex flex-row gap-2">
                            <DropMenuBarAdmin
                                selectedOption={question.class}
                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'class')}
                                options={Array.isArray(codes["grade"]) ? codes["grade"] : []}
                                className="text-xs"
                            />
                            <SuggestInputBarAdmin
                                selectedOption={question.chapter}
                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'chapter')}
                                options={optionChapter}
                                className="text-xs"
                            />
                            <DropMenuBarAdmin
                                selectedOption={question.difficulty}
                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'difficulty')}
                                options={Array.isArray(codes["difficulty"]) ? codes["difficulty"] : []}
                                className="text-xs"
                            />
                        </div>
                    </div>
                    <hr className=" bg-gray-200"></hr>
                    <div className="flex flex-col gap-2">
                        <h3 className="text-sm font-medium text-gray-900 mb-1">Thông tin câu hỏi</h3>
                        <div className="space-y-2">
                            <TextAreaWithAI
                                value={question.content}
                                onChange={(e) => handleQuestionChange(e, 'content')}
                                placeholder="Nhập nội dung câu hỏi"
                                label="Câu hỏi"
                                row={false}
                            />
                            {(view === 'image' || question.imageUrl) && (
                                <ImageDropZone
                                    imageUrl={question.imageUrl}
                                    onImageDrop={(image) => handleQuestionChange({ target: { value: image } }, 'imageUrl')}
                                    onImageRemove={() => handleQuestionChange({ target: { value: '' } }, 'imageUrl')}
                                />
                            )}
                            {question.typeOfQuestion !== 'TLN' && (
                                <div className="space-y-2">
                                    {question.statements.length < 4 && (
                                        <button
                                            onClick={() => handleAddStatement()}
                                            className="text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-blue-600 hover:bg-blue-700 text-white"
                                        >
                                            <Plus className="w-3 h-3" /> Thêm mệnh đề
                                        </button>
                                    )}
                                    {question.statements.map((statement, index) => (
                                        <div key={index} className="flex flex-col gap-2 items-center w-full">
                                            <div className="flex flex-row gap-2 items-center w-full">
                                                <p className="text-xs font-bold whitespace-nowrap">
                                                    {question.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}
                                                </p>
                                                <TextAreaWithAI
                                                    value={statement.content}
                                                    onChange={(e) => handleStatementChange(index, e.target.value, 'content')}
                                                    placeholder="Nhập nội dung mệnh đề"
                                                    short={true}
                                                    row={true}
                                                />
                                                {question.typeOfQuestion !== 'TLN' && (
                                                    <div className="flex items-center gap-2">
                                                        <label className="flex items-center gap-2 cursor-pointer">
                                                            <input
                                                                type="radio"
                                                                name={`statement-${index}`}
                                                                checked={statement.isCorrect}
                                                                onChange={() => handleStatementChange(index, true, 'isCorrect')}
                                                                className="w-4 h-4 text-green-600 focus:ring-green-500"
                                                            />
                                                            <span className="text-sm text-green-600 font-medium">Đúng</span>
                                                        </label>
                                                        <label className="flex items-center gap-2 cursor-pointer">
                                                            <input
                                                                type="radio"
                                                                name={`statement-${index}`}
                                                                checked={!statement.isCorrect}
                                                                onChange={() => handleStatementChange(index, false, 'isCorrect')}
                                                                className="w-4 h-4 text-red-600 focus:ring-red-500"
                                                            />
                                                            <span className="text-sm text-red-600 font-medium">Sai</span>
                                                        </label>
                                                    </div>
                                                )}
                                                <button
                                                    onClick={() => handleRemoveStatement(index)}
                                                    className="text-xs flex items-center p-1 rounded-full bg-red-50 hover:bg-red-200 text-red-600"
                                                >
                                                    <Trash2 className="w-3 h-3" />
                                                </button>
                                            </div>
                                            {(view === 'image' || statement.imageUrl) && (
                                                <ImageDropZone
                                                    imageUrl={statement.imageUrl}
                                                    onImageDrop={(image) => handleStatementChange(index, image, 'imageUrl')}
                                                    onImageRemove={() => handleStatementChange(index, '', 'imageUrl')}
                                                />
                                            )}

                                        </div>
                                    ))}
                                </div>
                            )}
                            {question.typeOfQuestion === 'TLN' && (
                                <div className="space-y-2">
                                    <TextArea
                                        value={question.correctAnswer}
                                        onChange={(e) => handleQuestionChange(e, 'correctAnswer')}
                                        placeholder="Nhập đáp án"
                                        label="Đáp án"
                                        Icon={CheckCircle}
                                    />
                                </div>
                            )}
                            {/* <TextArea
                                        value={question.solution}
                                        onChange={(e) => handleQuestionChange(e, 'solution')}
                                        placeholder="Nhập lời giải"
                                        label="Lời giải"
                                        Icon={CheckCircle}
                                    /> */}
                            <TextArea
                                value={question.solutionUrl || ''}
                                onChange={(e) => handleQuestionChange(e, 'solutionUrl')}
                                placeholder="Nhập link lời giải youtube"
                                label="Link lời giải"
                                Icon={Video}
                            />
                            {question.solutionUrl && (
                                <YouTubePlayer url={question.solutionUrl} />
                            )}
                            <SolutionEditor
                                solution={question.solution}
                                onSolutionChange={handleSolutionQuestionChange}
                                preview={false}
                            />
                        </div>
                    </div>

                </div>
            )}
        </div>
    )
}

const AddQuestionView = () => {
    const dispatch = useDispatch();
    const { newQuestion } = useSelector((state) => state.questionsExam);
    const { codes } = useSelector((state) => state.codes);
    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];
    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];
    const [optionChapter, setOptionChapter] = useState([]);
    const handleNewQuestionChange = (e, field) => {
        const updatedQuestion = { ...newQuestion, [field]: e.target.value };
        dispatch(setNewQuestion(updatedQuestion));
    };

    useEffect(() => {
        if (Array.isArray(codes["chapter"])) {
            if (newQuestion?.class && newQuestion?.class.trim() !== "") {
                setOptionChapter(
                    codes["chapter"].filter((code) => code.code.startsWith(newQuestion?.class) && code.code.length === 5)
                );
            } else {
                setOptionChapter(codes["chapter"].filter((code) => code.code.length === 5));
            }
        } else {
            setOptionChapter([]);
        }
    }, [codes, newQuestion?.class]);

    const handleNewStatementChange = (index, value, field) => {
        const updatedStatements = [...newQuestion.statements];
        updatedStatements[index] = { ...updatedStatements[index], [field]: value };
        const updatedQuestion = { ...newQuestion, statements: updatedStatements };
        dispatch(setNewQuestion(updatedQuestion));
    };

    const handleNewSolutionQuestionChange = (value) => {
        const updatedQuestion = { ...newQuestion, solution: value };
        dispatch(setNewQuestion(updatedQuestion));
    };

    return (
        <div className="space-y-3 p-3 w-full">
            <div className="flex flex-col gap-2">
                <h3 className="text-sm font-medium text-gray-900 mb-1">Phân loại</h3>
                <div className="flex flex-row gap-2">
                    <DropMenuBarAdmin
                        selectedOption={newQuestion.typeOfQuestion}
                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'typeOfQuestion')}
                        options={[{ code: "TN", description: "Trắc nghiệm" }, { code: "DS", description: "Đúng sai" }, { code: "TLN", description: "Trả lời ngắn" }]}
                        className="text-xs"
                    />
                    <DropMenuBarAdmin
                        selectedOption={newQuestion.class}
                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'class')}
                        options={Array.isArray(codes["grade"]) ? codes["grade"] : []}
                        className="text-xs"
                    />
                    <SuggestInputBarAdmin
                        selectedOption={newQuestion.chapter}
                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'chapter')}
                        options={optionChapter}
                        className="text-xs"
                    />
                    <DropMenuBarAdmin
                        selectedOption={newQuestion.difficulty}
                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'difficulty')}
                        options={Array.isArray(codes["difficulty"]) ? codes["difficulty"] : []}
                        className="text-xs"
                    />
                </div>
                <hr className=" bg-gray-200"></hr>
                <div className="flex flex-col gap-2">
                    <h3 className="text-sm font-medium text-gray-900 mb-1">Thông tin câu hỏi</h3>
                    <div className="space-y-2">
                        <TextAreaWithAI
                            value={newQuestion.content}
                            onChange={(e) => handleNewQuestionChange(e, 'content')}
                            placeholder="Nhập nội dung câu hỏi"
                            label="Câu hỏi"
                        />
                        <ImageDropZone
                            imageUrl={newQuestion.imageUrl}
                            onImageDrop={(image) => handleNewQuestionChange({ target: { value: image } }, 'imageUrl')}
                            onImageRemove={() => handleNewQuestionChange({ target: { value: '' } }, 'imageUrl')}
                        />
                        {newQuestion.typeOfQuestion !== 'TLN' && (
                            <div className="space-y-2">
                                {newQuestion.statements.map((statement, index) => (
                                    <div key={index} className="flex flex-col gap-2 items-center w-full">
                                        <div className="flex flex-row gap-2 items-center w-full">
                                            <p className="text-xs font-bold whitespace-nowrap">
                                                {newQuestion.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}
                                            </p>
                                            <TextAreaWithAI
                                                value={statement.content}
                                                onChange={(e) => handleNewStatementChange(index, e.target.value, 'content')}
                                                placeholder="Nhập nội dung mệnh đề"
                                            />
                                            <div className="flex items-center gap-2">
                                                <label className="flex items-center gap-2 cursor-pointer">
                                                    <input
                                                        type="radio"
                                                        name={`statement-${index}`}
                                                        checked={statement.isCorrect}
                                                        onChange={() => handleNewStatementChange(index, true, 'isCorrect')}
                                                        className="w-4 h-4 text-green-600 focus:ring-green-500"
                                                    />
                                                    <span className="text-sm text-green-600 font-medium">Đúng</span>
                                                </label>
                                                <label className="flex items-center gap-2 cursor-pointer">
                                                    <input
                                                        type="radio"
                                                        name={`statement-${index}`}
                                                        checked={!statement.isCorrect}
                                                        onChange={() => handleNewStatementChange(index, false, 'isCorrect')}
                                                        className="w-4 h-4 text-red-600 focus:ring-red-500"
                                                    />
                                                    <span className="text-sm text-red-600 font-medium">Sai</span>
                                                </label>
                                            </div>
                                        </div>
                                        <ImageDropZone
                                            imageUrl={statement.imageUrl}
                                            onImageDrop={(image) => handleNewStatementChange(index, image, 'imageUrl')}
                                            onImageRemove={() => handleNewStatementChange(index, '', 'imageUrl')}
                                        />
                                    </div>
                                ))}
                            </div>
                        )}
                        {newQuestion.typeOfQuestion === 'TLN' && (
                            <div className="space-y-2">
                                <TextArea
                                    value={newQuestion.correctAnswer}
                                    onChange={(e) => handleNewQuestionChange(e, 'correctAnswer')}
                                    placeholder="Nhập đáp án"
                                    label="Đáp án"
                                    Icon={CheckCircle}
                                />
                            </div>
                        )}
                    </div>
                </div>
                <div className="space-y-2">
                    <TextArea
                        value={newQuestion.solutionUrl}
                        onChange={(e) => handleNewQuestionChange(e, 'solutionUrl')}
                        placeholder="Nhập link lời giải youtube"
                        label="Link lời giải"
                        Icon={Video}
                    />
                    {newQuestion.solutionUrl && (
                        <YouTubePlayer url={newQuestion.solutionUrl} />
                    )}
                    <SolutionEditor
                        solution={newQuestion.solution}
                        onSolutionChange={handleNewSolutionQuestionChange}
                    />
                </div>
            </div>
        </div>
    )
}


const LeftContent = () => {
    const [view, setView] = useState('questionDetail');
    const { newQuestion } = useSelector((state) => state.questionsExam);
    const dispatch = useDispatch();

    const handleAddQuestion = () => {
        if (!newQuestion.content.trim()) {
            dispatch(setErrorMessage("Nội dung câu hỏi không được để trống!"));
            return;
        }
        if (!newQuestion.typeOfQuestion) {
            dispatch(setErrorMessage("Loại câu hỏi không được để trống!"));
            return;
        }
        if (!newQuestion.class) {
            dispatch(setErrorMessage("Lớp không được để trống!"));
            return;
        }
        if (newQuestion.typeOfQuestion !== 'TLN' && newQuestion.statements.filter(statement => statement.content.trim() !== "").length < 4) {
            dispatch(setErrorMessage("Câu hỏi TN phải có ít nhất 4 đáp án!"));
            return;
        }
        if (newQuestion.typeOfQuestion === 'TLN' && !newQuestion.correctAnswer.trim()) {
            dispatch(setErrorMessage("Đáp án không được để trống!"));
            return;
        }
        dispatch(addQuestion());
        setView('questionDetail');
    };



    return (
        <div className="flex flex-col h-[calc(100vh_-_138px)] overflow-y-auto">
            {/* Compact Preview Header */}
            <div className="flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10">
                <div className="flex items-center gap-2">
                    <h2 className="text-xs font-semibold text-gray-900">Chi tiết câu hỏi</h2>
                </div>
                <div className="flex items-center gap-2">
                    {
                        view === 'questionDetail' ? (
                            <button
                                onClick={() => setView('addQuestion')}
                                className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-sky-600 hover:bg-sky-700 text-white`}
                            >
                                Thêm câu hỏi
                            </button>
                        ) : (
                            <>
                                <button
                                    onClick={handleAddQuestion}
                                    className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-emerald-600 hover:bg-emerald-700 text-white`}
                                >
                                    Lưu
                                </button>
                                <button
                                    onClick={() => setView('questionDetail')}
                                    className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-red-600 hover:bg-red-700 text-white`}
                                >
                                    Hủy
                                </button>
                            </>
                        )
                    }
                </div>
            </div>
            {view === 'questionDetail' && <DetailQuestionView />}
            {view === 'addQuestion' && <AddQuestionView />}
        </div>
    )
}


export default LeftContent;