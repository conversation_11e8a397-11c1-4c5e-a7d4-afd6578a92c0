{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\RightContent.jsx\",\n  _s = $RefreshSig$();\nimport NavigateBar from \"../PageAddExam/NavigateBar\";\nimport { Eye, Edit, Sparkles } from \"lucide-react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setViewRightContent, putQuestionsExam, setQuestions, setAllQuestions } from \"src/features/questionsExam/questionsExamSlice\";\nimport QuestionView from \"./QuestionView\";\nimport ImageView from \"./ImageView\";\nimport LoadingSpinner from \"../loading/LoadingSpinner\";\nimport { classifyQuestions } from \"src/features/ai/aiSlice\";\nimport { useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RightContent = _ref => {\n  _s();\n  let {\n    examId\n  } = _ref;\n  const {\n    view,\n    loadingPut,\n    questionsExam\n  } = useSelector(state => state.questionsExam);\n  const {\n    classifyResult,\n    loading: aiLoading\n  } = useSelector(state => state.ai);\n  const dispatch = useDispatch();\n  const handlePutQuestions = () => {\n    if (!examId) return;\n    const questionsData = questionsExam.map((question, index) => {\n      var _question$ExamQuestio;\n      return {\n        id: question.id,\n        content: question.content,\n        correctAnswer: question.correctAnswer || null,\n        difficulty: question.difficulty || null,\n        chapter: question.chapter || null,\n        class: question.class,\n        solution: question.solution,\n        solutionUrl: question.solutionUrl || null,\n        imageUrl: question.imageUrl || null,\n        solutionImageUrl: question.solutionImageUrl || null,\n        statements: question.statements.map(statement => ({\n          id: statement.id,\n          content: statement.content,\n          isCorrect: statement.isCorrect,\n          difficulty: statement.difficulty,\n          imageUrl: statement.imageUrl,\n          isNewStatement: statement.isNewStatement || false\n        })),\n        ExamQuestions: {\n          order: ((_question$ExamQuestio = question.ExamQuestions) === null || _question$ExamQuestio === void 0 ? void 0 : _question$ExamQuestio.order) || index\n        },\n        isNewQuestion: question.isNewQuestion || false,\n        typeOfQuestion: question.typeOfQuestion,\n        description: question.description\n      };\n    });\n    // console.log(\"questionsData\", questionsData)\n\n    dispatch(putQuestionsExam({\n      examId,\n      questions: questionsData\n    }));\n  };\n  const handleClassifyQuestions = async () => {\n    // console.log(questionsExam)\n    const questionsData = questionsExam === null || questionsExam === void 0 ? void 0 : questionsExam.map((question, index) => {\n      return {\n        originalIndex: index,\n        class: question.class || \"\",\n        chapter: question.chapter || \"\",\n        difficulty: question.difficulty || \"\",\n        typeOfQuestion: question.typeOfQuestion || \"TN\",\n        content: question.content || \"\",\n        statements: question.statements && question.statements.length > 0 ? question.statements.map(statement => statement.content) : []\n      };\n    });\n    // const data = await dispatch(classifyQuestions(questionsData)).unwrap();\n    // const result = data.data;\n    const result = [{\n      \"originalIndex\": 0,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"NB\"\n    }, {\n      \"originalIndex\": 1,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"NB\"\n    }, {\n      \"originalIndex\": 2,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"NB\"\n    }, {\n      \"originalIndex\": 3,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"NB\"\n    }, {\n      \"originalIndex\": 4,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"NB\"\n    }, {\n      \"originalIndex\": 5,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"TH\"\n    }, {\n      \"originalIndex\": 6,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"TH\"\n    }, {\n      \"originalIndex\": 7,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"TH\"\n    }, {\n      \"originalIndex\": 8,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"TH\"\n    }, {\n      \"originalIndex\": 9,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"TH\"\n    }, {\n      \"originalIndex\": 10,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"TH\"\n    }, {\n      \"originalIndex\": 11,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"TH\"\n    }, {\n      \"originalIndex\": 12,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"VD\"\n    }, {\n      \"originalIndex\": 13,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"VD\"\n    }, {\n      \"originalIndex\": 14,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"VD\"\n    }, {\n      \"originalIndex\": 15,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"VD\"\n    }, {\n      \"originalIndex\": 16,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"VDC\"\n    }, {\n      \"originalIndex\": 17,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"VDC\"\n    }, {\n      \"originalIndex\": 18,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"VDC\"\n    }, {\n      \"originalIndex\": 19,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"VDC\"\n    }, {\n      \"originalIndex\": 20,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"VDC\"\n    }, {\n      \"originalIndex\": 21,\n      \"class\": \"12\",\n      \"chapter\": \"12C11\",\n      \"difficulty\": \"VDC\"\n    }];\n    const updatedQuestions = questionsExam.map((question, index) => {\n      const classifiedQuestion = result.find(cq => cq.originalIndex === index);\n      if (classifiedQuestion) {\n        return {\n          ...question,\n          class: classifiedQuestion.class,\n          chapter: classifiedQuestion.chapter,\n          difficulty: classifiedQuestion.difficulty\n        };\n      }\n      return question;\n    });\n    dispatch(setAllQuestions(updatedQuestions));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_138px)] bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Eye, {\n          className: \"w-3 h-3 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xs font-semibold text-gray-900\",\n          children: \"Xem tr\\u01B0\\u1EDBc\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleClassifyQuestions,\n          disabled: aiLoading || questionsExam.length === 0,\n          className: \"flex items-center gap-1 px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n          title: \"T\\u1EF1 \\u0111\\u1ED9ng ph\\xE2n lo\\u1EA1i c\\xE2u h\\u1ECFi b\\u1EB1ng AI\",\n          children: aiLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 33\n            }, this), \"\\u0110ang x\\u1EED l\\xFD...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n              className: \"w-3 h-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 33\n            }, this), \"Ph\\xE2n lo\\u1EA1i AI\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          disabled: loadingPut,\n          onClick: handlePutQuestions,\n          className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-orange-600 hover:bg-orange-700 text-white\",\n          children: [loadingPut ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n            className: \"w-3 h-3\",\n            minHeight: \"min-h-0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 39\n          }, this) : /*#__PURE__*/_jsxDEV(Edit, {\n            className: \"w-3 h-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 100\n          }, this), \"C\\u1EADp nh\\u1EADt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded border border-gray-200 \",\n        children: [/*#__PURE__*/_jsxDEV(NavigateBar, {\n          list: [{\n            id: 1,\n            name: 'Câu hỏi',\n            value: 'question'\n          }, {\n            id: 2,\n            name: 'Ảnh',\n            value: 'image'\n          }],\n          active: view,\n          setActive: value => dispatch(setViewRightContent(value))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 21\n        }, this), view === 'question' && /*#__PURE__*/_jsxDEV(QuestionView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 45\n        }, this), view === 'image' && /*#__PURE__*/_jsxDEV(ImageView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 42\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 217,\n    columnNumber: 9\n  }, this);\n};\n_s(RightContent, \"1Rr1VFIOXmbMxsEOHiN07rUDqo0=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = RightContent;\nexport default RightContent;\nvar _c;\n$RefreshReg$(_c, \"RightContent\");", "map": {"version": 3, "names": ["NavigateBar", "Eye", "Edit", "<PERSON><PERSON><PERSON>", "useSelector", "useDispatch", "setViewRightContent", "putQuestionsExam", "setQuestions", "setAllQuestions", "Question<PERSON>iew", "ImageView", "LoadingSpinner", "classifyQuestions", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RightContent", "_ref", "_s", "examId", "view", "loadingPut", "questionsExam", "state", "classifyResult", "loading", "aiLoading", "ai", "dispatch", "handlePutQuestions", "questionsData", "map", "question", "index", "_question$ExamQuestio", "id", "content", "<PERSON><PERSON><PERSON><PERSON>", "difficulty", "chapter", "class", "solution", "solutionUrl", "imageUrl", "solutionImageUrl", "statements", "statement", "isCorrect", "isNewStatement", "ExamQuestions", "order", "isNewQuestion", "typeOfQuestion", "description", "questions", "handleClassifyQuestions", "originalIndex", "length", "result", "updatedQuestions", "classifiedQuestion", "find", "cq", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "title", "minHeight", "list", "name", "value", "active", "setActive", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/RightContent.jsx"], "sourcesContent": ["import NavigateBar from \"../PageAddExam/NavigateBar\";\r\nimport { Eye, Edit, Sparkles } from \"lucide-react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { setViewRightContent, putQuestionsExam, setQuestions, setAllQuestions } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport QuestionView from \"./QuestionView\";\r\nimport ImageView from \"./ImageView\";\r\nimport LoadingSpinner from \"../loading/LoadingSpinner\";\r\nimport { classifyQuestions } from \"src/features/ai/aiSlice\";\r\nimport { useEffect } from \"react\";\r\n\r\nconst RightContent = ({ examId }) => {\r\n    const { view, loadingPut, questionsExam } = useSelector((state) => state.questionsExam);\r\n    const { classifyResult, loading: aiLoading } = useSelector((state) => state.ai);\r\n    const dispatch = useDispatch();\r\n\r\n    const handlePutQuestions = () => {\r\n        if (!examId) return;\r\n\r\n        const questionsData = questionsExam.map((question, index) => {\r\n            return {\r\n                id: question.id,\r\n                content: question.content,\r\n                correctAnswer: question.correctAnswer || null,\r\n                difficulty: question.difficulty || null,\r\n                chapter: question.chapter || null,\r\n                class: question.class,\r\n                solution: question.solution,\r\n                solutionUrl: question.solutionUrl || null,\r\n                imageUrl: question.imageUrl || null,\r\n                solutionImageUrl: question.solutionImageUrl || null,\r\n                statements: question.statements.map((statement) => ({\r\n                    id: statement.id,\r\n                    content: statement.content,\r\n                    isCorrect: statement.isCorrect,\r\n                    difficulty: statement.difficulty,\r\n                    imageUrl: statement.imageUrl,\r\n                    isNewStatement: statement.isNewStatement || false,\r\n                })),\r\n                ExamQuestions: {\r\n                    order: question.ExamQuestions?.order || index,\r\n                },\r\n                isNewQuestion: question.isNewQuestion || false,\r\n                typeOfQuestion: question.typeOfQuestion,\r\n                description: question.description,\r\n            }\r\n        })\r\n        // console.log(\"questionsData\", questionsData)\r\n\r\n        dispatch(putQuestionsExam({ examId, questions: questionsData }));\r\n    }\r\n\r\n    const handleClassifyQuestions = async () => {\r\n        // console.log(questionsExam)\r\n        const questionsData = questionsExam?.map((question, index) => {\r\n            return {\r\n                originalIndex: index,\r\n                class: question.class || \"\",\r\n                chapter: question.chapter || \"\",\r\n                difficulty: question.difficulty || \"\",\r\n                typeOfQuestion: question.typeOfQuestion || \"TN\",\r\n                content: question.content || \"\",\r\n                statements: question.statements && question.statements.length > 0 ? question.statements.map((statement) => statement.content) : [],\r\n            };\r\n        });\r\n        // const data = await dispatch(classifyQuestions(questionsData)).unwrap();\r\n        // const result = data.data;\r\n        const result = [\r\n            {\r\n                \"originalIndex\": 0,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"NB\"\r\n            },\r\n            {\r\n                \"originalIndex\": 1,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"NB\"\r\n            },\r\n            {\r\n                \"originalIndex\": 2,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"NB\"\r\n            },\r\n            {\r\n                \"originalIndex\": 3,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"NB\"\r\n            },\r\n            {\r\n                \"originalIndex\": 4,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"NB\"\r\n            },\r\n            {\r\n                \"originalIndex\": 5,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"TH\"\r\n            },\r\n            {\r\n                \"originalIndex\": 6,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"TH\"\r\n            },\r\n            {\r\n                \"originalIndex\": 7,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"TH\"\r\n            },\r\n            {\r\n                \"originalIndex\": 8,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"TH\"\r\n            },\r\n            {\r\n                \"originalIndex\": 9,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"TH\"\r\n            },\r\n            {\r\n                \"originalIndex\": 10,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"TH\"\r\n            },\r\n            {\r\n                \"originalIndex\": 11,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"TH\"\r\n            },\r\n            {\r\n                \"originalIndex\": 12,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"VD\"\r\n            },\r\n            {\r\n                \"originalIndex\": 13,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"VD\"\r\n            },\r\n            {\r\n                \"originalIndex\": 14,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"VD\"\r\n            },\r\n            {\r\n                \"originalIndex\": 15,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"VD\"\r\n            },\r\n            {\r\n                \"originalIndex\": 16,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"VDC\"\r\n            },\r\n            {\r\n                \"originalIndex\": 17,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"VDC\"\r\n            },\r\n            {\r\n                \"originalIndex\": 18,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"VDC\"\r\n            },\r\n            {\r\n                \"originalIndex\": 19,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"VDC\"\r\n            },\r\n            {\r\n                \"originalIndex\": 20,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"VDC\"\r\n            },\r\n            {\r\n                \"originalIndex\": 21,\r\n                \"class\": \"12\",\r\n                \"chapter\": \"12C11\",\r\n                \"difficulty\": \"VDC\"\r\n            }\r\n        ]\r\n        const updatedQuestions = questionsExam.map((question, index) => {\r\n            const classifiedQuestion = result.find(cq => cq.originalIndex === index);\r\n            if (classifiedQuestion) {\r\n                return {\r\n                    ...question,\r\n                    class: classifiedQuestion.class,\r\n                    chapter: classifiedQuestion.chapter,\r\n                    difficulty: classifiedQuestion.difficulty\r\n                };\r\n            }\r\n            return question;\r\n        });\r\n        dispatch(setAllQuestions(updatedQuestions));\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_138px)] bg-gray-50\">\r\n            {/* Compact Preview Header */}\r\n            <div className=\"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <Eye className=\"w-3 h-3 text-gray-600\" />\r\n                    <h2 className=\"text-xs font-semibold text-gray-900\">Xem trước</h2>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                    <button\r\n                        onClick={handleClassifyQuestions}\r\n                        disabled={aiLoading || questionsExam.length === 0}\r\n                        className=\"flex items-center gap-1 px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                        title=\"Tự động phân loại câu hỏi bằng AI\"\r\n                    >\r\n                        {aiLoading ? (\r\n                            <>\r\n                                <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-white\"></div>\r\n                                Đang xử lý...\r\n                            </>\r\n                        ) : (\r\n                            <>\r\n                                <Sparkles className=\"w-3 h-3\" />\r\n                                Phân loại AI\r\n                            </>\r\n                        )}\r\n                    </button>\r\n                    <button\r\n                        disabled={loadingPut}\r\n                        onClick={handlePutQuestions}\r\n                        className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-orange-600 hover:bg-orange-700 text-white`}\r\n                    >\r\n                        {loadingPut ? <LoadingSpinner className=\"w-3 h-3\" minHeight=\"min-h-0\" /> : <Edit className=\"w-3 h-3\" />}\r\n\r\n                        Cập nhật\r\n                    </button>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Scrollable Preview Content */}\r\n            <div className=\"flex-1 overflow-y-auto p-3\">\r\n                <div className=\"bg-white rounded border border-gray-200 \">\r\n                    <NavigateBar\r\n                        list={[\r\n                            {\r\n                                id: 1,\r\n                                name: 'Câu hỏi',\r\n                                value: 'question'\r\n                            },\r\n                            {\r\n                                id: 2,\r\n                                name: 'Ảnh',\r\n                                value: 'image'\r\n                            }\r\n                        ]}\r\n                        active={view}\r\n                        setActive={(value) => dispatch(setViewRightContent(value))}\r\n                    />\r\n                    {view === 'question' && <QuestionView />}\r\n                    {view === 'image' && <ImageView />}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default RightContent;"], "mappings": ";;AAAA,OAAOA,WAAW,MAAM,4BAA4B;AACpD,SAASC,GAAG,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,cAAc;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,mBAAmB,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,eAAe,QAAQ,+CAA+C;AACpI,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,MAAMC,YAAY,GAAGC,IAAA,IAAgB;EAAAC,EAAA;EAAA,IAAf;IAAEC;EAAO,CAAC,GAAAF,IAAA;EAC5B,MAAM;IAAEG,IAAI;IAAEC,UAAU;IAAEC;EAAc,CAAC,GAAGrB,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACD,aAAa,CAAC;EACvF,MAAM;IAAEE,cAAc;IAAEC,OAAO,EAAEC;EAAU,CAAC,GAAGzB,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACI,EAAE,CAAC;EAC/E,MAAMC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAE9B,MAAM2B,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACV,MAAM,EAAE;IAEb,MAAMW,aAAa,GAAGR,aAAa,CAACS,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;MAAA,IAAAC,qBAAA;MACzD,OAAO;QACHC,EAAE,EAAEH,QAAQ,CAACG,EAAE;QACfC,OAAO,EAAEJ,QAAQ,CAACI,OAAO;QACzBC,aAAa,EAAEL,QAAQ,CAACK,aAAa,IAAI,IAAI;QAC7CC,UAAU,EAAEN,QAAQ,CAACM,UAAU,IAAI,IAAI;QACvCC,OAAO,EAAEP,QAAQ,CAACO,OAAO,IAAI,IAAI;QACjCC,KAAK,EAAER,QAAQ,CAACQ,KAAK;QACrBC,QAAQ,EAAET,QAAQ,CAACS,QAAQ;QAC3BC,WAAW,EAAEV,QAAQ,CAACU,WAAW,IAAI,IAAI;QACzCC,QAAQ,EAAEX,QAAQ,CAACW,QAAQ,IAAI,IAAI;QACnCC,gBAAgB,EAAEZ,QAAQ,CAACY,gBAAgB,IAAI,IAAI;QACnDC,UAAU,EAAEb,QAAQ,CAACa,UAAU,CAACd,GAAG,CAAEe,SAAS,KAAM;UAChDX,EAAE,EAAEW,SAAS,CAACX,EAAE;UAChBC,OAAO,EAAEU,SAAS,CAACV,OAAO;UAC1BW,SAAS,EAAED,SAAS,CAACC,SAAS;UAC9BT,UAAU,EAAEQ,SAAS,CAACR,UAAU;UAChCK,QAAQ,EAAEG,SAAS,CAACH,QAAQ;UAC5BK,cAAc,EAAEF,SAAS,CAACE,cAAc,IAAI;QAChD,CAAC,CAAC,CAAC;QACHC,aAAa,EAAE;UACXC,KAAK,EAAE,EAAAhB,qBAAA,GAAAF,QAAQ,CAACiB,aAAa,cAAAf,qBAAA,uBAAtBA,qBAAA,CAAwBgB,KAAK,KAAIjB;QAC5C,CAAC;QACDkB,aAAa,EAAEnB,QAAQ,CAACmB,aAAa,IAAI,KAAK;QAC9CC,cAAc,EAAEpB,QAAQ,CAACoB,cAAc;QACvCC,WAAW,EAAErB,QAAQ,CAACqB;MAC1B,CAAC;IACL,CAAC,CAAC;IACF;;IAEAzB,QAAQ,CAACxB,gBAAgB,CAAC;MAAEe,MAAM;MAAEmC,SAAS,EAAExB;IAAc,CAAC,CAAC,CAAC;EACpE,CAAC;EAED,MAAMyB,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IACxC;IACA,MAAMzB,aAAa,GAAGR,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAES,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;MAC1D,OAAO;QACHuB,aAAa,EAAEvB,KAAK;QACpBO,KAAK,EAAER,QAAQ,CAACQ,KAAK,IAAI,EAAE;QAC3BD,OAAO,EAAEP,QAAQ,CAACO,OAAO,IAAI,EAAE;QAC/BD,UAAU,EAAEN,QAAQ,CAACM,UAAU,IAAI,EAAE;QACrCc,cAAc,EAAEpB,QAAQ,CAACoB,cAAc,IAAI,IAAI;QAC/ChB,OAAO,EAAEJ,QAAQ,CAACI,OAAO,IAAI,EAAE;QAC/BS,UAAU,EAAEb,QAAQ,CAACa,UAAU,IAAIb,QAAQ,CAACa,UAAU,CAACY,MAAM,GAAG,CAAC,GAAGzB,QAAQ,CAACa,UAAU,CAACd,GAAG,CAAEe,SAAS,IAAKA,SAAS,CAACV,OAAO,CAAC,GAAG;MACpI,CAAC;IACL,CAAC,CAAC;IACF;IACA;IACA,MAAMsB,MAAM,GAAG,CACX;MACI,eAAe,EAAE,CAAC;MAClB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,CAAC;MAClB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,CAAC;MAClB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,CAAC;MAClB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,CAAC;MAClB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,CAAC;MAClB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,CAAC;MAClB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,CAAC;MAClB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,CAAC;MAClB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,CAAC;MAClB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,EAAE;MACnB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,EAAE;MACnB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,EAAE;MACnB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,EAAE;MACnB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,EAAE;MACnB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,EAAE;MACnB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,EAAE;MACnB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,EAAE;MACnB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,EAAE;MACnB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,EAAE;MACnB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,EAAE;MACnB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,EACD;MACI,eAAe,EAAE,EAAE;MACnB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,OAAO;MAClB,YAAY,EAAE;IAClB,CAAC,CACJ;IACD,MAAMC,gBAAgB,GAAGrC,aAAa,CAACS,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;MAC5D,MAAM2B,kBAAkB,GAAGF,MAAM,CAACG,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACN,aAAa,KAAKvB,KAAK,CAAC;MACxE,IAAI2B,kBAAkB,EAAE;QACpB,OAAO;UACH,GAAG5B,QAAQ;UACXQ,KAAK,EAAEoB,kBAAkB,CAACpB,KAAK;UAC/BD,OAAO,EAAEqB,kBAAkB,CAACrB,OAAO;UACnCD,UAAU,EAAEsB,kBAAkB,CAACtB;QACnC,CAAC;MACL;MACA,OAAON,QAAQ;IACnB,CAAC,CAAC;IACFJ,QAAQ,CAACtB,eAAe,CAACqD,gBAAgB,CAAC,CAAC;EAC/C,CAAC;EAED,oBACI9C,OAAA;IAAKkD,SAAS,EAAC,kDAAkD;IAAAC,QAAA,gBAE7DnD,OAAA;MAAKkD,SAAS,EAAC,oFAAoF;MAAAC,QAAA,gBAC/FnD,OAAA;QAAKkD,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACpCnD,OAAA,CAACf,GAAG;UAACiE,SAAS,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCvD,OAAA;UAAIkD,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,eACNvD,OAAA;QAAKkD,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACpCnD,OAAA;UACIwD,OAAO,EAAEd,uBAAwB;UACjCe,QAAQ,EAAE5C,SAAS,IAAIJ,aAAa,CAACmC,MAAM,KAAK,CAAE;UAClDM,SAAS,EAAC,kKAAkK;UAC5KQ,KAAK,EAAC,uEAAmC;UAAAP,QAAA,EAExCtC,SAAS,gBACNb,OAAA,CAAAE,SAAA;YAAAiD,QAAA,gBACInD,OAAA;cAAKkD,SAAS,EAAC;YAA2D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,8BAErF;UAAA,eAAE,CAAC,gBAEHvD,OAAA,CAAAE,SAAA;YAAAiD,QAAA,gBACInD,OAAA,CAACb,QAAQ;cAAC+D,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBAEpC;UAAA,eAAE;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACTvD,OAAA;UACIyD,QAAQ,EAAEjD,UAAW;UACrBgD,OAAO,EAAExC,kBAAmB;UAC5BkC,SAAS,qGAAsG;UAAAC,QAAA,GAE9G3C,UAAU,gBAAGR,OAAA,CAACJ,cAAc;YAACsD,SAAS,EAAC,SAAS;YAACS,SAAS,EAAC;UAAS;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGvD,OAAA,CAACd,IAAI;YAACgE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAAC,oBAG5G;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNvD,OAAA;MAAKkD,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACvCnD,OAAA;QAAKkD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACrDnD,OAAA,CAAChB,WAAW;UACR4E,IAAI,EAAE,CACF;YACItC,EAAE,EAAE,CAAC;YACLuC,IAAI,EAAE,SAAS;YACfC,KAAK,EAAE;UACX,CAAC,EACD;YACIxC,EAAE,EAAE,CAAC;YACLuC,IAAI,EAAE,KAAK;YACXC,KAAK,EAAE;UACX,CAAC,CACH;UACFC,MAAM,EAAExD,IAAK;UACbyD,SAAS,EAAGF,KAAK,IAAK/C,QAAQ,CAACzB,mBAAmB,CAACwE,KAAK,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,EACDhD,IAAI,KAAK,UAAU,iBAAIP,OAAA,CAACN,YAAY;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACvChD,IAAI,KAAK,OAAO,iBAAIP,OAAA,CAACL,SAAS;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAlD,EAAA,CA7QKF,YAAY;EAAA,QAC8Bf,WAAW,EACRA,WAAW,EACzCC,WAAW;AAAA;AAAA4E,EAAA,GAH1B9D,YAAY;AA+QlB,eAAeA,YAAY;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}