/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { MistralCore } from "../core.js";
import { appendForm } from "../lib/encodings.js";
import { readableStreamToArrayBuffer } from "../lib/files.js";
import * as M from "../lib/matchers.js";
import { compactMap } from "../lib/primitives.js";
import { safeParse } from "../lib/schemas.js";
import { RequestOptions } from "../lib/sdks.js";
import { extractSecurity, resolveGlobalSecurity } from "../lib/security.js";
import { pathToFunc } from "../lib/url.js";
import * as components from "../models/components/index.js";
import {
  ConnectionError,
  InvalidRequestError,
  RequestAbortedError,
  RequestTimeoutError,
  UnexpectedClientError,
} from "../models/errors/httpclienterrors.js";
import { SDKError } from "../models/errors/sdkerror.js";
import { SDKValidationError } from "../models/errors/sdkvalidationerror.js";
import { APICall, APIPromise } from "../types/async.js";
import { isBlobLike } from "../types/blobs.js";
import { Result } from "../types/fp.js";
import { isReadableStream } from "../types/streams.js";

/**
 * Create Transcription
 */
export function audioTranscriptionsComplete(
  client: MistralCore,
  request: components.AudioTranscriptionRequest,
  options?: RequestOptions,
): APIPromise<
  Result<
    components.TranscriptionResponse,
    | SDKError
    | SDKValidationError
    | UnexpectedClientError
    | InvalidRequestError
    | RequestAbortedError
    | RequestTimeoutError
    | ConnectionError
  >
> {
  return new APIPromise($do(
    client,
    request,
    options,
  ));
}

async function $do(
  client: MistralCore,
  request: components.AudioTranscriptionRequest,
  options?: RequestOptions,
): Promise<
  [
    Result<
      components.TranscriptionResponse,
      | SDKError
      | SDKValidationError
      | UnexpectedClientError
      | InvalidRequestError
      | RequestAbortedError
      | RequestTimeoutError
      | ConnectionError
    >,
    APICall,
  ]
> {
  const parsed = safeParse(
    request,
    (value) => components.AudioTranscriptionRequest$outboundSchema.parse(value),
    "Input validation failed",
  );
  if (!parsed.ok) {
    return [parsed, { status: "invalid" }];
  }
  const payload = parsed.value;
  const body = new FormData();

  appendForm(body, "model", payload.model);
  if (payload.file !== undefined) {
    if (isBlobLike(payload.file)) {
      appendForm(body, "file", payload.file);
    } else if (isReadableStream(payload.file.content)) {
      const buffer = await readableStreamToArrayBuffer(payload.file.content);
      const blob = new Blob([buffer], { type: "application/octet-stream" });
      appendForm(body, "file", blob);
    } else {
      appendForm(
        body,
        "file",
        new Blob([payload.file.content], { type: "application/octet-stream" }),
        payload.file.fileName,
      );
    }
  }
  if (payload.file_id !== undefined) {
    appendForm(body, "file_id", payload.file_id);
  }
  if (payload.file_url !== undefined) {
    appendForm(body, "file_url", payload.file_url);
  }
  if (payload.language !== undefined) {
    appendForm(body, "language", payload.language);
  }
  if (payload.stream !== undefined) {
    appendForm(body, "stream", payload.stream);
  }
  if (payload.temperature !== undefined) {
    appendForm(body, "temperature", payload.temperature);
  }
  if (payload.timestamp_granularities !== undefined) {
    appendForm(
      body,
      "timestamp_granularities",
      payload.timestamp_granularities,
    );
  }

  const path = pathToFunc("/v1/audio/transcriptions")();

  const headers = new Headers(compactMap({
    Accept: "application/json",
  }));

  const secConfig = await extractSecurity(client._options.apiKey);
  const securityInput = secConfig == null ? {} : { apiKey: secConfig };
  const requestSecurity = resolveGlobalSecurity(securityInput);

  const context = {
    baseURL: options?.serverURL ?? client._baseURL ?? "",
    operationID: "audio_api_v1_transcriptions_post",
    oAuth2Scopes: [],

    resolvedSecurity: requestSecurity,

    securitySource: client._options.apiKey,
    retryConfig: options?.retries
      || client._options.retryConfig
      || { strategy: "none" },
    retryCodes: options?.retryCodes || ["429", "500", "502", "503", "504"],
  };

  const requestRes = client._createRequest(context, {
    security: requestSecurity,
    method: "POST",
    baseURL: options?.serverURL,
    path: path,
    headers: headers,
    body: body,
    timeoutMs: options?.timeoutMs || client._options.timeoutMs || -1,
  }, options);
  if (!requestRes.ok) {
    return [requestRes, { status: "invalid" }];
  }
  const req = requestRes.value;

  const doResult = await client._do(req, {
    context,
    errorCodes: ["4XX", "5XX"],
    retryConfig: context.retryConfig,
    retryCodes: context.retryCodes,
  });
  if (!doResult.ok) {
    return [doResult, { status: "request-error", request: req }];
  }
  const response = doResult.value;

  const [result] = await M.match<
    components.TranscriptionResponse,
    | SDKError
    | SDKValidationError
    | UnexpectedClientError
    | InvalidRequestError
    | RequestAbortedError
    | RequestTimeoutError
    | ConnectionError
  >(
    M.json(200, components.TranscriptionResponse$inboundSchema),
    M.fail("4XX"),
    M.fail("5XX"),
  )(response);
  if (!result.ok) {
    return [result, { status: "complete", request: req, response }];
  }

  return [result, { status: "complete", request: req, response }];
}
