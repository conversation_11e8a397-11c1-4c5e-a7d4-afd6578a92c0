export declare class RFCDate {
    private serialized;
    /**
     * Creates a new RFCDate instance using today's date.
     */
    static today(): RFCDate;
    /**
     * Creates a new RFCDate instance using the provided input.
     * If a string is used then in must be in the format YYYY-MM-DD.
     *
     * @param date A Date object or a date string in YYYY-MM-DD format
     * @example
     * new RFCDate("2022-01-01")
     * @example
     * new RFCDate(new Date())
     */
    constructor(date: Date | string);
    toJSON(): string;
    toString(): string;
}
//# sourceMappingURL=rfcdate.d.ts.map