import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";
export declare const TranscriptionStreamEventTypes: {
    readonly TranscriptionLanguage: "transcription.language";
    readonly TranscriptionSegment: "transcription.segment";
    readonly TranscriptionTextDelta: "transcription.text.delta";
    readonly TranscriptionDone: "transcription.done";
};
export type TranscriptionStreamEventTypes = ClosedEnum<typeof TranscriptionStreamEventTypes>;
/** @internal */
export declare const TranscriptionStreamEventTypes$inboundSchema: z.ZodNativeEnum<typeof TranscriptionStreamEventTypes>;
/** @internal */
export declare const TranscriptionStreamEventTypes$outboundSchema: z.<PERSON>od<PERSON><typeof TranscriptionStreamEventTypes>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace TranscriptionStreamEventTypes$ {
    /** @deprecated use `TranscriptionStreamEventTypes$inboundSchema` instead. */
    const inboundSchema: z.<PERSON>od<PERSON><{
        readonly TranscriptionLanguage: "transcription.language";
        readonly TranscriptionSegment: "transcription.segment";
        readonly TranscriptionTextDelta: "transcription.text.delta";
        readonly TranscriptionDone: "transcription.done";
    }>;
    /** @deprecated use `TranscriptionStreamEventTypes$outboundSchema` instead. */
    const outboundSchema: z.ZodNativeEnum<{
        readonly TranscriptionLanguage: "transcription.language";
        readonly TranscriptionSegment: "transcription.segment";
        readonly TranscriptionTextDelta: "transcription.text.delta";
        readonly TranscriptionDone: "transcription.done";
    }>;
}
//# sourceMappingURL=transcriptionstreameventtypes.d.ts.map