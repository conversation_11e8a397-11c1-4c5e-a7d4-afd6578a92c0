/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  ContentChunk,
  ContentChunk$inboundSchema,
  ContentChunk$Outbound,
  ContentChunk$outboundSchema,
} from "./contentchunk.js";

export type ToolMessageContent = string | Array<ContentChunk>;

export const ToolMessageRole = {
  Tool: "tool",
} as const;
export type ToolMessageRole = ClosedEnum<typeof ToolMessageRole>;

export type ToolMessage = {
  content: string | Array<ContentChunk> | null;
  toolCallId?: string | null | undefined;
  name?: string | null | undefined;
  role?: ToolMessageRole | undefined;
};

/** @internal */
export const ToolMessageContent$inboundSchema: z.ZodType<
  ToolMessageContent,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.array(ContentChunk$inboundSchema)]);

/** @internal */
export type ToolMessageContent$Outbound = string | Array<ContentChunk$Outbound>;

/** @internal */
export const ToolMessageContent$outboundSchema: z.ZodType<
  ToolMessageContent$Outbound,
  z.ZodTypeDef,
  ToolMessageContent
> = z.union([z.string(), z.array(ContentChunk$outboundSchema)]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolMessageContent$ {
  /** @deprecated use `ToolMessageContent$inboundSchema` instead. */
  export const inboundSchema = ToolMessageContent$inboundSchema;
  /** @deprecated use `ToolMessageContent$outboundSchema` instead. */
  export const outboundSchema = ToolMessageContent$outboundSchema;
  /** @deprecated use `ToolMessageContent$Outbound` instead. */
  export type Outbound = ToolMessageContent$Outbound;
}

export function toolMessageContentToJSON(
  toolMessageContent: ToolMessageContent,
): string {
  return JSON.stringify(
    ToolMessageContent$outboundSchema.parse(toolMessageContent),
  );
}

export function toolMessageContentFromJSON(
  jsonString: string,
): SafeParseResult<ToolMessageContent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ToolMessageContent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ToolMessageContent' from JSON`,
  );
}

/** @internal */
export const ToolMessageRole$inboundSchema: z.ZodNativeEnum<
  typeof ToolMessageRole
> = z.nativeEnum(ToolMessageRole);

/** @internal */
export const ToolMessageRole$outboundSchema: z.ZodNativeEnum<
  typeof ToolMessageRole
> = ToolMessageRole$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolMessageRole$ {
  /** @deprecated use `ToolMessageRole$inboundSchema` instead. */
  export const inboundSchema = ToolMessageRole$inboundSchema;
  /** @deprecated use `ToolMessageRole$outboundSchema` instead. */
  export const outboundSchema = ToolMessageRole$outboundSchema;
}

/** @internal */
export const ToolMessage$inboundSchema: z.ZodType<
  ToolMessage,
  z.ZodTypeDef,
  unknown
> = z.object({
  content: z.nullable(
    z.union([z.string(), z.array(ContentChunk$inboundSchema)]),
  ),
  tool_call_id: z.nullable(z.string()).optional(),
  name: z.nullable(z.string()).optional(),
  role: ToolMessageRole$inboundSchema.default("tool"),
}).transform((v) => {
  return remap$(v, {
    "tool_call_id": "toolCallId",
  });
});

/** @internal */
export type ToolMessage$Outbound = {
  content: string | Array<ContentChunk$Outbound> | null;
  tool_call_id?: string | null | undefined;
  name?: string | null | undefined;
  role: string;
};

/** @internal */
export const ToolMessage$outboundSchema: z.ZodType<
  ToolMessage$Outbound,
  z.ZodTypeDef,
  ToolMessage
> = z.object({
  content: z.nullable(
    z.union([z.string(), z.array(ContentChunk$outboundSchema)]),
  ),
  toolCallId: z.nullable(z.string()).optional(),
  name: z.nullable(z.string()).optional(),
  role: ToolMessageRole$outboundSchema.default("tool"),
}).transform((v) => {
  return remap$(v, {
    toolCallId: "tool_call_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolMessage$ {
  /** @deprecated use `ToolMessage$inboundSchema` instead. */
  export const inboundSchema = ToolMessage$inboundSchema;
  /** @deprecated use `ToolMessage$outboundSchema` instead. */
  export const outboundSchema = ToolMessage$outboundSchema;
  /** @deprecated use `ToolMessage$Outbound` instead. */
  export type Outbound = ToolMessage$Outbound;
}

export function toolMessageToJSON(toolMessage: ToolMessage): string {
  return JSON.stringify(ToolMessage$outboundSchema.parse(toolMessage));
}

export function toolMessageFromJSON(
  jsonString: string,
): SafeParseResult<ToolMessage, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ToolMessage$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ToolMessage' from JSON`,
  );
}
