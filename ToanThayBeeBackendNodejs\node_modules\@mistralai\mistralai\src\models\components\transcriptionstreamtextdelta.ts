/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import {
  collectExtraKeys as collectExtraKeys$,
  safeParse,
} from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const TranscriptionStreamTextDeltaType = {
  TranscriptionTextDelta: "transcription.text.delta",
} as const;
export type TranscriptionStreamTextDeltaType = ClosedEnum<
  typeof TranscriptionStreamTextDeltaType
>;

export type TranscriptionStreamTextDelta = {
  text: string;
  type?: TranscriptionStreamTextDeltaType | undefined;
  additionalProperties?: { [k: string]: any };
};

/** @internal */
export const TranscriptionStreamTextDeltaType$inboundSchema: z.Zod<PERSON>num<
  typeof TranscriptionStreamTextDeltaType
> = z.nativeEnum(TranscriptionStreamTextDeltaType);

/** @internal */
export const TranscriptionStreamTextDeltaType$outboundSchema: z.ZodNativeEnum<
  typeof TranscriptionStreamTextDeltaType
> = TranscriptionStreamTextDeltaType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TranscriptionStreamTextDeltaType$ {
  /** @deprecated use `TranscriptionStreamTextDeltaType$inboundSchema` instead. */
  export const inboundSchema = TranscriptionStreamTextDeltaType$inboundSchema;
  /** @deprecated use `TranscriptionStreamTextDeltaType$outboundSchema` instead. */
  export const outboundSchema = TranscriptionStreamTextDeltaType$outboundSchema;
}

/** @internal */
export const TranscriptionStreamTextDelta$inboundSchema: z.ZodType<
  TranscriptionStreamTextDelta,
  z.ZodTypeDef,
  unknown
> = collectExtraKeys$(
  z.object({
    text: z.string(),
    type: TranscriptionStreamTextDeltaType$inboundSchema.default(
      "transcription.text.delta",
    ),
  }).catchall(z.any()),
  "additionalProperties",
  true,
);

/** @internal */
export type TranscriptionStreamTextDelta$Outbound = {
  text: string;
  type: string;
  [additionalProperties: string]: unknown;
};

/** @internal */
export const TranscriptionStreamTextDelta$outboundSchema: z.ZodType<
  TranscriptionStreamTextDelta$Outbound,
  z.ZodTypeDef,
  TranscriptionStreamTextDelta
> = z.object({
  text: z.string(),
  type: TranscriptionStreamTextDeltaType$outboundSchema.default(
    "transcription.text.delta",
  ),
  additionalProperties: z.record(z.any()),
}).transform((v) => {
  return {
    ...v.additionalProperties,
    ...remap$(v, {
      additionalProperties: null,
    }),
  };
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TranscriptionStreamTextDelta$ {
  /** @deprecated use `TranscriptionStreamTextDelta$inboundSchema` instead. */
  export const inboundSchema = TranscriptionStreamTextDelta$inboundSchema;
  /** @deprecated use `TranscriptionStreamTextDelta$outboundSchema` instead. */
  export const outboundSchema = TranscriptionStreamTextDelta$outboundSchema;
  /** @deprecated use `TranscriptionStreamTextDelta$Outbound` instead. */
  export type Outbound = TranscriptionStreamTextDelta$Outbound;
}

export function transcriptionStreamTextDeltaToJSON(
  transcriptionStreamTextDelta: TranscriptionStreamTextDelta,
): string {
  return JSON.stringify(
    TranscriptionStreamTextDelta$outboundSchema.parse(
      transcriptionStreamTextDelta,
    ),
  );
}

export function transcriptionStreamTextDeltaFromJSON(
  jsonString: string,
): SafeParseResult<TranscriptionStreamTextDelta, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => TranscriptionStreamTextDelta$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'TranscriptionStreamTextDelta' from JSON`,
  );
}
