import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import * as operations from "../models/operations/index.js";
export declare class Jobs extends ClientSDK {
    /**
     * Get Fine Tuning Jobs
     *
     * @remarks
     * Get a list of fine-tuning jobs for your organization and user.
     */
    list(request?: operations.JobsApiRoutesFineTuningGetFineTuningJobsRequest | undefined, options?: RequestOptions): Promise<components.JobsOut>;
    /**
     * Create Fine Tuning Job
     *
     * @remarks
     * Create a new fine-tuning job, it will be queued for processing.
     */
    create(request: components.JobIn, options?: RequestOptions): Promise<operations.JobsApiRoutesFineTuningCreateFineTuningJobResponse>;
    /**
     * Get Fine Tuning Job
     *
     * @remarks
     * Get a fine-tuned job details by its UUID.
     */
    get(request: operations.JobsApiRoutesFineTuningGetFineTuningJobRequest, options?: RequestOptions): Promise<operations.JobsApiRoutesFineTuningGetFineTuningJobResponse>;
    /**
     * Cancel Fine Tuning Job
     *
     * @remarks
     * Request the cancellation of a fine tuning job.
     */
    cancel(request: operations.JobsApiRoutesFineTuningCancelFineTuningJobRequest, options?: RequestOptions): Promise<operations.JobsApiRoutesFineTuningCancelFineTuningJobResponse>;
    /**
     * Start Fine Tuning Job
     *
     * @remarks
     * Request the start of a validated fine tuning job.
     */
    start(request: operations.JobsApiRoutesFineTuningStartFineTuningJobRequest, options?: RequestOptions): Promise<operations.JobsApiRoutesFineTuningStartFineTuningJobResponse>;
}
//# sourceMappingURL=jobs.d.ts.map