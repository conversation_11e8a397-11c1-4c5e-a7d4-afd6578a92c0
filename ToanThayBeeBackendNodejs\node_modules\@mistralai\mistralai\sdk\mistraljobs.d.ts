import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import * as operations from "../models/operations/index.js";
export declare class MistralJobs extends ClientSDK {
    /**
     * Get Batch Jobs
     *
     * @remarks
     * Get a list of batch jobs for your organization and user.
     */
    list(request?: operations.JobsApiRoutesBatchGetBatchJobsRequest | undefined, options?: RequestOptions): Promise<components.BatchJobsOut>;
    /**
     * Create Batch Job
     *
     * @remarks
     * Create a new batch job, it will be queued for processing.
     */
    create(request: components.BatchJobIn, options?: RequestOptions): Promise<components.BatchJobOut>;
    /**
     * Get Batch Job
     *
     * @remarks
     * Get a batch job details by its UUID.
     */
    get(request: operations.JobsApiRoutesBatchGetBatchJobRequest, options?: RequestOptions): Promise<components.BatchJobOut>;
    /**
     * Cancel Batch Job
     *
     * @remarks
     * Request the cancellation of a batch job.
     */
    cancel(request: operations.JobsApiRoutesBatchCancelBatchJobRequest, options?: RequestOptions): Promise<components.BatchJobOut>;
}
//# sourceMappingURL=mistraljobs.d.ts.map