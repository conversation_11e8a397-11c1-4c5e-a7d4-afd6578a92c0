# API Sửa Chính Tả và LaTeX Bằng GPT

## <PERSON><PERSON> tả
API này sử dụng GPT để sửa chính tả tiếng Việt và ký hiệu LaTeX trong văn bản.

## Endpoint
```
POST /v1/ai/fix-text-latex
```

## Authentication
Y<PERSON>u cầu JWT token với role `JustStudent` hoặc cao hơn.

## Request Body
```json
{
  "text": "Giải phương trình x^2 + 2x - 3 = 0. Ta có $\\Delta = b^2 - 4ac = 4 + 12 = 16 > 0$ nên phương trình có hai nghiệm phân biệt."
}
```

### Thuộc tính:
- `text` (string, bắt buộc): Văn bản cần sửa chính tả và LaTeX

## Response

### Thành công (200)
```json
{
  "message": "Sửa văn bản thành công",
  "data": {
    "originalText": "Gi<PERSON>i phương trình x^2 + 2x - 3 = 0. Ta có $\\Delta = b^2 - 4ac = 4 + 12 = 16 > 0$ nên phương trình có hai nghiệm phân biệt.",
    "fixedText": "Giải phương trình $x^2 + 2x - 3 = 0$. Ta có $\\Delta = b^2 - 4ac = 4 + 12 = 16 > 0$ nên phương trình có hai nghiệm phân biệt.",
    "hasChanges": true,
    "model": "gpt-4"
  }
}
```

### Lỗi (400)
```json
{
  "message": "Dữ liệu không hợp lệ",
  "error": "text phải là một chuỗi không rỗng"
}
```

### Lỗi (500)
```json
{
  "message": "Lỗi server khi sửa văn bản",
  "error": "Chi tiết lỗi"
}
```

## Giới hạn
- Tối đa 10,000 ký tự mỗi lần gọi API
- Văn bản không được rỗng

## Tính năng sửa lỗi
1. **Chính tả tiếng Việt**: Sửa lỗi đánh máy, dấu thanh, ngữ pháp
2. **Ký hiệu LaTeX**: Sửa cú pháp LaTeX, dấu ngoặc, lệnh không đúng
3. **Cải thiện trình bày**: Tối ưu cách hiển thị công thức toán học
4. **Giữ nguyên ý nghĩa**: Không thay đổi nội dung gốc

## Ví dụ sử dụng

### JavaScript/Fetch
```javascript
const response = await fetch('/v1/ai/fix-text-latex', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_JWT_TOKEN'
  },
  body: JSON.stringify({
    text: "Tính đạo hàm của hàm số y = x^3 + 2x^2 - 5x + 1"
  })
});

const data = await response.json();
console.log(data);
```

### cURL
```bash
curl -X POST http://localhost:3000/v1/ai/fix-text-latex \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "text": "Giải bất phương trình x^2 - 5x + 6 > 0"
  }'
```

## Tích hợp Frontend

### Redux Slice
```javascript
import { fixTextAndLatex } from 'src/features/ai/aiSlice';

// Sử dụng trong component
const handleAIFix = async () => {
  try {
    const result = await dispatch(fixTextAndLatex(text)).unwrap();
    if (result.hasChanges) {
      setText(result.fixedText);
    } else {
      alert('Không tìm thấy lỗi nào cần sửa');
    }
  } catch (error) {
    console.error('Error fixing text:', error);
  }
};
```

### Component với nút AI
```jsx
import { Sparkles } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { fixTextAndLatex } from "src/features/ai/aiSlice";

const TextAreaWithAI = ({ value, onChange, placeholder }) => {
  const dispatch = useDispatch();
  const { loading } = useSelector((state) => state.ai);

  const handleAIFix = async () => {
    if (!value?.trim()) return;
    
    try {
      const result = await dispatch(fixTextAndLatex(value)).unwrap();
      if (result.hasChanges) {
        onChange({ target: { value: result.fixedText } });
      }
    } catch (error) {
      console.error('Error fixing text:', error);
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <label>Nội dung</label>
        <button
          onClick={handleAIFix}
          disabled={loading || !value?.trim()}
          className="flex items-center gap-1 px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
        >
          <Sparkles className="w-3 h-3" />
          {loading ? 'Đang sửa...' : 'AI Fix'}
        </button>
      </div>
      <textarea
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className="w-full p-2 border rounded"
      />
    </div>
  );
};
```

## Lưu ý
- API sử dụng model GPT-4 để đảm bảo độ chính xác cao
- Temperature được đặt thấp (0.1) để có kết quả nhất quán
- Nếu không có lỗi, API sẽ trả về văn bản gốc
- Thuộc tính `hasChanges` cho biết có thay đổi nào được thực hiện hay không
