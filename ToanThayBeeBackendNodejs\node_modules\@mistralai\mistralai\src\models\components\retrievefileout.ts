/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  FilePurpose,
  FilePurpose$inboundSchema,
  FilePurpose$outboundSchema,
} from "./filepurpose.js";
import {
  SampleType,
  SampleType$inboundSchema,
  SampleType$outboundSchema,
} from "./sampletype.js";
import {
  Source,
  Source$inboundSchema,
  Source$outboundSchema,
} from "./source.js";

export type RetrieveFileOut = {
  /**
   * The unique identifier of the file.
   */
  id: string;
  /**
   * The object type, which is always "file".
   */
  object: string;
  /**
   * The size of the file, in bytes.
   */
  sizeBytes: number;
  /**
   * The UNIX timestamp (in seconds) of the event.
   */
  createdAt: number;
  /**
   * The name of the uploaded file.
   */
  filename: string;
  purpose: FilePurpose;
  sampleType: SampleType;
  numLines?: number | null | undefined;
  mimetype?: string | null | undefined;
  source: Source;
  signature?: string | null | undefined;
  deleted: boolean;
};

/** @internal */
export const RetrieveFileOut$inboundSchema: z.ZodType<
  RetrieveFileOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  object: z.string(),
  bytes: z.number().int(),
  created_at: z.number().int(),
  filename: z.string(),
  purpose: FilePurpose$inboundSchema,
  sample_type: SampleType$inboundSchema,
  num_lines: z.nullable(z.number().int()).optional(),
  mimetype: z.nullable(z.string()).optional(),
  source: Source$inboundSchema,
  signature: z.nullable(z.string()).optional(),
  deleted: z.boolean(),
}).transform((v) => {
  return remap$(v, {
    "bytes": "sizeBytes",
    "created_at": "createdAt",
    "sample_type": "sampleType",
    "num_lines": "numLines",
  });
});

/** @internal */
export type RetrieveFileOut$Outbound = {
  id: string;
  object: string;
  bytes: number;
  created_at: number;
  filename: string;
  purpose: string;
  sample_type: string;
  num_lines?: number | null | undefined;
  mimetype?: string | null | undefined;
  source: string;
  signature?: string | null | undefined;
  deleted: boolean;
};

/** @internal */
export const RetrieveFileOut$outboundSchema: z.ZodType<
  RetrieveFileOut$Outbound,
  z.ZodTypeDef,
  RetrieveFileOut
> = z.object({
  id: z.string(),
  object: z.string(),
  sizeBytes: z.number().int(),
  createdAt: z.number().int(),
  filename: z.string(),
  purpose: FilePurpose$outboundSchema,
  sampleType: SampleType$outboundSchema,
  numLines: z.nullable(z.number().int()).optional(),
  mimetype: z.nullable(z.string()).optional(),
  source: Source$outboundSchema,
  signature: z.nullable(z.string()).optional(),
  deleted: z.boolean(),
}).transform((v) => {
  return remap$(v, {
    sizeBytes: "bytes",
    createdAt: "created_at",
    sampleType: "sample_type",
    numLines: "num_lines",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace RetrieveFileOut$ {
  /** @deprecated use `RetrieveFileOut$inboundSchema` instead. */
  export const inboundSchema = RetrieveFileOut$inboundSchema;
  /** @deprecated use `RetrieveFileOut$outboundSchema` instead. */
  export const outboundSchema = RetrieveFileOut$outboundSchema;
  /** @deprecated use `RetrieveFileOut$Outbound` instead. */
  export type Outbound = RetrieveFileOut$Outbound;
}

export function retrieveFileOutToJSON(
  retrieveFileOut: RetrieveFileOut,
): string {
  return JSON.stringify(RetrieveFileOut$outboundSchema.parse(retrieveFileOut));
}

export function retrieveFileOutFromJSON(
  jsonString: string,
): SafeParseResult<RetrieveFileOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => RetrieveFileOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'RetrieveFileOut' from JSON`,
  );
}
