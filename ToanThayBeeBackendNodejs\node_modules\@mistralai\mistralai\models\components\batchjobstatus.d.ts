import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";
export declare const BatchJobStatus: {
    readonly Queued: "QUEUED";
    readonly Running: "RUNNING";
    readonly Success: "SUCCESS";
    readonly Failed: "FAILED";
    readonly TimeoutExceeded: "TIMEOUT_EXCEEDED";
    readonly CancellationRequested: "CANCELLATION_REQUESTED";
    readonly Cancelled: "CANCELLED";
};
export type BatchJobStatus = ClosedEnum<typeof BatchJobStatus>;
/** @internal */
export declare const BatchJobStatus$inboundSchema: z.ZodNativeEnum<typeof BatchJobStatus>;
/** @internal */
export declare const BatchJobStatus$outboundSchema: z.ZodNativeEnum<typeof BatchJobStatus>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace BatchJobStatus$ {
    /** @deprecated use `BatchJobStatus$inboundSchema` instead. */
    const inboundSchema: z.<PERSON><{
        readonly Queued: "QUEUED";
        readonly Running: "RUNNING";
        readonly Success: "SUCCESS";
        readonly Failed: "FAILED";
        readonly TimeoutExceeded: "TIMEOUT_EXCEEDED";
        readonly CancellationRequested: "CANCELLATION_REQUESTED";
        readonly Cancelled: "CANCELLED";
    }>;
    /** @deprecated use `BatchJobStatus$outboundSchema` instead. */
    const outboundSchema: z.ZodNativeEnum<{
        readonly Queued: "QUEUED";
        readonly Running: "RUNNING";
        readonly Success: "SUCCESS";
        readonly Failed: "FAILED";
        readonly TimeoutExceeded: "TIMEOUT_EXCEEDED";
        readonly CancellationRequested: "CANCELLATION_REQUESTED";
        readonly Cancelled: "CANCELLED";
    }>;
}
//# sourceMappingURL=batchjobstatus.d.ts.map