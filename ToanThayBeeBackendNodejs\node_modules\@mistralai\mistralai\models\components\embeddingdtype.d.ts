import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";
export declare const EmbeddingDtype: {
    readonly Float: "float";
    readonly Int8: "int8";
    readonly Uint8: "uint8";
    readonly Binary: "binary";
    readonly Ubinary: "ubinary";
};
export type EmbeddingDtype = ClosedEnum<typeof EmbeddingDtype>;
/** @internal */
export declare const EmbeddingDtype$inboundSchema: z.ZodNativeEnum<typeof EmbeddingDtype>;
/** @internal */
export declare const EmbeddingDtype$outboundSchema: z.ZodNativeEnum<typeof EmbeddingDtype>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace EmbeddingDtype$ {
    /** @deprecated use `EmbeddingDtype$inboundSchema` instead. */
    const inboundSchema: z.ZodNativeEnum<{
        readonly Float: "float";
        readonly Int8: "int8";
        readonly Uint8: "uint8";
        readonly Binary: "binary";
        readonly Ubinary: "ubinary";
    }>;
    /** @deprecated use `EmbeddingDtype$outboundSchema` instead. */
    const outboundSchema: z.ZodNativeEnum<{
        readonly Float: "float";
        readonly Int8: "int8";
        readonly Uint8: "uint8";
        readonly Binary: "binary";
        readonly Ubinary: "ubinary";
    }>;
}
//# sourceMappingURL=embeddingdtype.d.ts.map