{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$(),\n  _s7 = $RefreshSig$(),\n  _s8 = $RefreshSig$(),\n  _s9 = $RefreshSig$();\n// Optimized Form Panel Component\nimport { useEffect, useState, useRef } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { setExamData, postExam, nextStep, prevStep, setExamImage, setExamFile, setQuestionTNContent, setQuestionDSContent, setQuestionTLNContent, setCorrectAnswerTN, setCorrectAnswerDS, setCorrectAnswerTLN, setQuestions, setSelectedIndex, setOcrFile, setMarkDownExam, setBase64Images } from \"src/features/addExam/addExamSlice\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport CompactStepHeader from \"./CompactStepHeader\";\nimport { Clock, Users, BookOpen, Image as ImageIcon, Upload, FileText, UploadCloud, CheckCircle, ChevronRight, ChevronLeft, Plus, Save, Trash2, Info, Sparkles } from \"lucide-react\";\nimport { fixTextAndLatex } from \"src/features/ai/aiSlice\";\nimport ImageUpload from \"src/components/image/UploadImage\";\nimport UploadPdf from \"src/components/UploadPdf\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport NavigateBar from \"./NavigateBar\";\nimport SolutionEditor from \"./SolutionEditor\";\nimport { normalizeText, validateExamData, splitMarkdownToParts } from \"src/utils/question/questionUtils\";\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\nimport TextArea from \"src/components/input/TextArea\";\nimport PdfViewer from \"../ViewPdf\";\nimport { ocrPdfWithMistral } from \"src/features/addExam/addExamSlice\";\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport { uploadBase64Images } from \"src/features/image/imageSlice\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Step1Form = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    examData,\n    examImage,\n    examFile\n  } = useSelector(state => state.addExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const updateExamData = (field, value) => {\n    dispatch(setExamData({\n      field,\n      value\n    }));\n  };\n  const [optionChapter, setOptionChapter] = useState([]);\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      if (examData.class && examData.class.trim() !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.startsWith(examData.class)));\n      } else {\n        setOptionChapter(codes[\"chapter\"]);\n      }\n    } else {\n      setOptionChapter();\n    }\n  }, [codes, examData.class]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [\"T\\xEAn \\u0111\\u1EC1 thi \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 36\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: examData.name || '',\n          onChange: e => updateExamData('name', e.target.value),\n          className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n          placeholder: \"Nh\\u1EADp t\\xEAn \\u0111\\u1EC1 thi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [\"Ki\\u1EC3u \\u0111\\u1EC1 \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: examData.typeOfExam,\n          onChange: option => updateExamData('typeOfExam', option),\n          options: Array.isArray(codes[\"exam type\"]) ? codes[\"exam type\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [\"L\\u1EDBp \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: examData.class,\n          onChange: option => updateExamData('class', option),\n          options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [\"N\\u0103m \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: examData.year,\n          onChange: option => updateExamData('year', option),\n          options: Array.isArray(codes[\"year\"]) ? codes[\"year\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(Clock, {\n            className: \"w-3 h-3 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 25\n          }, this), \"Th\\u1EDDi gian (ph\\xFAt)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          value: examData.testDuration || '',\n          onChange: e => updateExamData('testDuration', e.target.value),\n          className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n          placeholder: \"90\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(Users, {\n            className: \"w-3 h-3 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 25\n          }, this), \"\\u0110i\\u1EC3m \\u0111\\u1EA1t (%)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          value: examData.passRate || '',\n          onChange: e => updateExamData('passRate', e.target.value),\n          className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n          placeholder: \"50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 13\n    }, this), examData.typeOfExam === \"OT\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-xs font-medium text-gray-700 mb-1\",\n        children: [/*#__PURE__*/_jsxDEV(BookOpen, {\n          className: \"w-3 h-3 inline mr-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 25\n        }, this), \"Ch\\u01B0\\u01A1ng\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n        selectedOption: examData.chapter,\n        onChange: option => updateExamData('chapter', option),\n        options: optionChapter,\n        className: \"text-xs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-xs font-medium text-gray-700 mb-1\",\n        children: \"Link l\\u1EDDi gi\\u1EA3i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        value: examData.solutionUrl,\n        onChange: e => updateExamData('solutionUrl', e.target.value),\n        className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n        placeholder: \"Nh\\u1EADp link l\\u1EDDi gi\\u1EA3i vd: youtube, ...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-xs font-medium text-gray-700 mb-1\",\n        children: \"M\\xF4 t\\u1EA3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        value: examData.description || '',\n        onChange: e => updateExamData('description', e.target.value),\n        rows: 2,\n        className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n        placeholder: \"M\\xF4 t\\u1EA3 ng\\u1EAFn v\\u1EC1 \\u0111\\u1EC1 thi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"flex items-center text-xs\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: examData.public || false,\n          onChange: e => updateExamData('public', e.target.checked),\n          className: \"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-1 text-gray-700\",\n          children: \"C\\xF4ng khai\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"flex items-center text-xs\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: examData.isClassroomExam || false,\n          onChange: e => updateExamData('isClassroomExam', e.target.checked),\n          className: \"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-1 text-gray-700\",\n          children: \"\\u0110\\u1EC1 thi l\\u1EDBp\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(ImageIcon, {\n            className: \"w-3 h-3 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 25\n          }, this), \"\\u1EA2nh \\u0111\\u1EC1 thi\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ImageUpload, {\n          image: examImage,\n          setImage: img => dispatch(setExamImage(img)),\n          inputId: \"exam-image-compact\",\n          compact: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(Upload, {\n            className: \"w-3 h-3 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 25\n          }, this), \"File PDF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(UploadPdf, {\n          setPdf: pdf => dispatch(setExamFile(pdf)),\n          deleteButton: false,\n          compact: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 9\n  }, this);\n};\n_s(Step1Form, \"UPsNiDeUSSyfzIDxnLR7+kfyuhA=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = Step1Form;\nconst ButtonAddQuestion = _ref => {\n  let {\n    text,\n    onClick\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: onClick,\n    className: \"w-full px-3 py-2 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors text-xs\",\n    children: [/*#__PURE__*/_jsxDEV(Plus, {\n      className: \"w-3 h-3 inline mr-1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 13\n    }, this), text]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 240,\n    columnNumber: 9\n  }, this);\n};\n_c2 = ButtonAddQuestion;\nconst AddQuestionForm = _ref2 => {\n  let {\n    questionContent,\n    correctAnswerContent,\n    handleContentChange,\n    handleCorrectAnswerChange,\n    hintAnswer,\n    hintContent\n  } = _ref2;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-3 flex flex-col gap-4\",\n    children: [/*#__PURE__*/_jsxDEV(TextArea, {\n      value: correctAnswerContent,\n      onChange: handleCorrectAnswerChange,\n      placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n      label: \"\\u0110\\xE1p \\xE1n\",\n      Icon: CheckCircle,\n      hint: hintAnswer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n      value: questionContent,\n      onChange: handleContentChange,\n      placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n      label: \"C\\xE2u h\\u1ECFi\",\n      Icon: Plus,\n      hint: hintContent,\n      buttonFilterText: {\n        text: \"Lọc\",\n        onClick: () => {\n          handleContentChange({\n            target: {\n              value: normalizeText(questionContent)\n            }\n          });\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 253,\n    columnNumber: 9\n  }, this);\n};\n_c3 = AddQuestionForm;\nconst AddTNQuestion = () => {\n  _s2();\n  const dispatch = useDispatch();\n  const {\n    questionTNContent,\n    correctAnswerTN\n  } = useSelector(state => state.addExam);\n  const handleContentChange = e => {\n    dispatch(setQuestionTNContent(e.target.value));\n  };\n  const handleCorrectAnswerChange = e => {\n    dispatch(setCorrectAnswerTN(e.target.value));\n  };\n  return /*#__PURE__*/_jsxDEV(AddQuestionForm, {\n    questionContent: questionTNContent,\n    correctAnswerContent: correctAnswerTN,\n    handleContentChange: handleContentChange,\n    handleCorrectAnswerChange: handleCorrectAnswerChange,\n    hintAnswer: \"\\u0110\\xE1p \\xE1n tr\\u1EAFc nghi\\u1EC7m: A b c D ...\",\n    hintContent: \"N\\u1ED9i dung tr\\u1EAFc nghi\\u1EC7m: Paste c\\u1EA3 c\\xE2u h\\u1ECFi m\\u1EC7nh \\u0111\\u1EC1 v\\xE0 l\\u1EDDi gi\\u1EA3i\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 298,\n    columnNumber: 9\n  }, this);\n};\n_s2(AddTNQuestion, \"TUrm8UjG+jst7xNoz/PmxzyVdbI=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c4 = AddTNQuestion;\nconst AddDSQuestion = () => {\n  _s3();\n  const dispatch = useDispatch();\n  const {\n    questionDSContent,\n    correctAnswerDS\n  } = useSelector(state => state.addExam);\n  const handleContentChange = e => {\n    dispatch(setQuestionDSContent(e.target.value));\n  };\n  const handleCorrectAnswerChange = e => {\n    dispatch(setCorrectAnswerDS(e.target.value));\n  };\n  return /*#__PURE__*/_jsxDEV(AddQuestionForm, {\n    questionContent: questionDSContent,\n    correctAnswerContent: correctAnswerDS,\n    handleContentChange: handleContentChange,\n    handleCorrectAnswerChange: handleCorrectAnswerChange,\n    hintAnswer: \"\\u0110\\xE1p \\xE1n \\u0111\\xFAng sai: \\u0110\\u0110SS dsss DSDS ...\",\n    hintContent: \"N\\u1ED9i dung c\\xE2u h\\u1ECFi: Paste c\\u1EA3 c\\xE2u h\\u1ECFi m\\u1EC7nh \\u0111\\u1EC1 v\\xE0 l\\u1EDDi gi\\u1EA3i\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 322,\n    columnNumber: 9\n  }, this);\n};\n_s3(AddDSQuestion, \"OhFBIlLHYM7nXLOS6xnOobx4j7k=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c5 = AddDSQuestion;\nconst AddTLNQuestion = () => {\n  _s4();\n  const dispatch = useDispatch();\n  const {\n    questionTLNContent,\n    correctAnswerTLN\n  } = useSelector(state => state.addExam);\n  const handleContentChange = e => {\n    dispatch(setQuestionTLNContent(e.target.value));\n  };\n  const handleCorrectAnswerChange = e => {\n    dispatch(setCorrectAnswerTLN(e.target.value));\n  };\n  return /*#__PURE__*/_jsxDEV(AddQuestionForm, {\n    questionContent: questionTLNContent,\n    correctAnswerContent: correctAnswerTLN,\n    handleContentChange: handleContentChange,\n    handleCorrectAnswerChange: handleCorrectAnswerChange,\n    hintAnswer: \"\\u0110\\xE1p \\xE1n tr\\u1EA3 l\\u1EDDi ng\\u1EAFn: 3,14 1.5 3,2\",\n    hintContent: \"N\\u1ED9i dung c\\xE2u h\\u1ECFi: Paste c\\u1EA3 c\\xE2u h\\u1ECFi m\\u1EC7nh \\u0111\\u1EC1 v\\xE0 l\\u1EDDi gi\\u1EA3i\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 346,\n    columnNumber: 9\n  }, this);\n};\n_s4(AddTLNQuestion, \"2wyCIdvmC1fR2wIxMXSCGwa2CEM=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c6 = AddTLNQuestion;\nconst convertFileToBase64 = file => {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onloadend = () => resolve(reader.result);\n    reader.onerror = reject;\n    reader.readAsDataURL(file);\n  });\n};\nconst Step2Form = () => {\n  _s5();\n  const dispatch = useDispatch();\n  const fileInputRef = useRef(null);\n  const {\n    ocrFile,\n    markDownExam,\n    loadingOcr,\n    base64Images\n  } = useSelector(state => state.addExam);\n  const {\n    loadingUploadImages\n  } = useSelector(state => state.images);\n  const handleFileChange = file => dispatch(setOcrFile(file));\n  const handleAddImages = async event => {\n    const files = event.target.files;\n    if (!files || files.length === 0) return;\n    const base64List = await Promise.all(Array.from(files).map(file => convertFileToBase64(file)));\n    dispatch(setBase64Images([...base64Images, ...base64List]));\n  };\n  const handleOcr = () => dispatch(ocrPdfWithMistral(ocrFile));\n  const handleContentChange = e => dispatch(setMarkDownExam(e.target.value));\n  const handleRemoveImage = indexToRemove => {\n    const newImages = base64Images.filter((_, i) => i !== indexToRemove);\n    dispatch(setBase64Images(newImages));\n  };\n  const handleUploadImages = () => {\n    dispatch(uploadBase64Images({\n      images: base64Images,\n      folder: \"questionImage\"\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-5 bg-white space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-1 px-3 py-2 rounded bg-gray-50 border border-gray-200 text-sm text-gray-700 leading-relaxed\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"font-semibold text-gray-900\",\n          children: \"\\uD83D\\uDCDD H\\u01B0\\u1EDBng d\\u1EABn x\\u1EED l\\xFD \\u0111\\u1EC1 thi (m\\u1ECDi ng\\u01B0\\u1EDDi kh\\xF4ng c\\u1EA7n ph\\u1EA3i d\\xF9ng mathpix n\\u1EEFa)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"list-disc pl-5 space-y-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"T\\u1EA3i file \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-blue-600\",\n              children: \"PDF \\u0111\\u1EC1 thi c\\xF3 ch\\u1EE9a c\\u1EA3 \\u0111\\xE1p \\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 38\n            }, this), \".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"\\u1EA4n n\\xFAt \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-blue-600\",\n              children: \"\\u201CX\\u1EED l\\xFD \\u0111\\u1EC1 thi\\u201D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 36\n            }, this), \" \\u0111\\u1EC3 AI th\\u1EF1c hi\\u1EC7n nh\\u1EADn d\\u1EA1ng (OCR) n\\u1ED9i dung v\\xE0 t\\u1EF1 \\u0111\\u1ED9ng c\\u1EAFt \\u1EA3nh minh h\\u1ECDa.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"Sau khi x\\u1EED l\\xFD, \\u1EA3nh minh h\\u1ECDa s\\u1EBD hi\\u1EC3n th\\u1ECB \\u1EDF ph\\u1EA7n d\\u01B0\\u1EDBi. M\\u1ED9t s\\u1ED1 \\u1EA3nh c\\xF3 th\\u1EC3 b\\u1ECB thi\\u1EBFu, b\\u1EA1n c\\u1EA7n \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"th\\xEAm \\u1EA3nh th\\u1EE7 c\\xF4ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 118\n            }, this), \" b\\u1EB1ng c\\xE1ch b\\u1EA5m v\\xE0o \\xF4 \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-gray-100 px-1 rounded text-gray-800\",\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 193\n            }, this), \".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"Ki\\u1EC3m tra l\\u1EA1i n\\u1ED9i dung \\u0111\\u1EC1 \\u1EDF ph\\u1EA7n cu\\u1ED1i. H\\u1EC7 th\\u1ED1ng s\\u1EBD t\\u1EF1 \\u0111\\u1ED9ng ph\\xE1t hi\\u1EC7n c\\xE1c ph\\u1EA7n b\\u1EB1ng ch\\u1EEF \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"italic\",\n              children: \"\\\"PH\\u1EA6N I\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 115\n            }, this), \", \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"italic\",\n              children: \"\\\"PH\\u1EA6N II\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 157\n            }, this), \", \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"italic\",\n              children: \"\\\"PH\\u1EA6N III\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 200\n            }, this), \" n\\u1EBFu \\u0111\\u1EC1 r\\xF5 r\\xE0ng.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Sau khi ch\\u1EAFc ch\\u1EAFn n\\u1ED9i dung v\\xE0 \\u1EA3nh minh h\\u1ECDa \\u0111\\xE3 \\u0111\\xFAng, b\\u1EA1n c\\xF3 th\\u1EC3 ti\\u1EBFp t\\u1EE5c b\\u01B0\\u1EDBc ti\\u1EBFp theo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(UploadPdf, {\n        setPdf: handleFileChange,\n        deleteButton: false,\n        compact: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleOcr,\n        disabled: loadingOcr || !ocrFile,\n        className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed\",\n        children: loadingOcr ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 29\n          }, this), \"\\u0110ang x\\u1EED l\\xFD...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 29\n          }, this), \"X\\u1EED l\\xFD \\u0111\\u1EC1 thi\"]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 13\n    }, this), base64Images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-sm text-gray-600\",\n          children: [\"\\u1EA2nh minh h\\u1ECDa (\", base64Images.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleUploadImages,\n          disabled: loadingUploadImages,\n          className: \"flex items-center gap-2 text-sm px-3 py-1.5 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50\",\n          children: loadingUploadImages ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 37\n            }, this), \"\\u0110ang t\\u1EA3i...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(UploadCloud, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 37\n            }, this), \"T\\u1EA3i \\u1EA3nh l\\xEAn\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          multiple: true,\n          accept: \"image/*\",\n          ref: fileInputRef,\n          style: {\n            display: \"none\"\n          },\n          onChange: handleAddImages\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => fileInputRef.current.click(),\n          className: \"w-24 h-24 border-2 border-dashed rounded-lg flex items-center justify-center text-gray-500 hover:bg-gray-100 text-sm\",\n          children: \"+\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 25\n        }, this), base64Images.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative group w-24 h-24 border rounded-lg overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleRemoveImage(index),\n            className: \"absolute top-1 right-1 bg-red-600 text-white rounded-full w-5 h-5 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition\",\n            title: \"Xo\\xE1 \\u1EA3nh\",\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            src: image,\n            alt: \"Image \".concat(index),\n            className: \"w-full h-full object-contain\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 33\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 29\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 17\n    }, this), markDownExam && /*#__PURE__*/_jsxDEV(TextArea, {\n      value: markDownExam,\n      onChange: handleContentChange,\n      placeholder: \"N\\u1ED9i dung \\u0111\\u1EC1 thi sau khi OCR...\",\n      label: \"N\\u1ED9i dung \\u0111\\u1EC1 thi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 399,\n    columnNumber: 9\n  }, this);\n};\n_s5(Step2Form, \"PkU/fT8WF9fjd4vIkNasTjzd2Dw=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c7 = Step2Form;\nconst Step3Form = () => {\n  _s6();\n  const [isViewAdd, setIsViewAdd] = useState(true);\n  const [view, setView] = useState('TN');\n  const {\n    markDownExam,\n    questionTNContent,\n    questionDSContent,\n    questionTLNContent,\n    correctAnswerTN,\n    correctAnswerDS,\n    correctAnswerTLN\n  } = useSelector(state => state.addExam);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    if (!isViewAdd) return;\n    if (questionTNContent.trim() !== \"\" || correctAnswerTN.trim() !== \"\") {\n      setIsViewAdd(false);\n      setView('TN');\n    } else if (questionDSContent.trim() !== \"\" || correctAnswerDS.trim() !== \"\") {\n      setIsViewAdd(false);\n      setView('DS');\n    } else if (questionTLNContent.trim() !== \"\" || correctAnswerTLN.trim() !== \"\") {\n      setIsViewAdd(false);\n      setView('TLN');\n    }\n  }, [questionTNContent, correctAnswerTN, questionDSContent, correctAnswerDS, questionTLNContent, correctAnswerTLN]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3\",\n    children: [isViewAdd ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-4 px-3\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-8 h-8 mx-auto text-gray-400 mb-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-medium text-gray-900 mb-1\",\n        children: \"Th\\xEAm c\\xE2u h\\u1ECFi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-600 mb-3\",\n        children: \"T\\u1EA1o c\\xE2u h\\u1ECFi cho \\u0111\\u1EC1 thi c\\u1EE7a b\\u1EA1n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(ButtonAddQuestion, {\n          text: \"Th\\xEAm c\\xE2u tr\\u1EAFc nghi\\u1EC7m\",\n          onClick: () => {\n            setIsViewAdd(false);\n            setView('TN');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(ButtonAddQuestion, {\n          text: \"Th\\xEAm c\\xE2u \\u0111\\xFAng sai\",\n          onClick: () => {\n            setIsViewAdd(false);\n            setView('DS');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(ButtonAddQuestion, {\n          text: \"Th\\xEAm c\\xE2u tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\",\n          onClick: () => {\n            setIsViewAdd(false);\n            setView('TLN');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(NavigateBar, {\n      list: [{\n        id: 1,\n        name: 'Trắc nghiệm',\n        value: 'TN'\n      }, {\n        id: 2,\n        name: 'Đúng sai',\n        value: 'DS'\n      }, {\n        id: 3,\n        name: 'Trả lời ngắn',\n        value: 'TLN'\n      }],\n      active: view,\n      setActive: setView\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 17\n    }, this), view === 'TN' && !isViewAdd && /*#__PURE__*/_jsxDEV(AddTNQuestion, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 582,\n      columnNumber: 17\n    }, this), view === 'DS' && !isViewAdd && /*#__PURE__*/_jsxDEV(AddDSQuestion, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 17\n    }, this), view === 'TLN' && !isViewAdd && /*#__PURE__*/_jsxDEV(AddTLNQuestion, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 588,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 526,\n    columnNumber: 9\n  }, this);\n};\n_s6(Step3Form, \"r8vKu7qMQXpz+y2GBB/W7oUlP0w=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c8 = Step3Form;\nconst ListQuestions = _ref3 => {\n  let {\n    count,\n    title,\n    onClick,\n    i\n  } = _ref3;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex  flex-row w-full justify-start items-center border-b border-[#e3e4e5] pb-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#090a0a] text-xs font-bold font-bevietnam leading-loose whitespace-nowrap mr-4 flex-shrink-0 min-w-[6.2rem]\",\n      children: [title, \":\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 597,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-row gap-5 w-full overflow-x-auto min-h-max \",\n      children: Array.from({\n        length: count\n      }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: () => onClick(index),\n        className: \"cursor-pointer border text-xs border-[#e3e4e5] rounded flex justify-center whitespace-nowrap items-center gap-2.5 \".concat(i === index ? 'bg-[#253f61] text-white' : 'bg-white text-[#253f61]', \" px-2 py-1\"),\n        children: [\"C\\xE2u h\\u1ECFi \", index + 1]\n      }, index + title, true, {\n        fileName: _jsxFileName,\n        lineNumber: 602,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 596,\n    columnNumber: 9\n  }, this);\n};\n\n// Component TextArea với nút AI\n_c9 = ListQuestions;\nconst TextAreaWithAI = _ref4 => {\n  _s7();\n  let {\n    value,\n    onChange,\n    placeholder,\n    label,\n    Icon,\n    row = false\n  } = _ref4;\n  const dispatch = useDispatch();\n  const {\n    loading\n  } = useSelector(state => state.ai);\n  const handleAIFix = async () => {\n    if (!value || !value.trim()) {\n      alert('Vui lòng nhập nội dung trước khi sử dụng AI sửa lỗi');\n      return;\n    }\n    try {\n      const result = await dispatch(fixTextAndLatex(value)).unwrap();\n      if (result.hasChanges) {\n        onChange({\n          target: {\n            value: result.fixedText\n          }\n        });\n      } else {\n        alert('Không tìm thấy lỗi nào cần sửa');\n      }\n    } catch (error) {\n      console.error('Error fixing text:', error);\n      alert('Có lỗi xảy ra khi sử dụng AI sửa lỗi');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex gap-2 \".concat(row ? 'flex-row-reverse' : 'flex-col'),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"flex items-center gap-1 text-sm font-medium text-gray-700\",\n        children: [Icon && /*#__PURE__*/_jsxDEV(Icon, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 30\n        }, this), label]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 641,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAIFix,\n        disabled: loading || !(value !== null && value !== void 0 && value.trim()),\n        className: \"flex items-center gap-1 px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n        title: \"S\\u1EED d\\u1EE5ng AI \\u0111\\u1EC3 s\\u1EEDa ch\\xEDnh t\\u1EA3 v\\xE0 k\\xFD hi\\u1EC7u LaTeX\",\n        children: /*#__PURE__*/_jsxDEV(Sparkles, {\n          className: \"w-3 h-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 645,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 640,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n      value: value,\n      onChange: onChange,\n      placeholder: placeholder\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 639,\n    columnNumber: 9\n  }, this);\n};\n_s7(TextAreaWithAI, \"w5/sfgGGisvaz8PMddG7Tnrx0L0=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c10 = TextAreaWithAI;\nconst Step4Form = () => {\n  _s8();\n  const {\n    questions,\n    selectedIndex,\n    view\n  } = useSelector(state => state.addExam);\n  const dispatch = useDispatch();\n  const [questionCount, setQuestionCount] = useState({\n    TN: 0,\n    DS: 0,\n    TLN: 0\n  });\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const [optionChapter, setOptionChapter] = useState([]);\n  useEffect(() => {\n    if (questions) {\n      const counts = questions.reduce((acc, q) => {\n        const type = q.questionData.typeOfQuestion;\n        if (acc[type] !== undefined) acc[type]++;\n        return acc;\n      }, {\n        TN: 0,\n        DS: 0,\n        TLN: 0\n      });\n      setQuestionCount(counts);\n    }\n  }, [questions]);\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      var _questions$selectedIn, _questions$selectedIn2, _questions$selectedIn3, _questions$selectedIn4;\n      if ((_questions$selectedIn = questions[selectedIndex]) !== null && _questions$selectedIn !== void 0 && (_questions$selectedIn2 = _questions$selectedIn.questionData) !== null && _questions$selectedIn2 !== void 0 && _questions$selectedIn2.class && ((_questions$selectedIn3 = questions[selectedIndex]) === null || _questions$selectedIn3 === void 0 ? void 0 : (_questions$selectedIn4 = _questions$selectedIn3.questionData) === null || _questions$selectedIn4 === void 0 ? void 0 : _questions$selectedIn4.class.trim()) !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => {\n          var _questions$selectedIn5, _questions$selectedIn6;\n          return code.code.startsWith((_questions$selectedIn5 = questions[selectedIndex]) === null || _questions$selectedIn5 === void 0 ? void 0 : (_questions$selectedIn6 = _questions$selectedIn5.questionData) === null || _questions$selectedIn6 === void 0 ? void 0 : _questions$selectedIn6.class) && code.code.length === 5;\n        }));\n      } else {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, questions, selectedIndex]);\n  const handleQuestionChange = (e, field) => {\n    const newQuestions = questions.map((question, qIndex) => {\n      if (qIndex === selectedIndex) {\n        return {\n          ...question,\n          questionData: {\n            ...question.questionData,\n            [field]: e.target.value\n          }\n        };\n      }\n      return question;\n    });\n    dispatch(setQuestions(newQuestions));\n  };\n  const handleSolutionQuestionChange = newSolution => {\n    const newQuestions = questions.map((question, qIndex) => {\n      if (qIndex === selectedIndex) {\n        return {\n          ...question,\n          questionData: {\n            ...question.questionData,\n            solution: newSolution\n          }\n        };\n      }\n      return question;\n    });\n    dispatch(setQuestions(newQuestions));\n  };\n  const handleStatementChange = (index, value, field) => {\n    const newQuestions = questions.map((question, qIndex) => {\n      if (qIndex === selectedIndex) {\n        return {\n          ...question,\n          statements: question.statements.map((stmt, sIndex) => {\n            if (sIndex === index) {\n              return {\n                ...stmt,\n                [field]: value\n              };\n            }\n            return stmt;\n          })\n        };\n      }\n      return question;\n    });\n    dispatch(setQuestions(newQuestions));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3 w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \" font-medium text-gray-900 \",\n      children: \"Th\\xF4ng tin c\\xE2u h\\u1ECFi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 758,\n      columnNumber: 13\n    }, this), questions && questions[selectedIndex] && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900\",\n          children: \"Ph\\xE2n lo\\u1EA1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 763,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-row gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: questions[selectedIndex].questionData.class,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'class'),\n            options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n            className: \"text-xs\",\n            placeholder: \"Ch\\u1ECDn l\\u1EDBp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 765,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n            selectedOption: questions[selectedIndex].questionData.chapter,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'chapter'),\n            options: optionChapter,\n            className: \"text-xs\",\n            placeholder: \"Ch\\u1ECDn ch\\u01B0\\u01A1ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 772,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: questions[selectedIndex].questionData.difficulty,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'difficulty'),\n            options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n            className: \"text-xs\",\n            placeholder: \"Ch\\u1ECDn \\u0111\\u1ED9 kh\\xF3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 779,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 762,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \" bg-gray-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 788,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(TextAreaWithAI, {\n            value: questions[selectedIndex].questionData.content,\n            onChange: e => handleQuestionChange(e, 'content'),\n            placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n            label: \"C\\xE2u h\\u1ECFi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 29\n          }, this), (view === 'image' || questions[selectedIndex].questionData.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n            imageUrl: questions[selectedIndex].questionData.imageUrl,\n            onImageDrop: image => handleQuestionChange({\n              target: {\n                value: image\n              }\n            }, 'imageUrl'),\n            onImageRemove: () => handleQuestionChange({\n              target: {\n                value: ''\n              }\n            }, 'imageUrl')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 33\n          }, this), questions[selectedIndex].questionData.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: questions[selectedIndex].statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-2 items-center w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row gap-2 items-center w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs font-bold whitespace-nowrap\",\n                  children: questions[selectedIndex].questionData.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 809,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(TextAreaWithAI, {\n                  value: statement.content,\n                  onChange: e => handleStatementChange(index, e.target.value, 'content'),\n                  placeholder: \"Nh\\u1EADp n\\u1ED9i dung m\\u1EC7nh \\u0111\\u1EC1\",\n                  row: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 808,\n                columnNumber: 45\n              }, this), (view === 'image' || statement.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n                imageUrl: statement.imageUrl,\n                onImageDrop: image => handleStatementChange(index, image, 'imageUrl'),\n                onImageRemove: () => handleStatementChange(index, '', 'imageUrl')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 49\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 41\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 805,\n            columnNumber: 33\n          }, this), questions[selectedIndex].questionData.typeOfQuestion === 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              value: questions[selectedIndex].questionData.correctAnswer,\n              onChange: e => handleQuestionChange(e, 'correctAnswer'),\n              placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n              label: \"\\u0110\\xE1p \\xE1n\",\n              Icon: CheckCircle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 833,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(SolutionEditor, {\n            solution: questions[selectedIndex].questionData.solution,\n            onSolutionChange: handleSolutionQuestionChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 849,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 790,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 789,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 761,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 757,\n    columnNumber: 9\n  }, this);\n};\n_s8(Step4Form, \"mEGCa3uGuxnLJSepP+eV1Z59L8Y=\", false, function () {\n  return [useSelector, useDispatch, useSelector];\n});\n_c11 = Step4Form;\nconst LeftContent = () => {\n  _s9();\n  const dispatch = useDispatch();\n  const {\n    step,\n    examData,\n    loading,\n    examImage,\n    examFile,\n    questions\n  } = useSelector(state => state.addExam);\n  const handleNext = () => {\n    if (step < 4) dispatch(nextStep());\n  };\n  const handlePrev = () => {\n    if (step > 1) dispatch(prevStep());\n  };\n  const handleSubmit = async () => {\n    if (!validateExamData(examData, dispatch)) return;\n    await dispatch(postExam({\n      examData,\n      examImage,\n      questions: questions || [],\n      examFile\n    })).unwrap();\n    // Handle success\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_42px)]\",\n    children: [/*#__PURE__*/_jsxDEV(CompactStepHeader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 889,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto\",\n      children: [step === 1 && /*#__PURE__*/_jsxDEV(Step1Form, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 895,\n        columnNumber: 21\n      }, this), step === 2 && /*#__PURE__*/_jsxDEV(Step2Form, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 899,\n        columnNumber: 21\n      }, this), step === 3 && /*#__PURE__*/_jsxDEV(Step3Form, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 904,\n        columnNumber: 21\n      }, this), step === 4 && /*#__PURE__*/_jsxDEV(Step4Form, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 909,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 892,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-t border-gray-200 p-2 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePrev,\n          disabled: step === 1,\n          className: \"flex items-center gap-1 px-2 py-1 text-xs text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed\",\n          children: [/*#__PURE__*/_jsxDEV(ChevronLeft, {\n            className: \"w-3 h-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 921,\n            columnNumber: 25\n          }, this), \"Quay l\\u1EA1i\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 916,\n          columnNumber: 21\n        }, this), step < 3 ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleNext,\n          className: \"flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-xs\",\n          children: [\"Ti\\u1EBFp theo\", /*#__PURE__*/_jsxDEV(ChevronRight, {\n            className: \"w-3 h-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 931,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 926,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSubmit,\n          disabled: loading,\n          className: \"flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50 text-xs\",\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              minHeight: \"min-h-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 941,\n              columnNumber: 37\n            }, this), \"\\u0110ang t\\u1EA1o...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Save, {\n              className: \"w-3 h-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 946,\n              columnNumber: 37\n            }, this), \"T\\u1EA1o \\u0111\\u1EC1 thi\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 934,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 915,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 914,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 887,\n    columnNumber: 9\n  }, this);\n};\n_s9(LeftContent, \"JFH9vutr6E5+I+wgibQN8WmKR6Q=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c12 = LeftContent;\nexport default LeftContent;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12;\n$RefreshReg$(_c, \"Step1Form\");\n$RefreshReg$(_c2, \"ButtonAddQuestion\");\n$RefreshReg$(_c3, \"AddQuestionForm\");\n$RefreshReg$(_c4, \"AddTNQuestion\");\n$RefreshReg$(_c5, \"AddDSQuestion\");\n$RefreshReg$(_c6, \"AddTLNQuestion\");\n$RefreshReg$(_c7, \"Step2Form\");\n$RefreshReg$(_c8, \"Step3Form\");\n$RefreshReg$(_c9, \"ListQuestions\");\n$RefreshReg$(_c10, \"TextAreaWithAI\");\n$RefreshReg$(_c11, \"Step4Form\");\n$RefreshReg$(_c12, \"LeftContent\");", "map": {"version": 3, "names": ["useEffect", "useState", "useRef", "useDispatch", "useSelector", "setExamData", "postExam", "nextStep", "prevStep", "setExamImage", "setExamFile", "setQuestionTNContent", "setQuestionDSContent", "setQuestionTLNContent", "setCorrectAnswerTN", "setCorrectAnswerDS", "setCorrectAnswerTLN", "setQuestions", "setSelectedIndex", "setOcrFile", "setMarkDownExam", "setBase64Images", "DropMenuBarAdmin", "SuggestInputBarAdmin", "CompactStepHeader", "Clock", "Users", "BookOpen", "Image", "ImageIcon", "Upload", "FileText", "UploadCloud", "CheckCircle", "ChevronRight", "ChevronLeft", "Plus", "Save", "Trash2", "Info", "<PERSON><PERSON><PERSON>", "fixTextAndLatex", "ImageUpload", "UploadPdf", "LoadingSpinner", "NavigateBar", "SolutionEditor", "normalizeText", "validateExamData", "splitMarkdownToParts", "ImageDropZone", "TextArea", "PdfViewer", "ocrPdfWithMistral", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadBase64Images", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Step1Form", "_s", "dispatch", "examData", "examImage", "examFile", "state", "addExam", "codes", "updateExamData", "field", "value", "optionChapter", "setOptionChapter", "Array", "isArray", "class", "trim", "filter", "code", "startsWith", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "name", "onChange", "e", "target", "placeholder", "selectedOption", "typeOfExam", "option", "options", "year", "testDuration", "passRate", "chapter", "solutionUrl", "description", "rows", "checked", "public", "isClassroomExam", "image", "setImage", "img", "inputId", "compact", "setPdf", "pdf", "deleteButton", "_c", "ButtonAddQuestion", "_ref", "text", "onClick", "_c2", "AddQuestionForm", "_ref2", "questionContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleContentChange", "handleCorrectAnswerChange", "hintAnswer", "hintContent", "label", "Icon", "hint", "buttonFilterText", "_c3", "AddTNQuestion", "_s2", "questionT<PERSON>ontent", "correctAnswerTN", "_c4", "AddDSQuestion", "_s3", "question<PERSON><PERSON><PERSON><PERSON>", "correctAnswerDS", "_c5", "AddTLNQuestion", "_s4", "questionTLNContent", "correctAnswerTLN", "_c6", "convertFileToBase64", "file", "Promise", "resolve", "reject", "reader", "FileReader", "onloadend", "result", "onerror", "readAsDataURL", "Step2Form", "_s5", "fileInputRef", "ocrFile", "markDownExam", "loadingOcr", "base64Images", "loadingUploadImages", "images", "handleFileChange", "handleAddImages", "event", "files", "length", "base64List", "all", "from", "map", "handleOcr", "handleRemoveImage", "indexToRemove", "newImages", "_", "i", "handleUploadImages", "folder", "disabled", "multiple", "accept", "ref", "style", "display", "current", "click", "index", "title", "src", "alt", "concat", "_c7", "Step3Form", "_s6", "isViewAdd", "setIsViewAdd", "view", "<PERSON><PERSON><PERSON><PERSON>", "list", "id", "active", "setActive", "_c8", "ListQuestions", "_ref3", "count", "_c9", "TextAreaWithAI", "_ref4", "_s7", "row", "loading", "ai", "handleAIFix", "alert", "unwrap", "has<PERSON><PERSON><PERSON>", "fixedText", "error", "console", "_c10", "Step4Form", "_s8", "questions", "selectedIndex", "questionCount", "setQuestionCount", "TN", "DS", "TLN", "prefixTN", "prefixDS", "counts", "reduce", "acc", "q", "questionData", "typeOfQuestion", "undefined", "_questions$selectedIn", "_questions$selectedIn2", "_questions$selectedIn3", "_questions$selectedIn4", "_questions$selectedIn5", "_questions$selectedIn6", "handleQuestionChange", "newQuestions", "question", "qIndex", "handleSolutionQuestionChange", "newSolution", "solution", "handleStatementChange", "statements", "stmt", "sIndex", "difficulty", "content", "imageUrl", "onImageDrop", "onImageRemove", "statement", "<PERSON><PERSON><PERSON><PERSON>", "onSolutionChange", "_c11", "LeftContent", "_s9", "step", "handleNext", "handlePrev", "handleSubmit", "minHeight", "_c12", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/LeftContent.jsx"], "sourcesContent": ["// Optimized Form Panel Component\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport {\r\n    setExamData,\r\n    postExam,\r\n    nextStep,\r\n    prevStep,\r\n    setExamImage,\r\n    setExamFile,\r\n    setQuestionTNContent,\r\n    setQuestionDSContent,\r\n    setQuestionTLNContent,\r\n    setCorrectAnswerTN,\r\n    setCorrectAnswerDS,\r\n    setCorrectAnswerTLN,\r\n    setQuestions,\r\n    setSelectedIndex,\r\n    setOcrFile,\r\n    setMarkDownExam,\r\n    setBase64Images,\r\n} from \"src/features/addExam/addExamSlice\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport CompactStepHeader from \"./CompactStepHeader\";\r\nimport { Clock, Users, BookOpen, Image as ImageIcon, Upload, FileText, UploadCloud, CheckCircle, ChevronRight, ChevronLeft, Plus, Save, Trash2, Info, <PERSON>rkles } from \"lucide-react\";\r\nimport { fixTextAndLatex } from \"src/features/ai/aiSlice\";\r\nimport ImageUpload from \"src/components/image/UploadImage\";\r\nimport UploadPdf from \"src/components/UploadPdf\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\nimport NavigateBar from \"./NavigateBar\";\r\nimport SolutionEditor from \"./SolutionEditor\";\r\nimport { normalizeText, validateExamData, splitMarkdownToParts } from \"src/utils/question/questionUtils\";\r\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\r\nimport TextArea from \"src/components/input/TextArea\";\r\nimport PdfViewer from \"../ViewPdf\";\r\nimport { ocrPdfWithMistral } from \"src/features/addExam/addExamSlice\";\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\nimport { uploadBase64Images } from \"src/features/image/imageSlice\";\r\n\r\nconst Step1Form = () => {\r\n    const dispatch = useDispatch();\r\n    const { examData, examImage, examFile } = useSelector((state) => state.addExam);\r\n    const { codes } = useSelector(state => state.codes);\r\n\r\n    const updateExamData = (field, value) => {\r\n        dispatch(setExamData({ field, value }));\r\n    };\r\n\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (examData.class && examData.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.startsWith(examData.class))\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"])\r\n            }\r\n        } else {\r\n            setOptionChapter();\r\n        }\r\n    }, [codes, examData.class]);\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3\">\r\n            {/* Compact Name & Type Row */}\r\n            <div className=\"grid grid-cols-2 gap-2\">\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        Tên đề thi <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input\r\n                        type=\"text\"\r\n                        value={examData.name || ''}\r\n                        onChange={(e) => updateExamData('name', e.target.value)}\r\n                        className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                        placeholder=\"Nhập tên đề thi\"\r\n                    />\r\n                </div>\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        Kiểu đề <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={examData.typeOfExam}\r\n                        onChange={(option) => updateExamData('typeOfExam', option)}\r\n                        options={Array.isArray(codes[\"exam type\"]) ? codes[\"exam type\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n            </div>\r\n\r\n            {/* Compact Class & Year Row */}\r\n            <div className=\"grid grid-cols-2 gap-2\">\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        Lớp <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={examData.class}\r\n                        onChange={(option) => updateExamData('class', option)}\r\n                        options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        Năm <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={examData.year}\r\n                        onChange={(option) => updateExamData('year', option)}\r\n                        options={Array.isArray(codes[\"year\"]) ? codes[\"year\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n            </div>\r\n\r\n            {/* Compact Duration & Pass Rate Row */}\r\n            <div className=\"grid grid-cols-2 gap-2\">\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <Clock className=\"w-3 h-3 inline mr-1\" />\r\n                        Thời gian (phút)\r\n                    </label>\r\n                    <input\r\n                        type=\"number\"\r\n                        value={examData.testDuration || ''}\r\n                        onChange={(e) => updateExamData('testDuration', e.target.value)}\r\n                        className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                        placeholder=\"90\"\r\n                    />\r\n                </div>\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <Users className=\"w-3 h-3 inline mr-1\" />\r\n                        Điểm đạt (%)\r\n                    </label>\r\n                    <input\r\n                        type=\"number\"\r\n                        value={examData.passRate || ''}\r\n                        onChange={(e) => updateExamData('passRate', e.target.value)}\r\n                        className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                        placeholder=\"50\"\r\n                    />\r\n                </div>\r\n            </div>\r\n\r\n            {/* Chapter (conditional) */}\r\n            {examData.typeOfExam === \"OT\" && (\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <BookOpen className=\"w-3 h-3 inline mr-1\" />\r\n                        Chương\r\n                    </label>\r\n                    <SuggestInputBarAdmin\r\n                        selectedOption={examData.chapter}\r\n                        onChange={(option) => updateExamData('chapter', option)}\r\n                        options={optionChapter}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n            )}\r\n            <div>\r\n                <label className=\"block text-xs font-medium text-gray-700 mb-1\">Link lời giải</label>\r\n                <textarea\r\n                    value={examData.solutionUrl}\r\n                    onChange={(e) => updateExamData('solutionUrl', e.target.value)}\r\n                    className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                    placeholder=\"Nhập link lời giải vd: youtube, ...\"\r\n                />\r\n            </div>\r\n            {/* Compact Description */}\r\n            <div>\r\n                <label className=\"block text-xs font-medium text-gray-700 mb-1\">Mô tả</label>\r\n                <textarea\r\n                    value={examData.description || ''}\r\n                    onChange={(e) => updateExamData('description', e.target.value)}\r\n                    rows={2}\r\n                    className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                    placeholder=\"Mô tả ngắn về đề thi...\"\r\n                />\r\n            </div>\r\n\r\n            {/* Compact Checkboxes */}\r\n            <div className=\"flex items-center gap-3\">\r\n                <label className=\"flex items-center text-xs\">\r\n                    <input\r\n                        type=\"checkbox\"\r\n                        checked={examData.public || false}\r\n                        onChange={(e) => updateExamData('public', e.target.checked)}\r\n                        className=\"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\r\n                    />\r\n                    <span className=\"ml-1 text-gray-700\">Công khai</span>\r\n                </label>\r\n                <label className=\"flex items-center text-xs\">\r\n                    <input\r\n                        type=\"checkbox\"\r\n                        checked={examData.isClassroomExam || false}\r\n                        onChange={(e) => updateExamData('isClassroomExam', e.target.checked)}\r\n                        className=\"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\r\n                    />\r\n                    <span className=\"ml-1 text-gray-700\">Đề thi lớp</span>\r\n                </label>\r\n            </div>\r\n\r\n            {/* Compact File Uploads */}\r\n            <div className=\"flex flex-col gap-2\">\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <ImageIcon className=\"w-3 h-3 inline mr-1\" />\r\n                        Ảnh đề thi\r\n                    </label>\r\n                    <ImageUpload\r\n                        image={examImage}\r\n                        setImage={(img) => dispatch(setExamImage(img))}\r\n                        inputId=\"exam-image-compact\"\r\n                        compact={true}\r\n                    />\r\n                </div>\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <Upload className=\"w-3 h-3 inline mr-1\" />\r\n                        File PDF\r\n                    </label>\r\n                    <UploadPdf\r\n                        setPdf={(pdf) => dispatch(setExamFile(pdf))}\r\n                        deleteButton={false}\r\n                        compact={true}\r\n                    />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nconst ButtonAddQuestion = ({ text, onClick }) => {\r\n    return (\r\n        <button\r\n            onClick={onClick}\r\n            className=\"w-full px-3 py-2 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors text-xs\">\r\n            <Plus className=\"w-3 h-3 inline mr-1\" />\r\n            {text}\r\n        </button>\r\n    )\r\n}\r\n\r\n\r\n\r\nconst AddQuestionForm = ({ questionContent, correctAnswerContent, handleContentChange, handleCorrectAnswerChange, hintAnswer, hintContent }) => {\r\n    return (\r\n        <div className=\"p-3 flex flex-col gap-4\">\r\n            <TextArea\r\n                value={correctAnswerContent}\r\n                onChange={handleCorrectAnswerChange}\r\n                placeholder=\"Nhập đáp án\"\r\n                label=\"Đáp án\"\r\n                Icon={CheckCircle}\r\n                hint={hintAnswer}\r\n            />\r\n            <TextArea\r\n                value={questionContent}\r\n                onChange={handleContentChange}\r\n                placeholder=\"Nhập nội dung câu hỏi\"\r\n                label=\"Câu hỏi\"\r\n                Icon={Plus}\r\n                hint={hintContent}\r\n                buttonFilterText={{\r\n                    text: \"Lọc\",\r\n                    onClick: () => {\r\n                        handleContentChange({\r\n                            target: {\r\n                                value: normalizeText(questionContent)\r\n                            }\r\n                        });\r\n                    }\r\n                }}\r\n            />\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nconst AddTNQuestion = () => {\r\n    const dispatch = useDispatch();\r\n    const { questionTNContent, correctAnswerTN } = useSelector((state) => state.addExam);\r\n\r\n    const handleContentChange = (e) => {\r\n        dispatch(setQuestionTNContent(e.target.value));\r\n    };\r\n\r\n    const handleCorrectAnswerChange = (e) => {\r\n        dispatch(setCorrectAnswerTN(e.target.value));\r\n    };\r\n\r\n    return (\r\n        <AddQuestionForm\r\n            questionContent={questionTNContent}\r\n            correctAnswerContent={correctAnswerTN}\r\n            handleContentChange={handleContentChange}\r\n            handleCorrectAnswerChange={handleCorrectAnswerChange}\r\n            hintAnswer=\"Đáp án trắc nghiệm: A b c D ...\"\r\n            hintContent=\"Nội dung trắc nghiệm: Paste cả câu hỏi mệnh đề và lời giải\"\r\n        />\r\n    )\r\n}\r\n\r\nconst AddDSQuestion = () => {\r\n    const dispatch = useDispatch();\r\n    const { questionDSContent, correctAnswerDS } = useSelector((state) => state.addExam);\r\n\r\n    const handleContentChange = (e) => {\r\n        dispatch(setQuestionDSContent(e.target.value));\r\n    };\r\n\r\n    const handleCorrectAnswerChange = (e) => {\r\n        dispatch(setCorrectAnswerDS(e.target.value));\r\n    };\r\n\r\n    return (\r\n        <AddQuestionForm\r\n            questionContent={questionDSContent}\r\n            correctAnswerContent={correctAnswerDS}\r\n            handleContentChange={handleContentChange}\r\n            handleCorrectAnswerChange={handleCorrectAnswerChange}\r\n            hintAnswer=\"Đáp án đúng sai: ĐĐSS dsss DSDS ...\"\r\n            hintContent=\"Nội dung câu hỏi: Paste cả câu hỏi mệnh đề và lời giải\"\r\n        />\r\n    )\r\n}\r\n\r\nconst AddTLNQuestion = () => {\r\n    const dispatch = useDispatch();\r\n    const { questionTLNContent, correctAnswerTLN } = useSelector((state) => state.addExam);\r\n\r\n    const handleContentChange = (e) => {\r\n        dispatch(setQuestionTLNContent(e.target.value));\r\n    };\r\n\r\n    const handleCorrectAnswerChange = (e) => {\r\n        dispatch(setCorrectAnswerTLN(e.target.value));\r\n    };\r\n\r\n    return (\r\n        <AddQuestionForm\r\n            questionContent={questionTLNContent}\r\n            correctAnswerContent={correctAnswerTLN}\r\n            handleContentChange={handleContentChange}\r\n            handleCorrectAnswerChange={handleCorrectAnswerChange}\r\n            hintAnswer=\"Đáp án trả lời ngắn: 3,14 1.5 3,2\"\r\n            hintContent=\"Nội dung câu hỏi: Paste cả câu hỏi mệnh đề và lời giải\"\r\n        />\r\n    )\r\n}\r\n\r\nconst convertFileToBase64 = (file) => {\r\n    return new Promise((resolve, reject) => {\r\n        const reader = new FileReader();\r\n        reader.onloadend = () => resolve(reader.result);\r\n        reader.onerror = reject;\r\n        reader.readAsDataURL(file);\r\n    });\r\n};\r\n\r\n\r\nconst Step2Form = () => {\r\n    const dispatch = useDispatch();\r\n    const fileInputRef = useRef(null);\r\n    const { ocrFile, markDownExam, loadingOcr, base64Images } = useSelector((state) => state.addExam);\r\n    const { loadingUploadImages } = useSelector((state) => state.images);\r\n\r\n    const handleFileChange = (file) => dispatch(setOcrFile(file));\r\n\r\n    const handleAddImages = async (event) => {\r\n        const files = event.target.files;\r\n        if (!files || files.length === 0) return;\r\n\r\n        const base64List = await Promise.all(\r\n            Array.from(files).map(file => convertFileToBase64(file))\r\n        );\r\n        dispatch(setBase64Images([...base64Images, ...base64List]));\r\n    };\r\n\r\n    const handleOcr = () => dispatch(ocrPdfWithMistral(ocrFile));\r\n\r\n    const handleContentChange = (e) => dispatch(setMarkDownExam(e.target.value));\r\n\r\n    const handleRemoveImage = (indexToRemove) => {\r\n        const newImages = base64Images.filter((_, i) => i !== indexToRemove);\r\n        dispatch(setBase64Images(newImages));\r\n    };\r\n\r\n    const handleUploadImages = () => {\r\n        dispatch(uploadBase64Images({ images: base64Images, folder: \"questionImage\" }));\r\n    };\r\n\r\n    return (\r\n        <div className=\"p-5 bg-white space-y-6\">\r\n            {/* Upload PDF + OCR */}\r\n            <div className=\"space-y-2\">\r\n                <div className=\"space-y-1 px-3 py-2 rounded bg-gray-50 border border-gray-200 text-sm text-gray-700 leading-relaxed\">\r\n                    <p className=\"font-semibold text-gray-900\">📝 Hướng dẫn xử lý đề thi (mọi người không cần phải dùng mathpix nữa)</p>\r\n                    <ul className=\"list-disc pl-5 space-y-1\">\r\n                        <li>Tải file <span className=\"font-medium text-blue-600\">PDF đề thi có chứa cả đáp án</span>.</li>\r\n                        <li>Ấn nút <span className=\"font-medium text-blue-600\">“Xử lý đề thi”</span> để AI thực hiện nhận dạng (OCR) nội dung và tự động cắt ảnh minh họa.</li>\r\n                        <li>Sau khi xử lý, ảnh minh họa sẽ hiển thị ở phần dưới. Một số ảnh có thể bị thiếu, bạn cần <span className=\"font-medium\">thêm ảnh thủ công</span> bằng cách bấm vào ô <span className=\"bg-gray-100 px-1 rounded text-gray-800\">+</span>.</li>\r\n                        <li>Kiểm tra lại nội dung đề ở phần cuối. Hệ thống sẽ tự động phát hiện các phần bằng chữ <span className=\"italic\">\"PHẦN I\"</span>, <span className=\"italic\">\"PHẦN II\"</span>, <span className=\"italic\">\"PHẦN III\"</span> nếu đề rõ ràng.</li>\r\n                        <li>Sau khi chắc chắn nội dung và ảnh minh họa đã đúng, bạn có thể tiếp tục bước tiếp theo.</li>\r\n                    </ul>\r\n                </div>\r\n                <UploadPdf setPdf={handleFileChange} deleteButton={false} compact={true} />\r\n\r\n                <button\r\n                    onClick={handleOcr}\r\n                    disabled={loadingOcr || !ocrFile}\r\n                    className=\"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                    {loadingOcr ? (\r\n                        <>\r\n                            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\r\n                            Đang xử lý...\r\n                        </>\r\n                    ) : (\r\n                        <>\r\n                            <FileText className=\"w-4 h-4\" />\r\n                            Xử lý đề thi\r\n                        </>\r\n                    )}\r\n                </button>\r\n            </div>\r\n\r\n            {/* Upload và Preview ảnh */}\r\n            {base64Images.length > 0 && (\r\n                <div className=\"space-y-2\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                        <span className=\"font-medium text-sm text-gray-600\">Ảnh minh họa ({base64Images.length})</span>\r\n                        <button\r\n                            onClick={handleUploadImages}\r\n                            disabled={loadingUploadImages}\r\n                            className=\"flex items-center gap-2 text-sm px-3 py-1.5 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50\"\r\n                        >\r\n                            {loadingUploadImages ? (\r\n                                <>\r\n                                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\r\n                                    Đang tải...\r\n                                </>\r\n                            ) : (\r\n                                <>\r\n                                    <UploadCloud className=\"w-4 h-4\" />\r\n                                    Tải ảnh lên\r\n                                </>\r\n                            )}\r\n                        </button>\r\n                    </div>\r\n\r\n                    <div className=\"flex flex-wrap gap-3\">\r\n                        <input\r\n                            type=\"file\"\r\n                            multiple\r\n                            accept=\"image/*\"\r\n                            ref={fileInputRef}\r\n                            style={{ display: \"none\" }}\r\n                            onChange={handleAddImages}\r\n                        />\r\n                        <button\r\n                            onClick={() => fileInputRef.current.click()}\r\n                            className=\"w-24 h-24 border-2 border-dashed rounded-lg flex items-center justify-center text-gray-500 hover:bg-gray-100 text-sm\"\r\n                        >\r\n                            +\r\n                        </button>\r\n\r\n                        {base64Images.map((image, index) => (\r\n                            <div key={index} className=\"relative group w-24 h-24 border rounded-lg overflow-hidden\">\r\n                                <button\r\n                                    onClick={() => handleRemoveImage(index)}\r\n                                    className=\"absolute top-1 right-1 bg-red-600 text-white rounded-full w-5 h-5 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition\"\r\n                                    title=\"Xoá ảnh\"\r\n                                >\r\n                                    ×\r\n                                </button>\r\n                                <img\r\n                                    src={image}\r\n                                    alt={`Image ${index}`}\r\n                                    className=\"w-full h-full object-contain\"\r\n                                />\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n            {/* Nội dung đề thi */}\r\n            {markDownExam && (\r\n                <TextArea\r\n                    value={markDownExam}\r\n                    onChange={handleContentChange}\r\n                    placeholder=\"Nội dung đề thi sau khi OCR...\"\r\n                    label=\"Nội dung đề thi\"\r\n                />\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nconst Step3Form = () => {\r\n    const [isViewAdd, setIsViewAdd] = useState(true);\r\n    const [view, setView] = useState('TN');\r\n    const { markDownExam, questionTNContent, questionDSContent, questionTLNContent, correctAnswerTN, correctAnswerDS, correctAnswerTLN } = useSelector((state) => state.addExam);\r\n    const dispatch = useDispatch();\r\n    useEffect(() => {\r\n        if (!isViewAdd) return\r\n        if (questionTNContent.trim() !== \"\" || correctAnswerTN.trim() !== \"\") {\r\n            setIsViewAdd(false);\r\n            setView('TN')\r\n        } else if (questionDSContent.trim() !== \"\" || correctAnswerDS.trim() !== \"\") {\r\n            setIsViewAdd(false);\r\n            setView('DS')\r\n        } else if (questionTLNContent.trim() !== \"\" || correctAnswerTLN.trim() !== \"\") {\r\n            setIsViewAdd(false);\r\n            setView('TLN')\r\n        }\r\n    }, [questionTNContent, correctAnswerTN, questionDSContent, correctAnswerDS, questionTLNContent, correctAnswerTLN]);\r\n\r\n    return (\r\n        <div className=\"space-y-3\">\r\n            {isViewAdd ? (\r\n                <div className=\"text-center py-4 px-3\">\r\n                    <FileText className=\"w-8 h-8 mx-auto text-gray-400 mb-2\" />\r\n                    <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thêm câu hỏi</h3>\r\n                    <p className=\"text-xs text-gray-600 mb-3\">\r\n                        Tạo câu hỏi cho đề thi của bạn\r\n                    </p>\r\n                    <div className=\"space-y-2\">\r\n                        <ButtonAddQuestion\r\n                            text=\"Thêm câu trắc nghiệm\"\r\n                            onClick={() => {\r\n                                setIsViewAdd(false);\r\n                                setView('TN')\r\n                            }}\r\n                        />\r\n                        <ButtonAddQuestion\r\n                            text=\"Thêm câu đúng sai\"\r\n                            onClick={() => {\r\n                                setIsViewAdd(false);\r\n                                setView('DS')\r\n                            }}\r\n                        />\r\n                        <ButtonAddQuestion\r\n                            text=\"Thêm câu trả lời ngắn\"\r\n                            onClick={() => {\r\n                                setIsViewAdd(false);\r\n                                setView('TLN')\r\n                            }}\r\n                        />\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <NavigateBar\r\n                    list={[{\r\n                        id: 1,\r\n                        name: 'Trắc nghiệm',\r\n                        value: 'TN'\r\n                    },\r\n                    {\r\n                        id: 2,\r\n                        name: 'Đúng sai',\r\n                        value: 'DS'\r\n                    },\r\n                    {\r\n                        id: 3,\r\n                        name: 'Trả lời ngắn',\r\n                        value: 'TLN'\r\n                    }\r\n                    ]}\r\n                    active={view}\r\n                    setActive={setView}\r\n                />\r\n            )}\r\n\r\n            {view === 'TN' && !isViewAdd && (\r\n                <AddTNQuestion />\r\n            )}\r\n            {view === 'DS' && !isViewAdd && (\r\n                <AddDSQuestion />\r\n            )}\r\n            {view === 'TLN' && !isViewAdd && (\r\n                <AddTLNQuestion />\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst ListQuestions = ({ count, title, onClick, i }) => {\r\n    return (\r\n        <div className=\"flex  flex-row w-full justify-start items-center border-b border-[#e3e4e5] pb-2\">\r\n            <div className=\"text-[#090a0a] text-xs font-bold font-bevietnam leading-loose whitespace-nowrap mr-4 flex-shrink-0 min-w-[6.2rem]\">\r\n                {title}:\r\n            </div>\r\n            <div className=\"flex flex-row gap-5 w-full overflow-x-auto min-h-max \">\r\n                {Array.from({ length: count }).map((_, index) => (\r\n                    <div\r\n                        key={index + title}\r\n                        onClick={() => onClick(index)}\r\n                        className={`cursor-pointer border text-xs border-[#e3e4e5] rounded flex justify-center whitespace-nowrap items-center gap-2.5 ${i === index ? 'bg-[#253f61] text-white' : 'bg-white text-[#253f61]'} px-2 py-1`}>\r\n                        Câu hỏi {index + 1}\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\n// Component TextArea với nút AI\r\nconst TextAreaWithAI = ({ value, onChange, placeholder, label, Icon, row = false }) => {\r\n    const dispatch = useDispatch();\r\n    const { loading } = useSelector((state) => state.ai);\r\n\r\n    const handleAIFix = async () => {\r\n        if (!value || !value.trim()) {\r\n            alert('Vui lòng nhập nội dung trước khi sử dụng AI sửa lỗi');\r\n            return;\r\n        }\r\n\r\n        try {\r\n            const result = await dispatch(fixTextAndLatex(value)).unwrap();\r\n            if (result.hasChanges) {\r\n                onChange({ target: { value: result.fixedText } });\r\n            } else {\r\n                alert('Không tìm thấy lỗi nào cần sửa');\r\n            }\r\n        } catch (error) {\r\n            console.error('Error fixing text:', error);\r\n            alert('Có lỗi xảy ra khi sử dụng AI sửa lỗi');\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className={`flex gap-2 ${row ? 'flex-row-reverse' : 'flex-col'}`}>\r\n            <div className=\"flex items-center justify-between\">\r\n                <label className=\"flex items-center gap-1 text-sm font-medium text-gray-700\">\r\n                    {Icon && <Icon className=\"w-4 h-4\" />}\r\n                    {label}\r\n                </label>\r\n                <button\r\n                    onClick={handleAIFix}\r\n                    disabled={loading || !value?.trim()}\r\n                    className=\"flex items-center gap-1 px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                    title=\"Sử dụng AI để sửa chính tả và ký hiệu LaTeX\"\r\n                >\r\n                    <Sparkles className=\"w-3 h-3\" />\r\n                </button>\r\n            </div>\r\n            <TextArea\r\n                value={value}\r\n                onChange={onChange}\r\n                placeholder={placeholder}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nconst Step4Form = () => {\r\n    const { questions, selectedIndex, view } = useSelector((state) => state.addExam);\r\n    const dispatch = useDispatch();\r\n    const [questionCount, setQuestionCount] = useState({\r\n        TN: 0,\r\n        DS: 0,\r\n        TLN: 0\r\n    });\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    const { codes } = useSelector((state) => state.codes);\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n\r\n    useEffect(() => {\r\n        if (questions) {\r\n            const counts = questions.reduce(\r\n                (acc, q) => {\r\n                    const type = q.questionData.typeOfQuestion;\r\n                    if (acc[type] !== undefined) acc[type]++;\r\n                    return acc;\r\n                },\r\n                { TN: 0, DS: 0, TLN: 0 }\r\n            );\r\n\r\n            setQuestionCount(counts);\r\n        }\r\n    }, [questions]);\r\n\r\n\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (questions[selectedIndex]?.questionData?.class && questions[selectedIndex]?.questionData?.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.startsWith(questions[selectedIndex]?.questionData?.class) && code.code.length === 5)\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"].filter((code) => code.code.length === 5));\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, questions, selectedIndex]);\r\n\r\n    const handleQuestionChange = (e, field) => {\r\n        const newQuestions = questions.map((question, qIndex) => {\r\n            if (qIndex === selectedIndex) {\r\n                return {\r\n                    ...question,\r\n                    questionData: {\r\n                        ...question.questionData,\r\n                        [field]: e.target.value,\r\n                    }\r\n                };\r\n            }\r\n            return question;\r\n        });\r\n        dispatch(setQuestions(newQuestions));\r\n    };\r\n    const handleSolutionQuestionChange = (newSolution) => {\r\n        const newQuestions = questions.map((question, qIndex) => {\r\n            if (qIndex === selectedIndex) {\r\n                return {\r\n                    ...question,\r\n                    questionData: {\r\n                        ...question.questionData,\r\n                        solution: newSolution,\r\n                    }\r\n                };\r\n            }\r\n            return question;\r\n        });\r\n        dispatch(setQuestions(newQuestions));\r\n    };\r\n\r\n    const handleStatementChange = (index, value, field) => {\r\n        const newQuestions = questions.map((question, qIndex) => {\r\n            if (qIndex === selectedIndex) {\r\n                return {\r\n                    ...question,\r\n                    statements: question.statements.map((stmt, sIndex) => {\r\n                        if (sIndex === index) {\r\n                            return { ...stmt, [field]: value };\r\n                        }\r\n                        return stmt;\r\n                    })\r\n                };\r\n            }\r\n            return question;\r\n        });\r\n        dispatch(setQuestions(newQuestions));\r\n    };\r\n\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3 w-full\">\r\n            <h3 className=\" font-medium text-gray-900 \">Thông tin câu hỏi</h3>\r\n\r\n            {questions && questions[selectedIndex] && (\r\n                <div className=\"space-y-3 w-full\">\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900\">Phân loại</h3>\r\n                        <div className=\"flex flex-row gap-2\">\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={questions[selectedIndex].questionData.class}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'class')}\r\n                                options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                                className=\"text-xs\"\r\n                                placeholder=\"Chọn lớp\"\r\n                            />\r\n                            <SuggestInputBarAdmin\r\n                                selectedOption={questions[selectedIndex].questionData.chapter}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'chapter')}\r\n                                options={optionChapter}\r\n                                className=\"text-xs\"\r\n                                placeholder=\"Chọn chương\"\r\n                            />\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={questions[selectedIndex].questionData.difficulty}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'difficulty')}\r\n                                options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                                className=\"text-xs\"\r\n                                placeholder=\"Chọn độ khó\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <hr className=\" bg-gray-200\"></hr>\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <div className=\"space-y-2\">\r\n                            <TextAreaWithAI\r\n                                value={questions[selectedIndex].questionData.content}\r\n                                onChange={(e) => handleQuestionChange(e, 'content')}\r\n                                placeholder=\"Nhập nội dung câu hỏi\"\r\n                                label=\"Câu hỏi\"\r\n                            />\r\n                            {(view === 'image' || questions[selectedIndex].questionData.imageUrl) && (\r\n                                <ImageDropZone\r\n                                    imageUrl={questions[selectedIndex].questionData.imageUrl}\r\n                                    onImageDrop={(image) => handleQuestionChange({ target: { value: image } }, 'imageUrl')}\r\n                                    onImageRemove={() => handleQuestionChange({ target: { value: '' } }, 'imageUrl')}\r\n                                />\r\n                            )}\r\n                            {questions[selectedIndex].questionData.typeOfQuestion !== 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    {questions[selectedIndex].statements.map((statement, index) => (\r\n                                        <div key={index} className=\"flex flex-col gap-2 items-center w-full\">\r\n                                            <div className=\"flex flex-row gap-2 items-center w-full\">\r\n                                                <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                                    {questions[selectedIndex].questionData.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}\r\n                                                </p>\r\n                                                <TextAreaWithAI\r\n                                                    value={statement.content}\r\n                                                    onChange={(e) => handleStatementChange(index, e.target.value, 'content')}\r\n                                                    placeholder=\"Nhập nội dung mệnh đề\"\r\n                                                    row={true}\r\n                                                />\r\n                                            </div>\r\n                                            {(view === 'image' || statement.imageUrl) && (\r\n                                                <ImageDropZone\r\n                                                    imageUrl={statement.imageUrl}\r\n                                                    onImageDrop={(image) => handleStatementChange(index, image, 'imageUrl')}\r\n                                                    onImageRemove={() => handleStatementChange(index, '', 'imageUrl')}\r\n                                                />\r\n                                            )}\r\n\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            )}\r\n                            {questions[selectedIndex].questionData.typeOfQuestion === 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    <TextArea\r\n                                        value={questions[selectedIndex].questionData.correctAnswer}\r\n                                        onChange={(e) => handleQuestionChange(e, 'correctAnswer')}\r\n                                        placeholder=\"Nhập đáp án\"\r\n                                        label=\"Đáp án\"\r\n                                        Icon={CheckCircle}\r\n                                    />\r\n                                </div>\r\n                            )}\r\n                            {/* <TextArea\r\n                                value={questions[selectedIndex].questionData.solution}\r\n                                onChange={(e) => handleQuestionChange(e, 'solution')}\r\n                                placeholder=\"Nhập lời giải\"\r\n                                label=\"Lời giải\"\r\n                                Icon={CheckCircle}\r\n                            /> */}\r\n                            <SolutionEditor\r\n                                solution={questions[selectedIndex].questionData.solution}\r\n                                onSolutionChange={handleSolutionQuestionChange}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nconst LeftContent = () => {\r\n    const dispatch = useDispatch();\r\n    const { step, examData, loading, examImage, examFile, questions } = useSelector((state) => state.addExam);\r\n    const handleNext = () => {\r\n        if (step < 4) dispatch(nextStep());\r\n    };\r\n\r\n    const handlePrev = () => {\r\n        if (step > 1) dispatch(prevStep());\r\n    };\r\n\r\n    const handleSubmit = async () => {\r\n        if (!validateExamData(examData, dispatch)) return;\r\n\r\n        await dispatch(postExam({\r\n            examData,\r\n            examImage,\r\n            questions: questions || [],\r\n            examFile,\r\n        })).unwrap();\r\n        // Handle success\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_42px)]\">\r\n            {/* Compact Step Header */}\r\n            <CompactStepHeader />\r\n\r\n            {/* Scrollable Form Content */}\r\n            <div className=\"flex-1 overflow-y-auto\">\r\n                {/* Step 1: Basic Information */}\r\n                {step === 1 && (\r\n                    <Step1Form />\r\n                )}\r\n                {/* Step 2: Questions */}\r\n                {step === 2 && (\r\n                    <Step2Form />\r\n                )}\r\n\r\n                {/* Step 3: Questions */}\r\n                {step === 3 && (\r\n                    <Step3Form />\r\n                )}\r\n\r\n                {/* Step 4: Confirmation */}\r\n                {step === 4 && (\r\n                    <Step4Form />\r\n                )}\r\n            </div>\r\n\r\n            {/* Compact Navigation Footer */}\r\n            <div className=\"border-t border-gray-200 p-2 bg-white\">\r\n                <div className=\"flex justify-between items-center\">\r\n                    <button\r\n                        onClick={handlePrev}\r\n                        disabled={step === 1}\r\n                        className=\"flex items-center gap-1 px-2 py-1 text-xs text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                    >\r\n                        <ChevronLeft className=\"w-3 h-3\" />\r\n                        Quay lại\r\n                    </button>\r\n\r\n                    {step < 3 ? (\r\n                        <button\r\n                            onClick={handleNext}\r\n                            className=\"flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-xs\"\r\n                        >\r\n                            Tiếp theo\r\n                            <ChevronRight className=\"w-3 h-3\" />\r\n                        </button>\r\n                    ) : (\r\n                        <button\r\n                            onClick={handleSubmit}\r\n                            disabled={loading}\r\n                            className=\"flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50 text-xs\"\r\n                        >\r\n                            {loading ? (\r\n                                <>\r\n                                    <LoadingSpinner minHeight=\"min-h-0\" />\r\n                                    Đang tạo...\r\n                                </>\r\n                            ) : (\r\n                                <>\r\n                                    <Save className=\"w-3 h-3\" />\r\n                                    Tạo đề thi\r\n                                </>\r\n                            )}\r\n                        </button>\r\n                    )}\r\n                </div>\r\n            </div>\r\n        </div >\r\n    );\r\n};\r\n\r\nexport default LeftContent;"], "mappings": ";;;;;;;;;;AAAA;AACA,SAASA,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACIC,WAAW,EACXC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,YAAY,EACZC,WAAW,EACXC,oBAAoB,EACpBC,oBAAoB,EACpBC,qBAAqB,EACrBC,kBAAkB,EAClBC,kBAAkB,EAClBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,EAChBC,UAAU,EACVC,eAAe,EACfC,eAAe,QACZ,mCAAmC;AAC1C,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAEC,WAAW,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,cAAc;AACpL,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,WAAW,MAAM,kCAAkC;AAC1D,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,oBAAoB,QAAQ,kCAAkC;AACxG,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,OAAOC,QAAQ,MAAM,+BAA+B;AACpD,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,OAAOC,aAAa,MAAM,sBAAsB;AAChD,SAASC,kBAAkB,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAG3D,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4D,QAAQ;IAAEC,SAAS;IAAEC;EAAS,CAAC,GAAG7D,WAAW,CAAE8D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC/E,MAAM;IAAEC;EAAM,CAAC,GAAGhE,WAAW,CAAC8D,KAAK,IAAIA,KAAK,CAACE,KAAK,CAAC;EAEnD,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACrCT,QAAQ,CAACzD,WAAW,CAAC;MAAEiE,KAAK;MAAEC;IAAM,CAAC,CAAC,CAAC;EAC3C,CAAC;EAED,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EACtDD,SAAS,CAAC,MAAM;IACZ,IAAI0E,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MACjC,IAAIL,QAAQ,CAACa,KAAK,IAAIb,QAAQ,CAACa,KAAK,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAChDJ,gBAAgB,CACZL,KAAK,CAAC,SAAS,CAAC,CAACU,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,UAAU,CAACjB,QAAQ,CAACa,KAAK,CAAC,CAC1E,CAAC;MACL,CAAC,MAAM;QACHH,gBAAgB,CAACL,KAAK,CAAC,SAAS,CAAC,CAAC;MACtC;IACJ,CAAC,MAAM;MACHK,gBAAgB,CAAC,CAAC;IACtB;EACJ,CAAC,EAAE,CAACL,KAAK,EAAEL,QAAQ,CAACa,KAAK,CAAC,CAAC;EAE3B,oBACInB,OAAA;IAAKwB,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE1BzB,OAAA;MAAKwB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACnCzB,OAAA;QAAAyB,QAAA,gBACIzB,OAAA;UAAOwB,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,0BACjD,eAAAzB,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACR7B,OAAA;UACI8B,IAAI,EAAC,MAAM;UACXhB,KAAK,EAAER,QAAQ,CAACyB,IAAI,IAAI,EAAG;UAC3BC,QAAQ,EAAGC,CAAC,IAAKrB,cAAc,CAAC,MAAM,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;UACxDU,SAAS,EAAC,kHAAkH;UAC5HW,WAAW,EAAC;QAAiB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN7B,OAAA;QAAAyB,QAAA,gBACIzB,OAAA;UAAOwB,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,yBACpD,eAAAzB,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACR7B,OAAA,CAACnC,gBAAgB;UACbuE,cAAc,EAAE9B,QAAQ,CAAC+B,UAAW;UACpCL,QAAQ,EAAGM,MAAM,IAAK1B,cAAc,CAAC,YAAY,EAAE0B,MAAM,CAAE;UAC3DC,OAAO,EAAEtB,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,WAAW,CAAC,CAAC,GAAGA,KAAK,CAAC,WAAW,CAAC,GAAG,EAAG;UACrEa,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACnCzB,OAAA;QAAAyB,QAAA,gBACIzB,OAAA;UAAOwB,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,WACxD,eAAAzB,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACR7B,OAAA,CAACnC,gBAAgB;UACbuE,cAAc,EAAE9B,QAAQ,CAACa,KAAM;UAC/Ba,QAAQ,EAAGM,MAAM,IAAK1B,cAAc,CAAC,OAAO,EAAE0B,MAAM,CAAE;UACtDC,OAAO,EAAEtB,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;UAC7Da,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN7B,OAAA;QAAAyB,QAAA,gBACIzB,OAAA;UAAOwB,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,WACxD,eAAAzB,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACR7B,OAAA,CAACnC,gBAAgB;UACbuE,cAAc,EAAE9B,QAAQ,CAACkC,IAAK;UAC9BR,QAAQ,EAAGM,MAAM,IAAK1B,cAAc,CAAC,MAAM,EAAE0B,MAAM,CAAE;UACrDC,OAAO,EAAEtB,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,MAAM,CAAC,CAAC,GAAGA,KAAK,CAAC,MAAM,CAAC,GAAG,EAAG;UAC3Da,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACnCzB,OAAA;QAAAyB,QAAA,gBACIzB,OAAA;UAAOwB,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DzB,OAAA,CAAChC,KAAK;YAACwD,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7B,OAAA;UACI8B,IAAI,EAAC,QAAQ;UACbhB,KAAK,EAAER,QAAQ,CAACmC,YAAY,IAAI,EAAG;UACnCT,QAAQ,EAAGC,CAAC,IAAKrB,cAAc,CAAC,cAAc,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;UAChEU,SAAS,EAAC,kHAAkH;UAC5HW,WAAW,EAAC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN7B,OAAA;QAAAyB,QAAA,gBACIzB,OAAA;UAAOwB,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DzB,OAAA,CAAC/B,KAAK;YAACuD,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oCAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7B,OAAA;UACI8B,IAAI,EAAC,QAAQ;UACbhB,KAAK,EAAER,QAAQ,CAACoC,QAAQ,IAAI,EAAG;UAC/BV,QAAQ,EAAGC,CAAC,IAAKrB,cAAc,CAAC,UAAU,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;UAC5DU,SAAS,EAAC,kHAAkH;UAC5HW,WAAW,EAAC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGLvB,QAAQ,CAAC+B,UAAU,KAAK,IAAI,iBACzBrC,OAAA;MAAAyB,QAAA,gBACIzB,OAAA;QAAOwB,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3DzB,OAAA,CAAC9B,QAAQ;UAACsD,SAAS,EAAC;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAEhD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR7B,OAAA,CAAClC,oBAAoB;QACjBsE,cAAc,EAAE9B,QAAQ,CAACqC,OAAQ;QACjCX,QAAQ,EAAGM,MAAM,IAAK1B,cAAc,CAAC,SAAS,EAAE0B,MAAM,CAAE;QACxDC,OAAO,EAAExB,aAAc;QACvBS,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eACD7B,OAAA;MAAAyB,QAAA,gBACIzB,OAAA;QAAOwB,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrF7B,OAAA;QACIc,KAAK,EAAER,QAAQ,CAACsC,WAAY;QAC5BZ,QAAQ,EAAGC,CAAC,IAAKrB,cAAc,CAAC,aAAa,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;QAC/DU,SAAS,EAAC,kHAAkH;QAC5HW,WAAW,EAAC;MAAqC;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEN7B,OAAA;MAAAyB,QAAA,gBACIzB,OAAA;QAAOwB,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC7E7B,OAAA;QACIc,KAAK,EAAER,QAAQ,CAACuC,WAAW,IAAI,EAAG;QAClCb,QAAQ,EAAGC,CAAC,IAAKrB,cAAc,CAAC,aAAa,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;QAC/DgC,IAAI,EAAE,CAAE;QACRtB,SAAS,EAAC,kHAAkH;QAC5HW,WAAW,EAAC;MAAyB;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACpCzB,OAAA;QAAOwB,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxCzB,OAAA;UACI8B,IAAI,EAAC,UAAU;UACfiB,OAAO,EAAEzC,QAAQ,CAAC0C,MAAM,IAAI,KAAM;UAClChB,QAAQ,EAAGC,CAAC,IAAKrB,cAAc,CAAC,QAAQ,EAAEqB,CAAC,CAACC,MAAM,CAACa,OAAO,CAAE;UAC5DvB,SAAS,EAAC;QAAiE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACF7B,OAAA;UAAMwB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eACR7B,OAAA;QAAOwB,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxCzB,OAAA;UACI8B,IAAI,EAAC,UAAU;UACfiB,OAAO,EAAEzC,QAAQ,CAAC2C,eAAe,IAAI,KAAM;UAC3CjB,QAAQ,EAAGC,CAAC,IAAKrB,cAAc,CAAC,iBAAiB,EAAEqB,CAAC,CAACC,MAAM,CAACa,OAAO,CAAE;UACrEvB,SAAS,EAAC;QAAiE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACF7B,OAAA;UAAMwB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAChCzB,OAAA;QAAAyB,QAAA,gBACIzB,OAAA;UAAOwB,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DzB,OAAA,CAAC5B,SAAS;YAACoD,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BAEjD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7B,OAAA,CAACf,WAAW;UACRiE,KAAK,EAAE3C,SAAU;UACjB4C,QAAQ,EAAGC,GAAG,IAAK/C,QAAQ,CAACrD,YAAY,CAACoG,GAAG,CAAC,CAAE;UAC/CC,OAAO,EAAC,oBAAoB;UAC5BC,OAAO,EAAE;QAAK;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN7B,OAAA;QAAAyB,QAAA,gBACIzB,OAAA;UAAOwB,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DzB,OAAA,CAAC3B,MAAM;YAACmD,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7B,OAAA,CAACd,SAAS;UACNqE,MAAM,EAAGC,GAAG,IAAKnD,QAAQ,CAACpD,WAAW,CAACuG,GAAG,CAAC,CAAE;UAC5CC,YAAY,EAAE,KAAM;UACpBH,OAAO,EAAE;QAAK;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAzB,EAAA,CAnMKD,SAAS;EAAA,QACMzD,WAAW,EACcC,WAAW,EACnCA,WAAW;AAAA;AAAA+G,EAAA,GAH3BvD,SAAS;AAqMf,MAAMwD,iBAAiB,GAAGC,IAAA,IAAuB;EAAA,IAAtB;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAAF,IAAA;EACxC,oBACI5D,OAAA;IACI8D,OAAO,EAAEA,OAAQ;IACjBtC,SAAS,EAAC,wGAAwG;IAAAC,QAAA,gBAClHzB,OAAA,CAACrB,IAAI;MAAC6C,SAAS,EAAC;IAAqB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACvCgC,IAAI;EAAA;IAAAnC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEjB,CAAC;AAAAkC,GAAA,GATKJ,iBAAiB;AAavB,MAAMK,eAAe,GAAGC,KAAA,IAAwH;EAAA,IAAvH;IAAEC,eAAe;IAAEC,oBAAoB;IAAEC,mBAAmB;IAAEC,yBAAyB;IAAEC,UAAU;IAAEC;EAAY,CAAC,GAAAN,KAAA;EACvI,oBACIjE,OAAA;IAAKwB,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACpCzB,OAAA,CAACN,QAAQ;MACLoB,KAAK,EAAEqD,oBAAqB;MAC5BnC,QAAQ,EAAEqC,yBAA0B;MACpClC,WAAW,EAAC,6BAAa;MACzBqC,KAAK,EAAC,mBAAQ;MACdC,IAAI,EAAEjG,WAAY;MAClBkG,IAAI,EAAEJ;IAAW;MAAA5C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eACF7B,OAAA,CAACN,QAAQ;MACLoB,KAAK,EAAEoD,eAAgB;MACvBlC,QAAQ,EAAEoC,mBAAoB;MAC9BjC,WAAW,EAAC,yCAAuB;MACnCqC,KAAK,EAAC,iBAAS;MACfC,IAAI,EAAE9F,IAAK;MACX+F,IAAI,EAAEH,WAAY;MAClBI,gBAAgB,EAAE;QACdd,IAAI,EAAE,KAAK;QACXC,OAAO,EAAEA,CAAA,KAAM;UACXM,mBAAmB,CAAC;YAChBlC,MAAM,EAAE;cACJpB,KAAK,EAAExB,aAAa,CAAC4E,eAAe;YACxC;UACJ,CAAC,CAAC;QACN;MACJ;IAAE;MAAAxC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAA+C,GAAA,GA/BKZ,eAAe;AAkCrB,MAAMa,aAAa,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxB,MAAMzE,QAAQ,GAAG3D,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqI,iBAAiB;IAAEC;EAAgB,CAAC,GAAGrI,WAAW,CAAE8D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAEpF,MAAM0D,mBAAmB,GAAInC,CAAC,IAAK;IAC/B5B,QAAQ,CAACnD,oBAAoB,CAAC+E,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAC,CAAC;EAClD,CAAC;EAED,MAAMuD,yBAAyB,GAAIpC,CAAC,IAAK;IACrC5B,QAAQ,CAAChD,kBAAkB,CAAC4E,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAC,CAAC;EAChD,CAAC;EAED,oBACId,OAAA,CAACgE,eAAe;IACZE,eAAe,EAAEa,iBAAkB;IACnCZ,oBAAoB,EAAEa,eAAgB;IACtCZ,mBAAmB,EAAEA,mBAAoB;IACzCC,yBAAyB,EAAEA,yBAA0B;IACrDC,UAAU,EAAC,sDAAiC;IAC5CC,WAAW,EAAC;EAA4D;IAAA7C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3E,CAAC;AAEV,CAAC;AAAAiD,GAAA,CAtBKD,aAAa;EAAA,QACEnI,WAAW,EACmBC,WAAW;AAAA;AAAAsI,GAAA,GAFxDJ,aAAa;AAwBnB,MAAMK,aAAa,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxB,MAAM9E,QAAQ,GAAG3D,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE0I,iBAAiB;IAAEC;EAAgB,CAAC,GAAG1I,WAAW,CAAE8D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAEpF,MAAM0D,mBAAmB,GAAInC,CAAC,IAAK;IAC/B5B,QAAQ,CAAClD,oBAAoB,CAAC8E,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAC,CAAC;EAClD,CAAC;EAED,MAAMuD,yBAAyB,GAAIpC,CAAC,IAAK;IACrC5B,QAAQ,CAAC/C,kBAAkB,CAAC2E,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAC,CAAC;EAChD,CAAC;EAED,oBACId,OAAA,CAACgE,eAAe;IACZE,eAAe,EAAEkB,iBAAkB;IACnCjB,oBAAoB,EAAEkB,eAAgB;IACtCjB,mBAAmB,EAAEA,mBAAoB;IACzCC,yBAAyB,EAAEA,yBAA0B;IACrDC,UAAU,EAAC,kEAAqC;IAChDC,WAAW,EAAC;EAAwD;IAAA7C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvE,CAAC;AAEV,CAAC;AAAAsD,GAAA,CAtBKD,aAAa;EAAA,QACExI,WAAW,EACmBC,WAAW;AAAA;AAAA2I,GAAA,GAFxDJ,aAAa;AAwBnB,MAAMK,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAMnF,QAAQ,GAAG3D,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE+I,kBAAkB;IAAEC;EAAiB,CAAC,GAAG/I,WAAW,CAAE8D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAEtF,MAAM0D,mBAAmB,GAAInC,CAAC,IAAK;IAC/B5B,QAAQ,CAACjD,qBAAqB,CAAC6E,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAC,CAAC;EACnD,CAAC;EAED,MAAMuD,yBAAyB,GAAIpC,CAAC,IAAK;IACrC5B,QAAQ,CAAC9C,mBAAmB,CAAC0E,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAC,CAAC;EACjD,CAAC;EAED,oBACId,OAAA,CAACgE,eAAe;IACZE,eAAe,EAAEuB,kBAAmB;IACpCtB,oBAAoB,EAAEuB,gBAAiB;IACvCtB,mBAAmB,EAAEA,mBAAoB;IACzCC,yBAAyB,EAAEA,yBAA0B;IACrDC,UAAU,EAAC,6DAAmC;IAC9CC,WAAW,EAAC;EAAwD;IAAA7C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvE,CAAC;AAEV,CAAC;AAAA2D,GAAA,CAtBKD,cAAc;EAAA,QACC7I,WAAW,EACqBC,WAAW;AAAA;AAAAgJ,GAAA,GAF1DJ,cAAc;AAwBpB,MAAMK,mBAAmB,GAAIC,IAAI,IAAK;EAClC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACpC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,SAAS,GAAG,MAAMJ,OAAO,CAACE,MAAM,CAACG,MAAM,CAAC;IAC/CH,MAAM,CAACI,OAAO,GAAGL,MAAM;IACvBC,MAAM,CAACK,aAAa,CAACT,IAAI,CAAC;EAC9B,CAAC,CAAC;AACN,CAAC;AAGD,MAAMU,SAAS,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpB,MAAMnG,QAAQ,GAAG3D,WAAW,CAAC,CAAC;EAC9B,MAAM+J,YAAY,GAAGhK,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM;IAAEiK,OAAO;IAAEC,YAAY;IAAEC,UAAU;IAAEC;EAAa,CAAC,GAAGlK,WAAW,CAAE8D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EACjG,MAAM;IAAEoG;EAAoB,CAAC,GAAGnK,WAAW,CAAE8D,KAAK,IAAKA,KAAK,CAACsG,MAAM,CAAC;EAEpE,MAAMC,gBAAgB,GAAInB,IAAI,IAAKxF,QAAQ,CAAC3C,UAAU,CAACmI,IAAI,CAAC,CAAC;EAE7D,MAAMoB,eAAe,GAAG,MAAOC,KAAK,IAAK;IACrC,MAAMC,KAAK,GAAGD,KAAK,CAAChF,MAAM,CAACiF,KAAK;IAChC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IAElC,MAAMC,UAAU,GAAG,MAAMvB,OAAO,CAACwB,GAAG,CAChCrG,KAAK,CAACsG,IAAI,CAACJ,KAAK,CAAC,CAACK,GAAG,CAAC3B,IAAI,IAAID,mBAAmB,CAACC,IAAI,CAAC,CAC3D,CAAC;IACDxF,QAAQ,CAACzC,eAAe,CAAC,CAAC,GAAGiJ,YAAY,EAAE,GAAGQ,UAAU,CAAC,CAAC,CAAC;EAC/D,CAAC;EAED,MAAMI,SAAS,GAAGA,CAAA,KAAMpH,QAAQ,CAACT,iBAAiB,CAAC8G,OAAO,CAAC,CAAC;EAE5D,MAAMtC,mBAAmB,GAAInC,CAAC,IAAK5B,QAAQ,CAAC1C,eAAe,CAACsE,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAC,CAAC;EAE5E,MAAM4G,iBAAiB,GAAIC,aAAa,IAAK;IACzC,MAAMC,SAAS,GAAGf,YAAY,CAACxF,MAAM,CAAC,CAACwG,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKH,aAAa,CAAC;IACpEtH,QAAQ,CAACzC,eAAe,CAACgK,SAAS,CAAC,CAAC;EACxC,CAAC;EAED,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC7B1H,QAAQ,CAACP,kBAAkB,CAAC;MAAEiH,MAAM,EAAEF,YAAY;MAAEmB,MAAM,EAAE;IAAgB,CAAC,CAAC,CAAC;EACnF,CAAC;EAED,oBACIhI,OAAA;IAAKwB,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBAEnCzB,OAAA;MAAKwB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACtBzB,OAAA;QAAKwB,SAAS,EAAC,qGAAqG;QAAAC,QAAA,gBAChHzB,OAAA;UAAGwB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAAqE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpH7B,OAAA;UAAIwB,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACpCzB,OAAA;YAAAyB,QAAA,GAAI,gBAAS,eAAAzB,OAAA;cAAMwB,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClG7B,OAAA;YAAAyB,QAAA,GAAI,iBAAO,eAAAzB,OAAA;cAAMwB,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,8IAAsE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvJ7B,OAAA;YAAAyB,QAAA,GAAI,2LAAyF,eAAAzB,OAAA;cAAMwB,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,4CAAqB,eAAA7B,OAAA;cAAMwB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/O7B,OAAA;YAAAyB,QAAA,GAAI,wLAAsF,eAAAzB,OAAA;cAAMwB,SAAS,EAAC,QAAQ;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,MAAE,eAAA7B,OAAA;cAAMwB,SAAS,EAAC,QAAQ;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,MAAE,eAAA7B,OAAA;cAAMwB,SAAS,EAAC,QAAQ;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,yCAAgB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9O7B,OAAA;YAAAyB,QAAA,EAAI;UAAuF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7B,OAAA,CAACd,SAAS;QAACqE,MAAM,EAAEyD,gBAAiB;QAACvD,YAAY,EAAE,KAAM;QAACH,OAAO,EAAE;MAAK;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE3E7B,OAAA;QACI8D,OAAO,EAAE2D,SAAU;QACnBQ,QAAQ,EAAErB,UAAU,IAAI,CAACF,OAAQ;QACjClF,SAAS,EAAC,uJAAuJ;QAAAC,QAAA,EAEhKmF,UAAU,gBACP5G,OAAA,CAAAE,SAAA;UAAAuB,QAAA,gBACIzB,OAAA;YAAKwB,SAAS,EAAC;UAA2D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,8BAErF;QAAA,eAAE,CAAC,gBAEH7B,OAAA,CAAAE,SAAA;UAAAuB,QAAA,gBACIzB,OAAA,CAAC1B,QAAQ;YAACkD,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kCAEpC;QAAA,eAAE;MACL;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAGLgF,YAAY,CAACO,MAAM,GAAG,CAAC,iBACpBpH,OAAA;MAAKwB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACtBzB,OAAA;QAAKwB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAC9CzB,OAAA;UAAMwB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,GAAC,0BAAc,EAACoF,YAAY,CAACO,MAAM,EAAC,GAAC;QAAA;UAAA1F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/F7B,OAAA;UACI8D,OAAO,EAAEiE,kBAAmB;UAC5BE,QAAQ,EAAEnB,mBAAoB;UAC9BtF,SAAS,EAAC,oHAAoH;UAAAC,QAAA,EAE7HqF,mBAAmB,gBAChB9G,OAAA,CAAAE,SAAA;YAAAuB,QAAA,gBACIzB,OAAA;cAAKwB,SAAS,EAAC;YAA2D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,yBAErF;UAAA,eAAE,CAAC,gBAEH7B,OAAA,CAAAE,SAAA;YAAAuB,QAAA,gBACIzB,OAAA,CAACzB,WAAW;cAACiD,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAEvC;UAAA,eAAE;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEN7B,OAAA;QAAKwB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjCzB,OAAA;UACI8B,IAAI,EAAC,MAAM;UACXoG,QAAQ;UACRC,MAAM,EAAC,SAAS;UAChBC,GAAG,EAAE3B,YAAa;UAClB4B,KAAK,EAAE;YAAEC,OAAO,EAAE;UAAO,CAAE;UAC3BtG,QAAQ,EAAEiF;QAAgB;UAAAvF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACF7B,OAAA;UACI8D,OAAO,EAAEA,CAAA,KAAM2C,YAAY,CAAC8B,OAAO,CAACC,KAAK,CAAC,CAAE;UAC5ChH,SAAS,EAAC,sHAAsH;UAAAC,QAAA,EACnI;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERgF,YAAY,CAACW,GAAG,CAAC,CAACtE,KAAK,EAAEuF,KAAK,kBAC3BzI,OAAA;UAAiBwB,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACnFzB,OAAA;YACI8D,OAAO,EAAEA,CAAA,KAAM4D,iBAAiB,CAACe,KAAK,CAAE;YACxCjH,SAAS,EAAC,yJAAyJ;YACnKkH,KAAK,EAAC,iBAAS;YAAAjH,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7B,OAAA;YACI2I,GAAG,EAAEzF,KAAM;YACX0F,GAAG,WAAAC,MAAA,CAAWJ,KAAK,CAAG;YACtBjH,SAAS,EAAC;UAA8B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA,GAZI4G,KAAK;UAAA/G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaV,CACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAGA8E,YAAY,iBACT3G,OAAA,CAACN,QAAQ;MACLoB,KAAK,EAAE6F,YAAa;MACpB3E,QAAQ,EAAEoC,mBAAoB;MAC9BjC,WAAW,EAAC,+CAAgC;MAC5CqC,KAAK,EAAC;IAAiB;MAAA9C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC2E,GAAA,CAzIID,SAAS;EAAA,QACM7J,WAAW,EAEgCC,WAAW,EACvCA,WAAW;AAAA;AAAAmM,GAAA,GAJzCvC,SAAS;AA2If,MAAMwC,SAAS,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1M,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2M,IAAI,EAAEC,OAAO,CAAC,GAAG5M,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM;IAAEmK,YAAY;IAAE5B,iBAAiB;IAAEK,iBAAiB;IAAEK,kBAAkB;IAAET,eAAe;IAAEK,eAAe;IAAEK;EAAiB,CAAC,GAAG/I,WAAW,CAAE8D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC5K,MAAML,QAAQ,GAAG3D,WAAW,CAAC,CAAC;EAC9BH,SAAS,CAAC,MAAM;IACZ,IAAI,CAAC0M,SAAS,EAAE;IAChB,IAAIlE,iBAAiB,CAAC3D,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI4D,eAAe,CAAC5D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAClE8H,YAAY,CAAC,KAAK,CAAC;MACnBE,OAAO,CAAC,IAAI,CAAC;IACjB,CAAC,MAAM,IAAIhE,iBAAiB,CAAChE,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIiE,eAAe,CAACjE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACzE8H,YAAY,CAAC,KAAK,CAAC;MACnBE,OAAO,CAAC,IAAI,CAAC;IACjB,CAAC,MAAM,IAAI3D,kBAAkB,CAACrE,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIsE,gBAAgB,CAACtE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC3E8H,YAAY,CAAC,KAAK,CAAC;MACnBE,OAAO,CAAC,KAAK,CAAC;IAClB;EACJ,CAAC,EAAE,CAACrE,iBAAiB,EAAEC,eAAe,EAAEI,iBAAiB,EAAEC,eAAe,EAAEI,kBAAkB,EAAEC,gBAAgB,CAAC,CAAC;EAElH,oBACI1F,OAAA;IAAKwB,SAAS,EAAC,WAAW;IAAAC,QAAA,GACrBwH,SAAS,gBACNjJ,OAAA;MAAKwB,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAClCzB,OAAA,CAAC1B,QAAQ;QAACkD,SAAS,EAAC;MAAoC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3D7B,OAAA;QAAIwB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxE7B,OAAA;QAAGwB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ7B,OAAA;QAAKwB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtBzB,OAAA,CAAC2D,iBAAiB;UACdE,IAAI,EAAC,sCAAsB;UAC3BC,OAAO,EAAEA,CAAA,KAAM;YACXoF,YAAY,CAAC,KAAK,CAAC;YACnBE,OAAO,CAAC,IAAI,CAAC;UACjB;QAAE;UAAA1H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACF7B,OAAA,CAAC2D,iBAAiB;UACdE,IAAI,EAAC,iCAAmB;UACxBC,OAAO,EAAEA,CAAA,KAAM;YACXoF,YAAY,CAAC,KAAK,CAAC;YACnBE,OAAO,CAAC,IAAI,CAAC;UACjB;QAAE;UAAA1H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACF7B,OAAA,CAAC2D,iBAAiB;UACdE,IAAI,EAAC,4CAAuB;UAC5BC,OAAO,EAAEA,CAAA,KAAM;YACXoF,YAAY,CAAC,KAAK,CAAC;YACnBE,OAAO,CAAC,KAAK,CAAC;UAClB;QAAE;UAAA1H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAEN7B,OAAA,CAACZ,WAAW;MACRiK,IAAI,EAAE,CAAC;QACHC,EAAE,EAAE,CAAC;QACLvH,IAAI,EAAE,aAAa;QACnBjB,KAAK,EAAE;MACX,CAAC,EACD;QACIwI,EAAE,EAAE,CAAC;QACLvH,IAAI,EAAE,UAAU;QAChBjB,KAAK,EAAE;MACX,CAAC,EACD;QACIwI,EAAE,EAAE,CAAC;QACLvH,IAAI,EAAE,cAAc;QACpBjB,KAAK,EAAE;MACX,CAAC,CACC;MACFyI,MAAM,EAAEJ,IAAK;MACbK,SAAS,EAAEJ;IAAQ;MAAA1H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CACJ,EAEAsH,IAAI,KAAK,IAAI,IAAI,CAACF,SAAS,iBACxBjJ,OAAA,CAAC6E,aAAa;MAAAnD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACnB,EACAsH,IAAI,KAAK,IAAI,IAAI,CAACF,SAAS,iBACxBjJ,OAAA,CAACkF,aAAa;MAAAxD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACnB,EACAsH,IAAI,KAAK,KAAK,IAAI,CAACF,SAAS,iBACzBjJ,OAAA,CAACuF,cAAc;MAAA7D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACpB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAmH,GAAA,CAtFKD,SAAS;EAAA,QAG4HpM,WAAW,EACjID,WAAW;AAAA;AAAA+M,GAAA,GAJ1BV,SAAS;AAwFf,MAAMW,aAAa,GAAGC,KAAA,IAAkC;EAAA,IAAjC;IAAEC,KAAK;IAAElB,KAAK;IAAE5E,OAAO;IAAEgE;EAAE,CAAC,GAAA6B,KAAA;EAC/C,oBACI3J,OAAA;IAAKwB,SAAS,EAAC,iFAAiF;IAAAC,QAAA,gBAC5FzB,OAAA;MAAKwB,SAAS,EAAC,mHAAmH;MAAAC,QAAA,GAC7HiH,KAAK,EAAC,GACX;IAAA;MAAAhH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACN7B,OAAA;MAAKwB,SAAS,EAAC,uDAAuD;MAAAC,QAAA,EACjER,KAAK,CAACsG,IAAI,CAAC;QAAEH,MAAM,EAAEwC;MAAM,CAAC,CAAC,CAACpC,GAAG,CAAC,CAACK,CAAC,EAAEY,KAAK,kBACxCzI,OAAA;QAEI8D,OAAO,EAAEA,CAAA,KAAMA,OAAO,CAAC2E,KAAK,CAAE;QAC9BjH,SAAS,uHAAAqH,MAAA,CAAuHf,CAAC,KAAKW,KAAK,GAAG,yBAAyB,GAAG,yBAAyB,eAAa;QAAAhH,QAAA,GAAC,kBACzM,EAACgH,KAAK,GAAG,CAAC;MAAA,GAHbA,KAAK,GAAGC,KAAK;QAAAhH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIjB,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;;AAED;AAAAgI,GAAA,GApBMH,aAAa;AAqBnB,MAAMI,cAAc,GAAGC,KAAA,IAAgE;EAAAC,GAAA;EAAA,IAA/D;IAAElJ,KAAK;IAAEkB,QAAQ;IAAEG,WAAW;IAAEqC,KAAK;IAAEC,IAAI;IAAEwF,GAAG,GAAG;EAAM,CAAC,GAAAF,KAAA;EAC9E,MAAM1J,QAAQ,GAAG3D,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEwN;EAAQ,CAAC,GAAGvN,WAAW,CAAE8D,KAAK,IAAKA,KAAK,CAAC0J,EAAE,CAAC;EAEpD,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAACtJ,KAAK,IAAI,CAACA,KAAK,CAACM,IAAI,CAAC,CAAC,EAAE;MACzBiJ,KAAK,CAAC,qDAAqD,CAAC;MAC5D;IACJ;IAEA,IAAI;MACA,MAAMjE,MAAM,GAAG,MAAM/F,QAAQ,CAACrB,eAAe,CAAC8B,KAAK,CAAC,CAAC,CAACwJ,MAAM,CAAC,CAAC;MAC9D,IAAIlE,MAAM,CAACmE,UAAU,EAAE;QACnBvI,QAAQ,CAAC;UAAEE,MAAM,EAAE;YAAEpB,KAAK,EAAEsF,MAAM,CAACoE;UAAU;QAAE,CAAC,CAAC;MACrD,CAAC,MAAM;QACHH,KAAK,CAAC,gCAAgC,CAAC;MAC3C;IACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CJ,KAAK,CAAC,sCAAsC,CAAC;IACjD;EACJ,CAAC;EAED,oBACIrK,OAAA;IAAKwB,SAAS,gBAAAqH,MAAA,CAAgBoB,GAAG,GAAG,kBAAkB,GAAG,UAAU,CAAG;IAAAxI,QAAA,gBAClEzB,OAAA;MAAKwB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAC9CzB,OAAA;QAAOwB,SAAS,EAAC,2DAA2D;QAAAC,QAAA,GACvEgD,IAAI,iBAAIzE,OAAA,CAACyE,IAAI;UAACjD,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACpC2C,KAAK;MAAA;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACR7B,OAAA;QACI8D,OAAO,EAAEsG,WAAY;QACrBnC,QAAQ,EAAEiC,OAAO,IAAI,EAACpJ,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEM,IAAI,CAAC,CAAC,CAAC;QACpCI,SAAS,EAAC,kKAAkK;QAC5KkH,KAAK,EAAC,yFAA6C;QAAAjH,QAAA,eAEnDzB,OAAA,CAACjB,QAAQ;UAACyC,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eACN7B,OAAA,CAACN,QAAQ;MACLoB,KAAK,EAAEA,KAAM;MACbkB,QAAQ,EAAEA,QAAS;MACnBG,WAAW,EAAEA;IAAY;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACmI,GAAA,CA9CIF,cAAc;EAAA,QACCpN,WAAW,EACRC,WAAW;AAAA;AAAAgO,IAAA,GAF7Bb,cAAc;AAgDpB,MAAMc,SAAS,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpB,MAAM;IAAEC,SAAS;IAAEC,aAAa;IAAE5B;EAAK,CAAC,GAAGxM,WAAW,CAAE8D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAChF,MAAML,QAAQ,GAAG3D,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsO,aAAa,EAAEC,gBAAgB,CAAC,GAAGzO,QAAQ,CAAC;IAC/C0O,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,GAAG,EAAE;EACT,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAM;IAAE3K;EAAM,CAAC,GAAGhE,WAAW,CAAE8D,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EACrD,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAEtDD,SAAS,CAAC,MAAM;IACZ,IAAIuO,SAAS,EAAE;MACX,MAAMS,MAAM,GAAGT,SAAS,CAACU,MAAM,CAC3B,CAACC,GAAG,EAAEC,CAAC,KAAK;QACR,MAAM5J,IAAI,GAAG4J,CAAC,CAACC,YAAY,CAACC,cAAc;QAC1C,IAAIH,GAAG,CAAC3J,IAAI,CAAC,KAAK+J,SAAS,EAAEJ,GAAG,CAAC3J,IAAI,CAAC,EAAE;QACxC,OAAO2J,GAAG;MACd,CAAC,EACD;QAAEP,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAC3B,CAAC;MAEDH,gBAAgB,CAACM,MAAM,CAAC;IAC5B;EACJ,CAAC,EAAE,CAACT,SAAS,CAAC,CAAC;EAGfvO,SAAS,CAAC,MAAM;IACZ,IAAI0E,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MAAA,IAAAmL,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACjC,IAAI,CAAAH,qBAAA,GAAAhB,SAAS,CAACC,aAAa,CAAC,cAAAe,qBAAA,gBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BH,YAAY,cAAAI,sBAAA,eAAtCA,sBAAA,CAAwC5K,KAAK,IAAI,EAAA6K,sBAAA,GAAAlB,SAAS,CAACC,aAAa,CAAC,cAAAiB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BL,YAAY,cAAAM,sBAAA,uBAAtCA,sBAAA,CAAwC9K,KAAK,CAACC,IAAI,CAAC,CAAC,MAAK,EAAE,EAAE;QAC9GJ,gBAAgB,CACZL,KAAK,CAAC,SAAS,CAAC,CAACU,MAAM,CAAEC,IAAI;UAAA,IAAA4K,sBAAA,EAAAC,sBAAA;UAAA,OAAK7K,IAAI,CAACA,IAAI,CAACC,UAAU,EAAA2K,sBAAA,GAACpB,SAAS,CAACC,aAAa,CAAC,cAAAmB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BP,YAAY,cAAAQ,sBAAA,uBAAtCA,sBAAA,CAAwChL,KAAK,CAAC,IAAIG,IAAI,CAACA,IAAI,CAAC8F,MAAM,KAAK,CAAC;QAAA,EACnI,CAAC;MACL,CAAC,MAAM;QACHpG,gBAAgB,CAACL,KAAK,CAAC,SAAS,CAAC,CAACU,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAAC8F,MAAM,KAAK,CAAC,CAAC,CAAC;MAC/E;IACJ,CAAC,MAAM;MACHpG,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAACL,KAAK,EAAEmK,SAAS,EAAEC,aAAa,CAAC,CAAC;EAErC,MAAMqB,oBAAoB,GAAGA,CAACnK,CAAC,EAAEpB,KAAK,KAAK;IACvC,MAAMwL,YAAY,GAAGvB,SAAS,CAACtD,GAAG,CAAC,CAAC8E,QAAQ,EAAEC,MAAM,KAAK;MACrD,IAAIA,MAAM,KAAKxB,aAAa,EAAE;QAC1B,OAAO;UACH,GAAGuB,QAAQ;UACXX,YAAY,EAAE;YACV,GAAGW,QAAQ,CAACX,YAAY;YACxB,CAAC9K,KAAK,GAAGoB,CAAC,CAACC,MAAM,CAACpB;UACtB;QACJ,CAAC;MACL;MACA,OAAOwL,QAAQ;IACnB,CAAC,CAAC;IACFjM,QAAQ,CAAC7C,YAAY,CAAC6O,YAAY,CAAC,CAAC;EACxC,CAAC;EACD,MAAMG,4BAA4B,GAAIC,WAAW,IAAK;IAClD,MAAMJ,YAAY,GAAGvB,SAAS,CAACtD,GAAG,CAAC,CAAC8E,QAAQ,EAAEC,MAAM,KAAK;MACrD,IAAIA,MAAM,KAAKxB,aAAa,EAAE;QAC1B,OAAO;UACH,GAAGuB,QAAQ;UACXX,YAAY,EAAE;YACV,GAAGW,QAAQ,CAACX,YAAY;YACxBe,QAAQ,EAAED;UACd;QACJ,CAAC;MACL;MACA,OAAOH,QAAQ;IACnB,CAAC,CAAC;IACFjM,QAAQ,CAAC7C,YAAY,CAAC6O,YAAY,CAAC,CAAC;EACxC,CAAC;EAED,MAAMM,qBAAqB,GAAGA,CAAClE,KAAK,EAAE3H,KAAK,EAAED,KAAK,KAAK;IACnD,MAAMwL,YAAY,GAAGvB,SAAS,CAACtD,GAAG,CAAC,CAAC8E,QAAQ,EAAEC,MAAM,KAAK;MACrD,IAAIA,MAAM,KAAKxB,aAAa,EAAE;QAC1B,OAAO;UACH,GAAGuB,QAAQ;UACXM,UAAU,EAAEN,QAAQ,CAACM,UAAU,CAACpF,GAAG,CAAC,CAACqF,IAAI,EAAEC,MAAM,KAAK;YAClD,IAAIA,MAAM,KAAKrE,KAAK,EAAE;cAClB,OAAO;gBAAE,GAAGoE,IAAI;gBAAE,CAAChM,KAAK,GAAGC;cAAM,CAAC;YACtC;YACA,OAAO+L,IAAI;UACf,CAAC;QACL,CAAC;MACL;MACA,OAAOP,QAAQ;IACnB,CAAC,CAAC;IACFjM,QAAQ,CAAC7C,YAAY,CAAC6O,YAAY,CAAC,CAAC;EACxC,CAAC;EAGD,oBACIrM,OAAA;IAAKwB,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACjCzB,OAAA;MAAIwB,SAAS,EAAC,6BAA6B;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEjEiJ,SAAS,IAAIA,SAAS,CAACC,aAAa,CAAC,iBAClC/K,OAAA;MAAKwB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BzB,OAAA;QAAKwB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCzB,OAAA;UAAIwB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChE7B,OAAA;UAAKwB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAChCzB,OAAA,CAACnC,gBAAgB;YACbuE,cAAc,EAAE0I,SAAS,CAACC,aAAa,CAAC,CAACY,YAAY,CAACxK,KAAM;YAC5Da,QAAQ,EAAGM,MAAM,IAAK8J,oBAAoB,CAAC;cAAElK,MAAM,EAAE;gBAAEpB,KAAK,EAAEwB;cAAO;YAAE,CAAC,EAAE,OAAO,CAAE;YACnFC,OAAO,EAAEtB,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;YAC7Da,SAAS,EAAC,SAAS;YACnBW,WAAW,EAAC;UAAU;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACF7B,OAAA,CAAClC,oBAAoB;YACjBsE,cAAc,EAAE0I,SAAS,CAACC,aAAa,CAAC,CAACY,YAAY,CAAChJ,OAAQ;YAC9DX,QAAQ,EAAGM,MAAM,IAAK8J,oBAAoB,CAAC;cAAElK,MAAM,EAAE;gBAAEpB,KAAK,EAAEwB;cAAO;YAAE,CAAC,EAAE,SAAS,CAAE;YACrFC,OAAO,EAAExB,aAAc;YACvBS,SAAS,EAAC,SAAS;YACnBW,WAAW,EAAC;UAAa;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACF7B,OAAA,CAACnC,gBAAgB;YACbuE,cAAc,EAAE0I,SAAS,CAACC,aAAa,CAAC,CAACY,YAAY,CAACoB,UAAW;YACjE/K,QAAQ,EAAGM,MAAM,IAAK8J,oBAAoB,CAAC;cAAElK,MAAM,EAAE;gBAAEpB,KAAK,EAAEwB;cAAO;YAAE,CAAC,EAAE,YAAY,CAAE;YACxFC,OAAO,EAAEtB,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;YACvEa,SAAS,EAAC,SAAS;YACnBW,WAAW,EAAC;UAAa;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN7B,OAAA;QAAIwB,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClC7B,OAAA;QAAKwB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAChCzB,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBzB,OAAA,CAAC8J,cAAc;YACXhJ,KAAK,EAAEgK,SAAS,CAACC,aAAa,CAAC,CAACY,YAAY,CAACqB,OAAQ;YACrDhL,QAAQ,EAAGC,CAAC,IAAKmK,oBAAoB,CAACnK,CAAC,EAAE,SAAS,CAAE;YACpDE,WAAW,EAAC,yCAAuB;YACnCqC,KAAK,EAAC;UAAS;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,EACD,CAACsH,IAAI,KAAK,OAAO,IAAI2B,SAAS,CAACC,aAAa,CAAC,CAACY,YAAY,CAACsB,QAAQ,kBAChEjN,OAAA,CAACP,aAAa;YACVwN,QAAQ,EAAEnC,SAAS,CAACC,aAAa,CAAC,CAACY,YAAY,CAACsB,QAAS;YACzDC,WAAW,EAAGhK,KAAK,IAAKkJ,oBAAoB,CAAC;cAAElK,MAAM,EAAE;gBAAEpB,KAAK,EAAEoC;cAAM;YAAE,CAAC,EAAE,UAAU,CAAE;YACvFiK,aAAa,EAAEA,CAAA,KAAMf,oBAAoB,CAAC;cAAElK,MAAM,EAAE;gBAAEpB,KAAK,EAAE;cAAG;YAAE,CAAC,EAAE,UAAU;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CACJ,EACAiJ,SAAS,CAACC,aAAa,CAAC,CAACY,YAAY,CAACC,cAAc,KAAK,KAAK,iBAC3D5L,OAAA;YAAKwB,SAAS,EAAC,WAAW;YAAAC,QAAA,EACrBqJ,SAAS,CAACC,aAAa,CAAC,CAAC6B,UAAU,CAACpF,GAAG,CAAC,CAAC4F,SAAS,EAAE3E,KAAK,kBACtDzI,OAAA;cAAiBwB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBAChEzB,OAAA;gBAAKwB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBACpDzB,OAAA;kBAAGwB,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAC7CqJ,SAAS,CAACC,aAAa,CAAC,CAACY,YAAY,CAACC,cAAc,KAAK,IAAI,GAAGP,QAAQ,CAAC5C,KAAK,CAAC,GAAG6C,QAAQ,CAAC7C,KAAK;gBAAC;kBAAA/G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,eACJ7B,OAAA,CAAC8J,cAAc;kBACXhJ,KAAK,EAAEsM,SAAS,CAACJ,OAAQ;kBACzBhL,QAAQ,EAAGC,CAAC,IAAK0K,qBAAqB,CAAClE,KAAK,EAAExG,CAAC,CAACC,MAAM,CAACpB,KAAK,EAAE,SAAS,CAAE;kBACzEqB,WAAW,EAAC,gDAAuB;kBACnC8H,GAAG,EAAE;gBAAK;kBAAAvI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACL,CAACsH,IAAI,KAAK,OAAO,IAAIiE,SAAS,CAACH,QAAQ,kBACpCjN,OAAA,CAACP,aAAa;gBACVwN,QAAQ,EAAEG,SAAS,CAACH,QAAS;gBAC7BC,WAAW,EAAGhK,KAAK,IAAKyJ,qBAAqB,CAAClE,KAAK,EAAEvF,KAAK,EAAE,UAAU,CAAE;gBACxEiK,aAAa,EAAEA,CAAA,KAAMR,qBAAqB,CAAClE,KAAK,EAAE,EAAE,EAAE,UAAU;cAAE;gBAAA/G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CACJ;YAAA,GAlBK4G,KAAK;cAAA/G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EACAiJ,SAAS,CAACC,aAAa,CAAC,CAACY,YAAY,CAACC,cAAc,KAAK,KAAK,iBAC3D5L,OAAA;YAAKwB,SAAS,EAAC,WAAW;YAAAC,QAAA,eACtBzB,OAAA,CAACN,QAAQ;cACLoB,KAAK,EAAEgK,SAAS,CAACC,aAAa,CAAC,CAACY,YAAY,CAAC0B,aAAc;cAC3DrL,QAAQ,EAAGC,CAAC,IAAKmK,oBAAoB,CAACnK,CAAC,EAAE,eAAe,CAAE;cAC1DE,WAAW,EAAC,6BAAa;cACzBqC,KAAK,EAAC,mBAAQ;cACdC,IAAI,EAAEjG;YAAY;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,eAQD7B,OAAA,CAACX,cAAc;YACXqN,QAAQ,EAAE5B,SAAS,CAACC,aAAa,CAAC,CAACY,YAAY,CAACe,QAAS;YACzDY,gBAAgB,EAAEd;UAA6B;YAAA9K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAgJ,GAAA,CArMKD,SAAS;EAAA,QACgCjO,WAAW,EACrCD,WAAW,EAQVC,WAAW;AAAA;AAAA4Q,IAAA,GAV3B3C,SAAS;AAwMf,MAAM4C,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtB,MAAMpN,QAAQ,GAAG3D,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgR,IAAI;IAAEpN,QAAQ;IAAE4J,OAAO;IAAE3J,SAAS;IAAEC,QAAQ;IAAEsK;EAAU,CAAC,GAAGnO,WAAW,CAAE8D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EACzG,MAAMiN,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAID,IAAI,GAAG,CAAC,EAAErN,QAAQ,CAACvD,QAAQ,CAAC,CAAC,CAAC;EACtC,CAAC;EAED,MAAM8Q,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIF,IAAI,GAAG,CAAC,EAAErN,QAAQ,CAACtD,QAAQ,CAAC,CAAC,CAAC;EACtC,CAAC;EAED,MAAM8Q,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACtO,gBAAgB,CAACe,QAAQ,EAAED,QAAQ,CAAC,EAAE;IAE3C,MAAMA,QAAQ,CAACxD,QAAQ,CAAC;MACpByD,QAAQ;MACRC,SAAS;MACTuK,SAAS,EAAEA,SAAS,IAAI,EAAE;MAC1BtK;IACJ,CAAC,CAAC,CAAC,CAAC8J,MAAM,CAAC,CAAC;IACZ;EACJ,CAAC;EAED,oBACItK,OAAA;IAAKwB,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBAEjDzB,OAAA,CAACjC,iBAAiB;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGrB7B,OAAA;MAAKwB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,GAElCiM,IAAI,KAAK,CAAC,iBACP1N,OAAA,CAACG,SAAS;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACf,EAEA6L,IAAI,KAAK,CAAC,iBACP1N,OAAA,CAACuG,SAAS;QAAA7E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACf,EAGA6L,IAAI,KAAK,CAAC,iBACP1N,OAAA,CAAC+I,SAAS;QAAArH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACf,EAGA6L,IAAI,KAAK,CAAC,iBACP1N,OAAA,CAAC4K,SAAS;QAAAlJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACf;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eAClDzB,OAAA;QAAKwB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAC9CzB,OAAA;UACI8D,OAAO,EAAE8J,UAAW;UACpB3F,QAAQ,EAAEyF,IAAI,KAAK,CAAE;UACrBlM,SAAS,EAAC,6HAA6H;UAAAC,QAAA,gBAEvIzB,OAAA,CAACtB,WAAW;YAAC8C,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAER6L,IAAI,GAAG,CAAC,gBACL1N,OAAA;UACI8D,OAAO,EAAE6J,UAAW;UACpBnM,SAAS,EAAC,8GAA8G;UAAAC,QAAA,GAC3H,gBAEG,eAAAzB,OAAA,CAACvB,YAAY;YAAC+C,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,gBAET7B,OAAA;UACI8D,OAAO,EAAE+J,YAAa;UACtB5F,QAAQ,EAAEiC,OAAQ;UAClB1I,SAAS,EAAC,oIAAoI;UAAAC,QAAA,EAE7IyI,OAAO,gBACJlK,OAAA,CAAAE,SAAA;YAAAuB,QAAA,gBACIzB,OAAA,CAACb,cAAc;cAAC2O,SAAS,EAAC;YAAS;cAAApM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAE1C;UAAA,eAAE,CAAC,gBAEH7B,OAAA,CAAAE,SAAA;YAAAuB,QAAA,gBACIzB,OAAA,CAACpB,IAAI;cAAC4C,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6BAEhC;UAAA,eAAE;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEf,CAAC;AAAC4L,GAAA,CA7FID,WAAW;EAAA,QACI9Q,WAAW,EACwCC,WAAW;AAAA;AAAAoR,IAAA,GAF7EP,WAAW;AA+FjB,eAAeA,WAAW;AAAC,IAAA9J,EAAA,EAAAK,GAAA,EAAAa,GAAA,EAAAK,GAAA,EAAAK,GAAA,EAAAK,GAAA,EAAAmD,GAAA,EAAAW,GAAA,EAAAI,GAAA,EAAAc,IAAA,EAAA4C,IAAA,EAAAQ,IAAA;AAAAC,YAAA,CAAAtK,EAAA;AAAAsK,YAAA,CAAAjK,GAAA;AAAAiK,YAAA,CAAApJ,GAAA;AAAAoJ,YAAA,CAAA/I,GAAA;AAAA+I,YAAA,CAAA1I,GAAA;AAAA0I,YAAA,CAAArI,GAAA;AAAAqI,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAAnE,GAAA;AAAAmE,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAAT,IAAA;AAAAS,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}