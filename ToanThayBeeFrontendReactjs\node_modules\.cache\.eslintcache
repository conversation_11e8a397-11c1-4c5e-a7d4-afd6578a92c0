[{"C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx": "5", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx": "6", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx": "7", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx": "8", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx": "9", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx": "10", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx": "11", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx": "12", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx": "13", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx": "14", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx": "15", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx": "16", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx": "17", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx": "18", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx": "19", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx": "20", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx": "21", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx": "22", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx": "23", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx": "24", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js": "25", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js": "26", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js": "27", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js": "28", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js": "29", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js": "30", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js": "31", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js": "32", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js": "33", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js": "34", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js": "35", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js": "36", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js": "37", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js": "38", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js": "39", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx": "40", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx": "41", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx": "42", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx": "43", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx": "44", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx": "45", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx": "46", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx": "47", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx": "48", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx": "49", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx": "50", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx": "51", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx": "52", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx": "53", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx": "54", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx": "55", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx": "56", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx": "57", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx": "58", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx": "59", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx": "60", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx": "61", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx": "62", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx": "63", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx": "64", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx": "65", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx": "66", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx": "67", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx": "68", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx": "69", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx": "70", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx": "71", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx": "72", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx": "73", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx": "74", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx": "75", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx": "76", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js": "77", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx": "78", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx": "79", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js": "80", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx": "81", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx": "82", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js": "83", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx": "84", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx": "85", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx": "86", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx": "87", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx": "88", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx": "89", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx": "90", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx": "91", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx": "92", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx": "93", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx": "94", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx": "95", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js": "96", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx": "97", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx": "98", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js": "99", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js": "100", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js": "101", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx": "102", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js": "103", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js": "104", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx": "105", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx": "106", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx": "107", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx": "108", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx": "109", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx": "110", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx": "111", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx": "112", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx": "113", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx": "114", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx": "115", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js": "116", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js": "117", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js": "118", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx": "119", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx": "120", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx": "121", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx": "122", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx": "123", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx": "124", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx": "125", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js": "126", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx": "127", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx": "128", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx": "129", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx": "130", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js": "131", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx": "132", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx": "133", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx": "134", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx": "135", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx": "136", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx": "137", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx": "138", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx": "139", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx": "140", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx": "141", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js": "142", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js": "143", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx": "144", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx": "145", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx": "146", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx": "147", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx": "148", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx": "149", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx": "150", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx": "151", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx": "152", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx": "153", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx": "154", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx": "155", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx": "156", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx": "157", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx": "158", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx": "159", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx": "160", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js": "161", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js": "162", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx": "163", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx": "164", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx": "165", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx": "166", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js": "167", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx": "168", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx": "169", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx": "170", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx": "171", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx": "172", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx": "173", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx": "174", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx": "175", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx": "176", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx": "177", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx": "178", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx": "179", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx": "180", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx": "181", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx": "182", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx": "183", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx": "184", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx": "185", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js": "186", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx": "187", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx": "188", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js": "189", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx": "190", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js": "191", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx": "192", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx": "193", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx": "194", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx": "195", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js": "196", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js": "197", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js": "198", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx": "199", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx": "200", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx": "201", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx": "202", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx": "203", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js": "204", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx": "205", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx": "206", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js": "207", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx": "208", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx": "209", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx": "210", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx": "211", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx": "212", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js": "213", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js": "214", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx": "215", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx": "216", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx": "217", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx": "218", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx": "219", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js": "220", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx": "221", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx": "222", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx": "223", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js": "224", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx": "225", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx": "226", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx": "227", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx": "228", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx": "229", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx": "230", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js": "231", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js": "232", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx": "233", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx": "234", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx": "235", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx": "236", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js": "237", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx": "238", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx": "239", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js": "240", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx": "241", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js": "242", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js": "243", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx": "244", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx": "245", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js": "246", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js": "247", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\CustomSearchInput.jsx": "248", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UnpaidTuitionModal.jsx": "249", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ExamAdminLayout.jsx": "250", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sheet\\sheetSlice.js": "251", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\UpdateTuitionSheetModal.jsx": "252", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\sheetApi.js": "253", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ExamSearchInput.jsx": "254", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiLessonSelector.jsx": "255", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\LessonSearchInput.jsx": "256", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\index.js": "257", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\FullSchedulePage.jsx": "258", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\calendar\\calendarSlice.js": "259", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\SidebarCalender.jsx": "260", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\CalenderMonth.jsx": "261", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\WeekView.jsx": "262", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\MonthView.jsx": "263", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\DayView.jsx": "264", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\NavigateTimeButton.jsx": "265", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\TimeSlots.jsx": "266", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\constants\\UserType.js": "267", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\AdminDashboard.jsx": "268", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\ocrExamApi.js": "269", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\apin8n.js": "270", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx": "271", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx": "272", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx": "273", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TableAdmin.jsx": "274", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingData.jsx": "275", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\setupKatexWarningFilter.js": "276", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StaffManagement.jsx": "277", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\dashboard\\dashboardSlice.js": "278", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\dashboardApi.js": "279", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\layout\\AdminLayout.jsx": "280", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AddExamAdmin.jsx": "281", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\RightContent.jsx": "282", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\LeftContent.jsx": "283", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\addExam\\addExamSlice.js": "284", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\CompactStepHeader.jsx": "285", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\NavigateBar.jsx": "286", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\hooks\\useDebouncedEffect.js": "287", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddImagesModal.jsx": "288", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\SolutionEditor.jsx": "289", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\LeftContent.jsx": "290", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\RightContent.jsx": "291", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionsExam\\questionsExamSlice.js": "292", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\QuestionView.jsx": "293", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\QuestionContent.jsx": "294", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\SortableQuestionItem.jsx": "295", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\SortableStatementsContainer.jsx": "296", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\SortableStatementItem.jsx": "297", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ImageDropZone.jsx": "298", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\TextArea.jsx": "299", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\ImageView.jsx": "300", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\LearningHeader.jsx": "301", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ButtonHeader.jsx": "302", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\common\\OutsideClickWrapper.jsx": "303", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingText.jsx": "304", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\ExamContent.jsx": "305", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\ExamSideBar.jsx": "306", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\LoadingQuestions.jsx": "307", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\QuestionSectionTitle.jsx": "308", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\TrueFalseQuestion.jsx": "309", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\MultipleChoiceQuestion.jsx": "310", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\ShortAnswerQuestion.jsx": "311", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\QuestionImage.jsx": "312", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\QuestionCounter.jsx": "313", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\ProgressBar.jsx": "314", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\SubmitButton.jsx": "315", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\QuestionContent.jsx": "316", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\ModalSubmitExam.jsx": "317", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\common\\WrapperWithTooltip.jsx": "318", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule1.jsx": "319", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\BatteryLoading.jsx": "320", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\SortBar.jsx": "321", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterBar.jsx": "322", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\practice\\practiceSlice.js": "323", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examDetailSlice.js": "324", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\comments\\ExamCommentsSlice.js": "325", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examCommentsApi.js": "326", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\comment\\CommentSection.jsx": "327", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\comment\\CommentItem.jsx": "328", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\comment\\EmojiPicker.jsx": "329", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\comment\\CommentInput.jsx": "330", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\comment\\LoadingCommentItem.jsx": "331", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\question\\QuestionPage.jsx": "332", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\examDetail\\RankingView.jsx": "333", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\examDetail\\UserInfoPanel.jsx": "334", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\examDetail\\HistoryView.jsx": "335", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\examDetail\\PreviewView.jsx": "336", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StarRating.jsx": "337", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\studentExamApi.js": "338", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\ai\\aiSlice.js": "339", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\aiApi.js": "340", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ExamOverviewHeader.jsx": "341", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\scorePage\\scorePageSlice.js": "342", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ai\\AiModal.jsx": "343", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ai\\AiSection.jsx": "344", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ai\\QuestionDropdown.jsx": "345", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ai\\FloatingAiButton.jsx": "346", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ai\\AiChatWidget.jsx": "347", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\codeUtils.js": "348", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\OnlineLoading.jsx": "349", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\shareUntil.js": "350", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ActionButton.jsx": "351"}, {"size": 837, "mtime": 1748800674146, "results": "352", "hashOfConfig": "353"}, {"size": 375, "mtime": 1744531393988, "results": "354", "hashOfConfig": "353"}, {"size": 12232, "mtime": 1753982461226, "results": "355", "hashOfConfig": "353"}, {"size": 3269, "mtime": 1753982430700, "results": "356", "hashOfConfig": "353"}, {"size": 1169, "mtime": 1753785955229, "results": "357", "hashOfConfig": "353"}, {"size": 7439, "mtime": 1751975227633, "results": "358", "hashOfConfig": "353"}, {"size": 7222, "mtime": 1753778505777, "results": "359", "hashOfConfig": "353"}, {"size": 2152, "mtime": 1751276563455, "results": "360", "hashOfConfig": "353"}, {"size": 1271, "mtime": 1744531393978, "results": "361", "hashOfConfig": "353"}, {"size": 21516, "mtime": 1751827522381, "results": "362", "hashOfConfig": "353"}, {"size": 11683, "mtime": 1748250288653, "results": "363", "hashOfConfig": "353"}, {"size": 1979, "mtime": 1749722105422, "results": "364", "hashOfConfig": "353"}, {"size": 551, "mtime": 1744531393982, "results": "365", "hashOfConfig": "353"}, {"size": 275, "mtime": 1748215697376, "results": "366", "hashOfConfig": "353"}, {"size": 1739, "mtime": 1749721392840, "results": "367", "hashOfConfig": "353"}, {"size": 45070, "mtime": 1753159218629, "results": "368", "hashOfConfig": "353"}, {"size": 9223, "mtime": 1751743443365, "results": "369", "hashOfConfig": "353"}, {"size": 350, "mtime": 1750322483573, "results": "370", "hashOfConfig": "353"}, {"size": 19813, "mtime": 1750323348863, "results": "371", "hashOfConfig": "353"}, {"size": 2083, "mtime": 1752239343627, "results": "372", "hashOfConfig": "353"}, {"size": 2397, "mtime": 1752003962009, "results": "373", "hashOfConfig": "353"}, {"size": 1898, "mtime": 1750325540002, "results": "374", "hashOfConfig": "353"}, {"size": 348, "mtime": 1749202010110, "results": "375", "hashOfConfig": "353"}, {"size": 3322, "mtime": 1751743523736, "results": "376", "hashOfConfig": "353"}, {"size": 10253, "mtime": 1753631324747, "results": "377", "hashOfConfig": "353"}, {"size": 733, "mtime": 1751439773864, "results": "378", "hashOfConfig": "353"}, {"size": 7352, "mtime": 1751971764594, "results": "379", "hashOfConfig": "353"}, {"size": 3345, "mtime": 1753676148677, "results": "380", "hashOfConfig": "353"}, {"size": 3523, "mtime": 1752112732971, "results": "381", "hashOfConfig": "353"}, {"size": 9913, "mtime": 1753770548726, "results": "382", "hashOfConfig": "353"}, {"size": 18524, "mtime": 1753833090236, "results": "383", "hashOfConfig": "353"}, {"size": 13809, "mtime": 1753857021158, "results": "384", "hashOfConfig": "353"}, {"size": 1380, "mtime": 1744531393975, "results": "385", "hashOfConfig": "353"}, {"size": 1717, "mtime": 1753855929542, "results": "386", "hashOfConfig": "353"}, {"size": 7117, "mtime": 1753987812580, "results": "387", "hashOfConfig": "353"}, {"size": 5924, "mtime": 1753855658006, "results": "388", "hashOfConfig": "353"}, {"size": 2480, "mtime": 1747721626218, "results": "389", "hashOfConfig": "353"}, {"size": 5150, "mtime": 1748398556383, "results": "390", "hashOfConfig": "353"}, {"size": 1337, "mtime": 1747720637516, "results": "391", "hashOfConfig": "353"}, {"size": 690, "mtime": 1744531393950, "results": "392", "hashOfConfig": "353"}, {"size": 1339, "mtime": 1751554069606, "results": "393", "hashOfConfig": "353"}, {"size": 673, "mtime": 1744531393976, "results": "394", "hashOfConfig": "353"}, {"size": 1495, "mtime": 1751362330062, "results": "395", "hashOfConfig": "353"}, {"size": 2276, "mtime": 1751554069606, "results": "396", "hashOfConfig": "353"}, {"size": 1151, "mtime": 1753966346142, "results": "397", "hashOfConfig": "353"}, {"size": 3200, "mtime": 1744531393954, "results": "398", "hashOfConfig": "353"}, {"size": 3255, "mtime": 1752047065950, "results": "399", "hashOfConfig": "353"}, {"size": 635, "mtime": 1744531393961, "results": "400", "hashOfConfig": "353"}, {"size": 1228, "mtime": 1751361437565, "results": "401", "hashOfConfig": "353"}, {"size": 4288, "mtime": 1751977780456, "results": "402", "hashOfConfig": "353"}, {"size": 7391, "mtime": 1751977756680, "results": "403", "hashOfConfig": "353"}, {"size": 1709, "mtime": 1751982367850, "results": "404", "hashOfConfig": "353"}, {"size": 10068, "mtime": 1752112776538, "results": "405", "hashOfConfig": "353"}, {"size": 5752, "mtime": 1751276751884, "results": "406", "hashOfConfig": "353"}, {"size": 28691, "mtime": 1744958278477, "results": "407", "hashOfConfig": "353"}, {"size": 19936, "mtime": 1748984865157, "results": "408", "hashOfConfig": "353"}, {"size": 5905, "mtime": 1751977734164, "results": "409", "hashOfConfig": "353"}, {"size": 8616, "mtime": 1751977877203, "results": "410", "hashOfConfig": "353"}, {"size": 1815, "mtime": 1753449113113, "results": "411", "hashOfConfig": "353"}, {"size": 3767, "mtime": 1752059450460, "results": "412", "hashOfConfig": "353"}, {"size": 31136, "mtime": 1751782971202, "results": "413", "hashOfConfig": "353"}, {"size": 9670, "mtime": 1752000747881, "results": "414", "hashOfConfig": "353"}, {"size": 1574, "mtime": 1744531393960, "results": "415", "hashOfConfig": "353"}, {"size": 7401, "mtime": 1747223318342, "results": "416", "hashOfConfig": "353"}, {"size": 645, "mtime": 1753623621081, "results": "417", "hashOfConfig": "353"}, {"size": 28398, "mtime": 1749425223637, "results": "418", "hashOfConfig": "353"}, {"size": 17584, "mtime": 1750758162845, "results": "419", "hashOfConfig": "353"}, {"size": 17782, "mtime": 1753687748865, "results": "420", "hashOfConfig": "353"}, {"size": 1734, "mtime": 1744531393948, "results": "421", "hashOfConfig": "353"}, {"size": 6857, "mtime": 1751977816258, "results": "422", "hashOfConfig": "353"}, {"size": 5637, "mtime": 1747254491214, "results": "423", "hashOfConfig": "353"}, {"size": 22219, "mtime": 1752133135691, "results": "424", "hashOfConfig": "353"}, {"size": 10256, "mtime": 1744531393939, "results": "425", "hashOfConfig": "353"}, {"size": 1876, "mtime": 1753971025776, "results": "426", "hashOfConfig": "353"}, {"size": 19265, "mtime": 1751742523955, "results": "427", "hashOfConfig": "353"}, {"size": 6162, "mtime": 1748250288622, "results": "428", "hashOfConfig": "353"}, {"size": 3670, "mtime": 1753856653070, "results": "429", "hashOfConfig": "353"}, {"size": 7937, "mtime": 1753621522848, "results": "430", "hashOfConfig": "353"}, {"size": 6118, "mtime": 1751977901342, "results": "431", "hashOfConfig": "353"}, {"size": 935, "mtime": 1745405710864, "results": "432", "hashOfConfig": "353"}, {"size": 949, "mtime": 1747223318316, "results": "433", "hashOfConfig": "353"}, {"size": 3327, "mtime": 1753611701206, "results": "434", "hashOfConfig": "353"}, {"size": 3005, "mtime": 1750849360884, "results": "435", "hashOfConfig": "353"}, {"size": 4276, "mtime": 1752243788413, "results": "436", "hashOfConfig": "353"}, {"size": 2380, "mtime": 1744531393947, "results": "437", "hashOfConfig": "353"}, {"size": 2201, "mtime": 1744531393951, "results": "438", "hashOfConfig": "353"}, {"size": 5867, "mtime": 1753839221738, "results": "439", "hashOfConfig": "353"}, {"size": 1990, "mtime": 1744531393948, "results": "440", "hashOfConfig": "353"}, {"size": 841, "mtime": 1748984865154, "results": "441", "hashOfConfig": "353"}, {"size": 5565, "mtime": 1745690690469, "results": "442", "hashOfConfig": "353"}, {"size": 2295, "mtime": 1747223318353, "results": "443", "hashOfConfig": "353"}, {"size": 3146, "mtime": 1744531393940, "results": "444", "hashOfConfig": "353"}, {"size": 5818, "mtime": 1753411972219, "results": "445", "hashOfConfig": "353"}, {"size": 2787, "mtime": 1750173366905, "results": "446", "hashOfConfig": "353"}, {"size": 10634, "mtime": 1745483150534, "results": "447", "hashOfConfig": "448"}, {"size": 4165, "mtime": 1753770450186, "results": "449", "hashOfConfig": "353"}, {"size": 6034, "mtime": 1748364026312, "results": "450", "hashOfConfig": "353"}, {"size": 4087, "mtime": 1753501608709, "results": "451", "hashOfConfig": "353"}, {"size": 822, "mtime": 1751276455614, "results": "452", "hashOfConfig": "353"}, {"size": 3408, "mtime": 1753962692322, "results": "453", "hashOfConfig": "353"}, {"size": 297, "mtime": 1744531393989, "results": "454", "hashOfConfig": "353"}, {"size": 313, "mtime": 1744531393940, "results": "455", "hashOfConfig": "353"}, {"size": 4755, "mtime": 1751305471358, "results": "456", "hashOfConfig": "353"}, {"size": 1946, "mtime": 1751971622958, "results": "457", "hashOfConfig": "353"}, {"size": 993, "mtime": 1747223318349, "results": "458", "hashOfConfig": "353"}, {"size": 10898, "mtime": 1753454366198, "results": "459", "hashOfConfig": "353"}, {"size": 902, "mtime": 1747223318353, "results": "460", "hashOfConfig": "353"}, {"size": 3053, "mtime": 1744531393946, "results": "461", "hashOfConfig": "353"}, {"size": 50713, "mtime": 1753785458792, "results": "462", "hashOfConfig": "353"}, {"size": 2985, "mtime": 1753407567040, "results": "463", "hashOfConfig": "353"}, {"size": 4872, "mtime": 1747223318349, "results": "464", "hashOfConfig": "353"}, {"size": 1641, "mtime": 1751827507517, "results": "465", "hashOfConfig": "353"}, {"size": 2297, "mtime": 1744531393945, "results": "466", "hashOfConfig": "353"}, {"size": 2193, "mtime": 1747223318349, "results": "467", "hashOfConfig": "353"}, {"size": 1359, "mtime": 1747223318349, "results": "468", "hashOfConfig": "353"}, {"size": 826, "mtime": 1748398318309, "results": "469", "hashOfConfig": "353"}, {"size": 2634, "mtime": 1753987711040, "results": "470", "hashOfConfig": "353"}, {"size": 1158, "mtime": 1753855782911, "results": "471", "hashOfConfig": "353"}, {"size": 4921, "mtime": 1747223318325, "results": "472", "hashOfConfig": "353"}, {"size": 9098, "mtime": 1753453852772, "results": "473", "hashOfConfig": "353"}, {"size": 411, "mtime": 1744531393940, "results": "474", "hashOfConfig": "353"}, {"size": 2290, "mtime": 1744531393963, "results": "475", "hashOfConfig": "353"}, {"size": 1219, "mtime": 1747467640276, "results": "476", "hashOfConfig": "353"}, {"size": 2003, "mtime": 1744531393970, "results": "477", "hashOfConfig": "353"}, {"size": 2166, "mtime": 1744531393969, "results": "478", "hashOfConfig": "353"}, {"size": 23827, "mtime": 1753986919012, "results": "479", "hashOfConfig": "353"}, {"size": 8372, "mtime": 1752047784140, "results": "480", "hashOfConfig": "353"}, {"size": 3094, "mtime": 1744531393970, "results": "481", "hashOfConfig": "353"}, {"size": 10222, "mtime": 1752047795777, "results": "482", "hashOfConfig": "353"}, {"size": 503, "mtime": 1744531393949, "results": "483", "hashOfConfig": "353"}, {"size": 394, "mtime": 1752269433445, "results": "484", "hashOfConfig": "353"}, {"size": 7876, "mtime": 1747223318342, "results": "485", "hashOfConfig": "353"}, {"size": 4922, "mtime": 1748329867180, "results": "486", "hashOfConfig": "353"}, {"size": 28555, "mtime": 1753773982276, "results": "487", "hashOfConfig": "353"}, {"size": 20555, "mtime": 1748250288625, "results": "488", "hashOfConfig": "353"}, {"size": 1337, "mtime": 1744531393967, "results": "489", "hashOfConfig": "448"}, {"size": 5412, "mtime": 1747223318349, "results": "490", "hashOfConfig": "353"}, {"size": 2938, "mtime": 1747223318353, "results": "491", "hashOfConfig": "353"}, {"size": 3182, "mtime": 1747223318353, "results": "492", "hashOfConfig": "353"}, {"size": 2928, "mtime": 1747223318349, "results": "493", "hashOfConfig": "353"}, {"size": 1885, "mtime": 1747354661883, "results": "494", "hashOfConfig": "353"}, {"size": 1345, "mtime": 1749697625937, "results": "495", "hashOfConfig": "353"}, {"size": 4099, "mtime": 1749731407409, "results": "496", "hashOfConfig": "353"}, {"size": 1576, "mtime": 1751811065450, "results": "497", "hashOfConfig": "353"}, {"size": 6380, "mtime": 1747361017417, "results": "498", "hashOfConfig": "353"}, {"size": 2600, "mtime": 1753496401537, "results": "499", "hashOfConfig": "353"}, {"size": 11569, "mtime": 1753501034601, "results": "500", "hashOfConfig": "353"}, {"size": 3297, "mtime": 1753785960960, "results": "501", "hashOfConfig": "353"}, {"size": 31011, "mtime": 1753601685205, "results": "502", "hashOfConfig": "353"}, {"size": 14565, "mtime": 1750819864802, "results": "503", "hashOfConfig": "353"}, {"size": 1205, "mtime": 1748984865161, "results": "504", "hashOfConfig": "353"}, {"size": 12148, "mtime": 1748250288636, "results": "505", "hashOfConfig": "353"}, {"size": 7688, "mtime": 1747223318312, "results": "506", "hashOfConfig": "353"}, {"size": 8344, "mtime": 1745507778499, "results": "507", "hashOfConfig": "353"}, {"size": 8025, "mtime": 1745507836095, "results": "508", "hashOfConfig": "353"}, {"size": 6988, "mtime": 1747223318344, "results": "509", "hashOfConfig": "353"}, {"size": 7719, "mtime": 1745499803667, "results": "510", "hashOfConfig": "353"}, {"size": 8378, "mtime": 1747223318344, "results": "511", "hashOfConfig": "353"}, {"size": 7254, "mtime": 1747223318344, "results": "512", "hashOfConfig": "353"}, {"size": 1770, "mtime": 1751850036982, "results": "513", "hashOfConfig": "353"}, {"size": 11464, "mtime": 1745500164258, "results": "514", "hashOfConfig": "353"}, {"size": 4650, "mtime": 1745508333678, "results": "515", "hashOfConfig": "353"}, {"size": 13822, "mtime": 1748250288625, "results": "516", "hashOfConfig": "353"}, {"size": 8599, "mtime": 1747223318326, "results": "517", "hashOfConfig": "353"}, {"size": 9774, "mtime": 1747223318326, "results": "518", "hashOfConfig": "353"}, {"size": 7914, "mtime": 1747223318326, "results": "519", "hashOfConfig": "353"}, {"size": 10728, "mtime": 1749548057374, "results": "520", "hashOfConfig": "353"}, {"size": 3429, "mtime": 1745682027607, "results": "521", "hashOfConfig": "353"}, {"size": 2298, "mtime": 1749802834779, "results": "522", "hashOfConfig": "353"}, {"size": 823, "mtime": 1748779533260, "results": "523", "hashOfConfig": "353"}, {"size": 1389, "mtime": 1753406785327, "results": "524", "hashOfConfig": "353"}, {"size": 1176, "mtime": 1753449376899, "results": "525", "hashOfConfig": "353"}, {"size": 1781, "mtime": 1745682059432, "results": "526", "hashOfConfig": "448"}, {"size": 2723, "mtime": 1749810234131, "results": "527", "hashOfConfig": "353"}, {"size": 1075, "mtime": 1753407601764, "results": "528", "hashOfConfig": "353"}, {"size": 13626, "mtime": 1753454628043, "results": "529", "hashOfConfig": "353"}, {"size": 70430, "mtime": 1748984865165, "results": "530", "hashOfConfig": "353"}, {"size": 12968, "mtime": 1753844225694, "results": "531", "hashOfConfig": "353"}, {"size": 15741, "mtime": 1753673484936, "results": "532", "hashOfConfig": "353"}, {"size": 21291, "mtime": 1753972584512, "results": "533", "hashOfConfig": "353"}, {"size": 15829, "mtime": 1753833108487, "results": "534", "hashOfConfig": "353"}, {"size": 34299, "mtime": 1753635307809, "results": "535", "hashOfConfig": "353"}, {"size": 17237, "mtime": 1753626680725, "results": "536", "hashOfConfig": "353"}, {"size": 8060, "mtime": 1747223318310, "results": "537", "hashOfConfig": "353"}, {"size": 16767, "mtime": 1748876218633, "results": "538", "hashOfConfig": "353"}, {"size": 3160, "mtime": 1745731138150, "results": "539", "hashOfConfig": "353"}, {"size": 7136, "mtime": 1747223318325, "results": "540", "hashOfConfig": "353"}, {"size": 20572, "mtime": 1751978048828, "results": "541", "hashOfConfig": "353"}, {"size": 2129, "mtime": 1746378664905, "results": "542", "hashOfConfig": "353"}, {"size": 955, "mtime": 1746378664898, "results": "543", "hashOfConfig": "353"}, {"size": 1184, "mtime": 1746378664905, "results": "544", "hashOfConfig": "353"}, {"size": 13380, "mtime": 1753971421112, "results": "545", "hashOfConfig": "353"}, {"size": 1099, "mtime": 1748326442261, "results": "546", "hashOfConfig": "353"}, {"size": 11963, "mtime": 1750323324025, "results": "547", "hashOfConfig": "353"}, {"size": 12224, "mtime": 1748220515100, "results": "548", "hashOfConfig": "353"}, {"size": 10534, "mtime": 1748220627343, "results": "549", "hashOfConfig": "353"}, {"size": 4031, "mtime": 1747278525096, "results": "550", "hashOfConfig": "353"}, {"size": 2036, "mtime": 1747283717643, "results": "551", "hashOfConfig": "353"}, {"size": 40452, "mtime": 1753972798900, "results": "552", "hashOfConfig": "353"}, {"size": 3589, "mtime": 1747355350828, "results": "553", "hashOfConfig": "353"}, {"size": 7509, "mtime": 1747353152130, "results": "554", "hashOfConfig": "353"}, {"size": 6153, "mtime": 1747354063004, "results": "555", "hashOfConfig": "353"}, {"size": 16791, "mtime": 1748473697636, "results": "556", "hashOfConfig": "353"}, {"size": 12245, "mtime": 1750386603287, "results": "557", "hashOfConfig": "353"}, {"size": 13612, "mtime": 1750175198005, "results": "558", "hashOfConfig": "353"}, {"size": 73138, "mtime": 1750487274356, "results": "559", "hashOfConfig": "353"}, {"size": 5589, "mtime": 1750239106405, "results": "560", "hashOfConfig": "353"}, {"size": 9836, "mtime": 1750175833314, "results": "561", "hashOfConfig": "353"}, {"size": 7964, "mtime": 1750400179899, "results": "562", "hashOfConfig": "353"}, {"size": 4152, "mtime": 1749202010110, "results": "563", "hashOfConfig": "353"}, {"size": 4762, "mtime": 1748513292659, "results": "564", "hashOfConfig": "353"}, {"size": 2443, "mtime": 1747719362467, "results": "565", "hashOfConfig": "353"}, {"size": 16049, "mtime": 1751785268325, "results": "566", "hashOfConfig": "353"}, {"size": 2002, "mtime": 1751785248620, "results": "567", "hashOfConfig": "353"}, {"size": 55806, "mtime": 1751975191453, "results": "568", "hashOfConfig": "353"}, {"size": 16095, "mtime": 1751785298468, "results": "569", "hashOfConfig": "353"}, {"size": 8222, "mtime": 1748250288630, "results": "570", "hashOfConfig": "353"}, {"size": 11210, "mtime": 1748223444732, "results": "571", "hashOfConfig": "353"}, {"size": 41991, "mtime": 1751785335077, "results": "572", "hashOfConfig": "353"}, {"size": 9160, "mtime": 1753602735973, "results": "573", "hashOfConfig": "353"}, {"size": 28083, "mtime": 1748250288657, "results": "574", "hashOfConfig": "353"}, {"size": 29543, "mtime": 1748984865164, "results": "575", "hashOfConfig": "353"}, {"size": 36682, "mtime": 1748250768337, "results": "576", "hashOfConfig": "353"}, {"size": 2115, "mtime": 1753602817798, "results": "577", "hashOfConfig": "353"}, {"size": 9569, "mtime": 1749694485776, "results": "578", "hashOfConfig": "353"}, {"size": 13102, "mtime": 1748250288647, "results": "579", "hashOfConfig": "353"}, {"size": 16077, "mtime": 1748365756504, "results": "580", "hashOfConfig": "353"}, {"size": 3992, "mtime": 1753159805042, "results": "581", "hashOfConfig": "353"}, {"size": 3539, "mtime": 1748800991826, "results": "582", "hashOfConfig": "353"}, {"size": 1712, "mtime": 1748800656400, "results": "583", "hashOfConfig": "353"}, {"size": 1983, "mtime": 1751010202512, "results": "584", "hashOfConfig": "353"}, {"size": 3908, "mtime": 1748801325319, "results": "585", "hashOfConfig": "353"}, {"size": 839, "mtime": 1748801505979, "results": "586", "hashOfConfig": "353"}, {"size": 365, "mtime": 1748984865153, "results": "587", "hashOfConfig": "353"}, {"size": 1850, "mtime": 1752243785228, "results": "588", "hashOfConfig": "353"}, {"size": 8860, "mtime": 1749202010110, "results": "589", "hashOfConfig": "353"}, {"size": 5114, "mtime": 1753602323704, "results": "590", "hashOfConfig": "353"}, {"size": 8088, "mtime": 1751977681640, "results": "591", "hashOfConfig": "353"}, {"size": 4156, "mtime": 1753159814167, "results": "592", "hashOfConfig": "353"}, {"size": 958, "mtime": 1750951934365, "results": "593", "hashOfConfig": "353"}, {"size": 2836, "mtime": 1749722547776, "results": "594", "hashOfConfig": "353"}, {"size": 913, "mtime": 1751742948436, "results": "595", "hashOfConfig": "353"}, {"size": 388, "mtime": 1749720429395, "results": "596", "hashOfConfig": "353"}, {"size": 579, "mtime": 1749731593347, "results": "597", "hashOfConfig": "353"}, {"size": 1948, "mtime": 1751543127045, "results": "598", "hashOfConfig": "353"}, {"size": 19880, "mtime": 1753449971664, "results": "599", "hashOfConfig": "353"}, {"size": 1664, "mtime": 1749808930419, "results": "600", "hashOfConfig": "353"}, {"size": 2401, "mtime": 1750389237348, "results": "601", "hashOfConfig": "353"}, {"size": 7582, "mtime": 1750400196241, "results": "602", "hashOfConfig": "353"}, {"size": 3926, "mtime": 1752228453755, "results": "603", "hashOfConfig": "353"}, {"size": 2502, "mtime": 1751969779191, "results": "604", "hashOfConfig": "353"}, {"size": 6803, "mtime": 1751969853518, "results": "605", "hashOfConfig": "353"}, {"size": 200, "mtime": 1750487274358, "results": "606", "hashOfConfig": "353"}, {"size": 4832, "mtime": 1750574009240, "results": "607", "hashOfConfig": "353"}, {"size": 3066, "mtime": 1750766071078, "results": "608", "hashOfConfig": "353"}, {"size": 4807, "mtime": 1750765702772, "results": "609", "hashOfConfig": "353"}, {"size": 74, "mtime": 1748250288649, "results": "610", "hashOfConfig": "353"}, {"size": 4384, "mtime": 1750962443161, "results": "611", "hashOfConfig": "353"}, {"size": 4263, "mtime": 1750960348603, "results": "612", "hashOfConfig": "353"}, {"size": 4331, "mtime": 1750963155580, "results": "613", "hashOfConfig": "353"}, {"size": 3878, "mtime": 1750940043247, "results": "614", "hashOfConfig": "353"}, {"size": 23770, "mtime": 1750963670443, "results": "615", "hashOfConfig": "353"}, {"size": 17942, "mtime": 1750963374804, "results": "616", "hashOfConfig": "353"}, {"size": 22243, "mtime": 1750961198993, "results": "617", "hashOfConfig": "353"}, {"size": 1252, "mtime": 1750941383903, "results": "618", "hashOfConfig": "353"}, {"size": 846, "mtime": 1750944008903, "results": "619", "hashOfConfig": "353"}, {"size": 218, "mtime": 1751349395110, "results": "620", "hashOfConfig": "353"}, {"size": 17515, "mtime": 1751975558805, "results": "621", "hashOfConfig": "353"}, {"size": 354, "mtime": 1753983305827, "results": "622", "hashOfConfig": "353"}, {"size": 449, "mtime": 1751515976272, "results": "623", "hashOfConfig": "353"}, {"size": 759, "mtime": 1751785941157, "results": "624", "hashOfConfig": "353"}, {"size": 4350, "mtime": 1751784560929, "results": "625", "hashOfConfig": "353"}, {"size": 4259, "mtime": 1751742649369, "results": "626", "hashOfConfig": "353"}, {"size": 851, "mtime": 1752000599842, "results": "627", "hashOfConfig": "353"}, {"size": 1300, "mtime": 1753735232911, "results": "628", "hashOfConfig": "353"}, {"size": 1702, "mtime": 1751829247534, "results": "629", "hashOfConfig": "353"}, {"size": 8061, "mtime": 1751981788773, "results": "630", "hashOfConfig": "353"}, {"size": 6115, "mtime": 1751974570045, "results": "631", "hashOfConfig": "353"}, {"size": 1301, "mtime": 1751974544781, "results": "632", "hashOfConfig": "353"}, {"size": 1565, "mtime": 1751974714234, "results": "633", "hashOfConfig": "353"}, {"size": 5999, "mtime": 1753986945508, "results": "634", "hashOfConfig": "353"}, {"size": 17861, "mtime": 1753988680962, "results": "635", "hashOfConfig": "353"}, {"size": 39239, "mtime": 1753988228394, "results": "636", "hashOfConfig": "353"}, {"size": 5468, "mtime": 1753984153467, "results": "637", "hashOfConfig": "353"}, {"size": 2173, "mtime": 1753982834410, "results": "638", "hashOfConfig": "353"}, {"size": 593, "mtime": 1752051337091, "results": "639", "hashOfConfig": "353"}, {"size": 592, "mtime": 1752053261180, "results": "640", "hashOfConfig": "353"}, {"size": 12882, "mtime": 1752061763745, "results": "641", "hashOfConfig": "353"}, {"size": 4514, "mtime": 1752242389997, "results": "642", "hashOfConfig": "353"}, {"size": 25557, "mtime": 1753159697017, "results": "643", "hashOfConfig": "353"}, {"size": 4300, "mtime": 1752241039808, "results": "644", "hashOfConfig": "353"}, {"size": 8924, "mtime": 1752241748653, "results": "645", "hashOfConfig": "353"}, {"size": 9040, "mtime": 1752236766925, "results": "646", "hashOfConfig": "353"}, {"size": 4044, "mtime": 1752243999360, "results": "647", "hashOfConfig": "353"}, {"size": 1511, "mtime": 1752233024753, "results": "648", "hashOfConfig": "353"}, {"size": 3492, "mtime": 1752233095186, "results": "649", "hashOfConfig": "353"}, {"size": 2241, "mtime": 1752233181958, "results": "650", "hashOfConfig": "353"}, {"size": 3061, "mtime": 1752234241391, "results": "651", "hashOfConfig": "353"}, {"size": 2538, "mtime": 1753985474021, "results": "652", "hashOfConfig": "353"}, {"size": 2297, "mtime": 1752234795565, "results": "653", "hashOfConfig": "353"}, {"size": 3645, "mtime": 1753626892270, "results": "654", "hashOfConfig": "353"}, {"size": 740, "mtime": 1753612070978, "results": "655", "hashOfConfig": "353"}, {"size": 1178, "mtime": 1753448701964, "results": "656", "hashOfConfig": "353"}, {"size": 376, "mtime": 1753761858044, "results": "657", "hashOfConfig": "353"}, {"size": 4615, "mtime": 1753454082909, "results": "658", "hashOfConfig": "353"}, {"size": 7086, "mtime": 1753455102578, "results": "659", "hashOfConfig": "353"}, {"size": 1760, "mtime": 1753441079659, "results": "660", "hashOfConfig": "353"}, {"size": 496, "mtime": 1753438589808, "results": "661", "hashOfConfig": "353"}, {"size": 7071, "mtime": 1753451194253, "results": "662", "hashOfConfig": "353"}, {"size": 3881, "mtime": 1753443954667, "results": "663", "hashOfConfig": "353"}, {"size": 2747, "mtime": 1753454152386, "results": "664", "hashOfConfig": "353"}, {"size": 1104, "mtime": 1753415000400, "results": "665", "hashOfConfig": "353"}, {"size": 1148, "mtime": 1753437535628, "results": "666", "hashOfConfig": "353"}, {"size": 1622, "mtime": 1753437717610, "results": "667", "hashOfConfig": "353"}, {"size": 911, "mtime": 1753438097408, "results": "668", "hashOfConfig": "353"}, {"size": 1204, "mtime": 1753441623760, "results": "669", "hashOfConfig": "353"}, {"size": 1904, "mtime": 1753450900486, "results": "670", "hashOfConfig": "353"}, {"size": 493, "mtime": 1753500354742, "results": "671", "hashOfConfig": "353"}, {"size": 30960, "mtime": 1753602056712, "results": "672", "hashOfConfig": "353"}, {"size": 340, "mtime": 1753630179426, "results": "673", "hashOfConfig": "353"}, {"size": 3054, "mtime": 1753674428721, "results": "674", "hashOfConfig": "353"}, {"size": 3362, "mtime": 1753677505699, "results": "675", "hashOfConfig": "353"}, {"size": 4259, "mtime": 1753843828292, "results": "676", "hashOfConfig": "353"}, {"size": 7652, "mtime": 1753962746831, "results": "677", "hashOfConfig": "353"}, {"size": 7599, "mtime": 1753734862354, "results": "678", "hashOfConfig": "353"}, {"size": 904, "mtime": 1753731808097, "results": "679", "hashOfConfig": "353"}, {"size": 4133, "mtime": 1753735679077, "results": "680", "hashOfConfig": "353"}, {"size": 8615, "mtime": 1753787565818, "results": "681", "hashOfConfig": "353"}, {"size": 1722, "mtime": 1753786756505, "results": "682", "hashOfConfig": "353"}, {"size": 1507, "mtime": 1753786293709, "results": "683", "hashOfConfig": "353"}, {"size": 647, "mtime": 1753735287677, "results": "684", "hashOfConfig": "353"}, {"size": 31528, "mtime": 1753970903069, "results": "685", "hashOfConfig": "353"}, {"size": 13674, "mtime": 1753787518896, "results": "686", "hashOfConfig": "353"}, {"size": 3477, "mtime": 1753777631810, "results": "687", "hashOfConfig": "353"}, {"size": 10086, "mtime": 1753777590222, "results": "688", "hashOfConfig": "353"}, {"size": 7431, "mtime": 1753777132479, "results": "689", "hashOfConfig": "353"}, {"size": 5159, "mtime": 1753842059993, "results": "690", "hashOfConfig": "353"}, {"size": 411, "mtime": 1753839233751, "results": "691", "hashOfConfig": "353"}, {"size": 1797, "mtime": 1753849823554, "results": "692", "hashOfConfig": "353"}, {"size": 409, "mtime": 1753849897112, "results": "693", "hashOfConfig": "353"}, {"size": 6757, "mtime": 1753971192453, "results": "694", "hashOfConfig": "353"}, {"size": 3528, "mtime": 1753857218384, "results": "695", "hashOfConfig": "353"}, {"size": 7142, "mtime": 1753869572240, "results": "696", "hashOfConfig": "353"}, {"size": 4197, "mtime": 1753870869108, "results": "697", "hashOfConfig": "353"}, {"size": 4137, "mtime": 1753869628141, "results": "698", "hashOfConfig": "353"}, {"size": 815, "mtime": 1753869935707, "results": "699", "hashOfConfig": "353"}, {"size": 8129, "mtime": 1753966429486, "results": "700", "hashOfConfig": "353"}, {"size": 139, "mtime": 1753964502645, "results": "701", "hashOfConfig": "353"}, {"size": 576, "mtime": 1753966368176, "results": "702", "hashOfConfig": "353"}, {"size": 1206, "mtime": 1753970782857, "results": "703", "hashOfConfig": "353"}, {"size": 709, "mtime": 1753972576899, "results": "704", "hashOfConfig": "353"}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e07wfn", {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1op6h7j", {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1227", "messages": "1228", "suppressedMessages": "1229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1230", "messages": "1231", "suppressedMessages": "1232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1233", "messages": "1234", "suppressedMessages": "1235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1236", "messages": "1237", "suppressedMessages": "1238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1239", "messages": "1240", "suppressedMessages": "1241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1242", "messages": "1243", "suppressedMessages": "1244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1245", "messages": "1246", "suppressedMessages": "1247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1248", "messages": "1249", "suppressedMessages": "1250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1251", "messages": "1252", "suppressedMessages": "1253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1254", "messages": "1255", "suppressedMessages": "1256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1257", "messages": "1258", "suppressedMessages": "1259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1260", "messages": "1261", "suppressedMessages": "1262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1263", "messages": "1264", "suppressedMessages": "1265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1266", "messages": "1267", "suppressedMessages": "1268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1269", "messages": "1270", "suppressedMessages": "1271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1272", "messages": "1273", "suppressedMessages": "1274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1275", "messages": "1276", "suppressedMessages": "1277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1278", "messages": "1279", "suppressedMessages": "1280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1281", "messages": "1282", "suppressedMessages": "1283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1284", "messages": "1285", "suppressedMessages": "1286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1287", "messages": "1288", "suppressedMessages": "1289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1290", "messages": "1291", "suppressedMessages": "1292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1293", "messages": "1294", "suppressedMessages": "1295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1296", "messages": "1297", "suppressedMessages": "1298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1299", "messages": "1300", "suppressedMessages": "1301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1302", "messages": "1303", "suppressedMessages": "1304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1305", "messages": "1306", "suppressedMessages": "1307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1308", "messages": "1309", "suppressedMessages": "1310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1311", "messages": "1312", "suppressedMessages": "1313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1314", "messages": "1315", "suppressedMessages": "1316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1317", "messages": "1318", "suppressedMessages": "1319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1320", "messages": "1321", "suppressedMessages": "1322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1323", "messages": "1324", "suppressedMessages": "1325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1326", "messages": "1327", "suppressedMessages": "1328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1329", "messages": "1330", "suppressedMessages": "1331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1332", "messages": "1333", "suppressedMessages": "1334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1335", "messages": "1336", "suppressedMessages": "1337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1338", "messages": "1339", "suppressedMessages": "1340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1341", "messages": "1342", "suppressedMessages": "1343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1344", "messages": "1345", "suppressedMessages": "1346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1347", "messages": "1348", "suppressedMessages": "1349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1350", "messages": "1351", "suppressedMessages": "1352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1353", "messages": "1354", "suppressedMessages": "1355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1356", "messages": "1357", "suppressedMessages": "1358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1359", "messages": "1360", "suppressedMessages": "1361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1362", "messages": "1363", "suppressedMessages": "1364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1365", "messages": "1366", "suppressedMessages": "1367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1368", "messages": "1369", "suppressedMessages": "1370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1371", "messages": "1372", "suppressedMessages": "1373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1374", "messages": "1375", "suppressedMessages": "1376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1377", "messages": "1378", "suppressedMessages": "1379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1380", "messages": "1381", "suppressedMessages": "1382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1383", "messages": "1384", "suppressedMessages": "1385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1386", "messages": "1387", "suppressedMessages": "1388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1389", "messages": "1390", "suppressedMessages": "1391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1392", "messages": "1393", "suppressedMessages": "1394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1395", "messages": "1396", "suppressedMessages": "1397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1398", "messages": "1399", "suppressedMessages": "1400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1401", "messages": "1402", "suppressedMessages": "1403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1404", "messages": "1405", "suppressedMessages": "1406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1407", "messages": "1408", "suppressedMessages": "1409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1410", "messages": "1411", "suppressedMessages": "1412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1413", "messages": "1414", "suppressedMessages": "1415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1416", "messages": "1417", "suppressedMessages": "1418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1419", "messages": "1420", "suppressedMessages": "1421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1422", "messages": "1423", "suppressedMessages": "1424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1425", "messages": "1426", "suppressedMessages": "1427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1428", "messages": "1429", "suppressedMessages": "1430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1431", "messages": "1432", "suppressedMessages": "1433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1434", "messages": "1435", "suppressedMessages": "1436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1437", "messages": "1438", "suppressedMessages": "1439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1440", "messages": "1441", "suppressedMessages": "1442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1443", "messages": "1444", "suppressedMessages": "1445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1446", "messages": "1447", "suppressedMessages": "1448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1449", "messages": "1450", "suppressedMessages": "1451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1452", "messages": "1453", "suppressedMessages": "1454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1455", "messages": "1456", "suppressedMessages": "1457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1458", "messages": "1459", "suppressedMessages": "1460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1461", "messages": "1462", "suppressedMessages": "1463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1464", "messages": "1465", "suppressedMessages": "1466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1467", "messages": "1468", "suppressedMessages": "1469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1470", "messages": "1471", "suppressedMessages": "1472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1473", "messages": "1474", "suppressedMessages": "1475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1476", "messages": "1477", "suppressedMessages": "1478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1479", "messages": "1480", "suppressedMessages": "1481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1482", "messages": "1483", "suppressedMessages": "1484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1485", "messages": "1486", "suppressedMessages": "1487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1488", "messages": "1489", "suppressedMessages": "1490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1491", "messages": "1492", "suppressedMessages": "1493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1494", "messages": "1495", "suppressedMessages": "1496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1497", "messages": "1498", "suppressedMessages": "1499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1500", "messages": "1501", "suppressedMessages": "1502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1503", "messages": "1504", "suppressedMessages": "1505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1506", "messages": "1507", "suppressedMessages": "1508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1509", "messages": "1510", "suppressedMessages": "1511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1512", "messages": "1513", "suppressedMessages": "1514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1515", "messages": "1516", "suppressedMessages": "1517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1518", "messages": "1519", "suppressedMessages": "1520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1521", "messages": "1522", "suppressedMessages": "1523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1524", "messages": "1525", "suppressedMessages": "1526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1527", "messages": "1528", "suppressedMessages": "1529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1530", "messages": "1531", "suppressedMessages": "1532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1533", "messages": "1534", "suppressedMessages": "1535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1536", "messages": "1537", "suppressedMessages": "1538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1539", "messages": "1540", "suppressedMessages": "1541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1542", "messages": "1543", "suppressedMessages": "1544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1545", "messages": "1546", "suppressedMessages": "1547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 30, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1548", "messages": "1549", "suppressedMessages": "1550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1551", "messages": "1552", "suppressedMessages": "1553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1554", "messages": "1555", "suppressedMessages": "1556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1557", "messages": "1558", "suppressedMessages": "1559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1560", "messages": "1561", "suppressedMessages": "1562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1563", "messages": "1564", "suppressedMessages": "1565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1566", "messages": "1567", "suppressedMessages": "1568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1569", "messages": "1570", "suppressedMessages": "1571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1572", "messages": "1573", "suppressedMessages": "1574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1575", "messages": "1576", "suppressedMessages": "1577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1578", "messages": "1579", "suppressedMessages": "1580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1581", "messages": "1582", "suppressedMessages": "1583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1584", "messages": "1585", "suppressedMessages": "1586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1587", "messages": "1588", "suppressedMessages": "1589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1590", "messages": "1591", "suppressedMessages": "1592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1593", "messages": "1594", "suppressedMessages": "1595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1596", "messages": "1597", "suppressedMessages": "1598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1599", "messages": "1600", "suppressedMessages": "1601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1602", "messages": "1603", "suppressedMessages": "1604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1605", "messages": "1606", "suppressedMessages": "1607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1608", "messages": "1609", "suppressedMessages": "1610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1611", "messages": "1612", "suppressedMessages": "1613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1614", "messages": "1615", "suppressedMessages": "1616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1617", "messages": "1618", "suppressedMessages": "1619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1620", "messages": "1621", "suppressedMessages": "1622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1623", "messages": "1624", "suppressedMessages": "1625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1626", "messages": "1627", "suppressedMessages": "1628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1629", "messages": "1630", "suppressedMessages": "1631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1632", "messages": "1633", "suppressedMessages": "1634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1635", "messages": "1636", "suppressedMessages": "1637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1638", "messages": "1639", "suppressedMessages": "1640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1641", "messages": "1642", "suppressedMessages": "1643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1644", "messages": "1645", "suppressedMessages": "1646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1647", "messages": "1648", "suppressedMessages": "1649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1650", "messages": "1651", "suppressedMessages": "1652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1653", "messages": "1654", "suppressedMessages": "1655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1656", "messages": "1657", "suppressedMessages": "1658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1659", "messages": "1660", "suppressedMessages": "1661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1662", "messages": "1663", "suppressedMessages": "1664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1665", "messages": "1666", "suppressedMessages": "1667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1668", "messages": "1669", "suppressedMessages": "1670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1671", "messages": "1672", "suppressedMessages": "1673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1674", "messages": "1675", "suppressedMessages": "1676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1677", "messages": "1678", "suppressedMessages": "1679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1680", "messages": "1681", "suppressedMessages": "1682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1683", "messages": "1684", "suppressedMessages": "1685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1686", "messages": "1687", "suppressedMessages": "1688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1689", "messages": "1690", "suppressedMessages": "1691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1692", "messages": "1693", "suppressedMessages": "1694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1695", "messages": "1696", "suppressedMessages": "1697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1698", "messages": "1699", "suppressedMessages": "1700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1701", "messages": "1702", "suppressedMessages": "1703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1704", "messages": "1705", "suppressedMessages": "1706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1707", "messages": "1708", "suppressedMessages": "1709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1710", "messages": "1711", "suppressedMessages": "1712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1713", "messages": "1714", "suppressedMessages": "1715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1716", "messages": "1717", "suppressedMessages": "1718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1719", "messages": "1720", "suppressedMessages": "1721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1722", "messages": "1723", "suppressedMessages": "1724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1725", "messages": "1726", "suppressedMessages": "1727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1728", "messages": "1729", "suppressedMessages": "1730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1731", "messages": "1732", "suppressedMessages": "1733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1734", "messages": "1735", "suppressedMessages": "1736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1737", "messages": "1738", "suppressedMessages": "1739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1740", "messages": "1741", "suppressedMessages": "1742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1743", "messages": "1744", "suppressedMessages": "1745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1746", "messages": "1747", "suppressedMessages": "1748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1749", "messages": "1750", "suppressedMessages": "1751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1752", "messages": "1753", "suppressedMessages": "1754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1755", "messages": "1756", "suppressedMessages": "1757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js", ["1758"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx", ["1759"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx", ["1760", "1761", "1762", "1763", "1764"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx", ["1765", "1766"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx", ["1767", "1768"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx", ["1769"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx", ["1770"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx", ["1771", "1772", "1773", "1774", "1775"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx", ["1776", "1777"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx", ["1778", "1779"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx", ["1780"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx", ["1781", "1782", "1783", "1784", "1785", "1786", "1787"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx", ["1788", "1789", "1790", "1791", "1792"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx", ["1793"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx", ["1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx", ["1807", "1808"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx", ["1809", "1810"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx", ["1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx", ["1819"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js", ["1820"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js", ["1821"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js", ["1822", "1823", "1824"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js", ["1825"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx", ["1826"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx", ["1827"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx", ["1828"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx", ["1829", "1830", "1831", "1832", "1833", "1834", "1835", "1836"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx", ["1837", "1838"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx", ["1839", "1840", "1841"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx", ["1842"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx", ["1843"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx", ["1844", "1845", "1846"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx", ["1847", "1848", "1849"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx", ["1850", "1851", "1852"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx", ["1853", "1854", "1855", "1856", "1857", "1858", "1859", "1860"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx", ["1861", "1862", "1863", "1864", "1865", "1866", "1867", "1868"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx", ["1869", "1870"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx", ["1871", "1872", "1873", "1874", "1875"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx", ["1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx", ["1884"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx", ["1885"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx", ["1886", "1887", "1888", "1889", "1890", "1891"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx", ["1892"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx", ["1893", "1894", "1895", "1896", "1897", "1898"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx", ["1899", "1900", "1901"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx", ["1902"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx", ["1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911", "1912", "1913", "1914", "1915", "1916", "1917", "1918", "1919", "1920"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx", ["1921", "1922", "1923", "1924", "1925"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx", ["1926", "1927", "1928"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx", ["1929", "1930", "1931"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx", ["1932", "1933", "1934", "1935"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx", ["1936"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx", ["1937", "1938", "1939", "1940", "1941"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx", ["1942"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx", ["1943"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js", ["1944", "1945", "1946", "1947", "1948", "1949", "1950", "1951", "1952", "1953", "1954", "1955", "1956", "1957", "1958", "1959", "1960", "1961"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx", ["1962"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx", ["1963"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx", ["1964"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx", ["1965"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx", ["1966"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx", ["1967", "1968", "1969", "1970", "1971", "1972"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx", ["1973"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx", ["1974", "1975", "1976", "1977", "1978", "1979"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx", ["1980"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx", ["1981", "1982", "1983", "1984"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx", ["1985", "1986"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx", ["1987", "1988", "1989", "1990"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx", ["1991", "1992"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx", ["1993", "1994", "1995", "1996"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx", ["1997"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx", ["1998", "1999"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx", ["2000"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx", ["2001", "2002"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx", ["2003"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx", ["2004"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx", ["2005", "2006", "2007", "2008", "2009"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx", ["2010", "2011", "2012", "2013", "2014"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx", ["2015", "2016", "2017", "2018", "2019", "2020", "2021", "2022", "2023", "2024", "2025", "2026", "2027", "2028", "2029"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx", ["2030", "2031", "2032"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx", ["2033"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx", ["2034", "2035", "2036", "2037", "2038", "2039", "2040", "2041", "2042", "2043", "2044", "2045", "2046", "2047", "2048", "2049", "2050", "2051", "2052", "2053", "2054", "2055", "2056"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx", ["2057", "2058", "2059", "2060", "2061", "2062", "2063", "2064", "2065", "2066"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx", ["2067", "2068", "2069", "2070", "2071", "2072", "2073", "2074"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx", ["2075", "2076"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx", ["2077", "2078", "2079", "2080", "2081", "2082", "2083"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx", ["2084", "2085", "2086", "2087", "2088", "2089", "2090"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx", ["2091", "2092", "2093"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js", ["2094"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx", ["2095", "2096", "2097", "2098"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js", ["2099"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx", ["2100", "2101", "2102", "2103", "2104", "2105", "2106", "2107", "2108"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx", ["2109", "2110", "2111", "2112", "2113", "2114", "2115"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js", ["2116"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx", ["2117", "2118", "2119", "2120"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx", ["2121", "2122", "2123", "2124", "2125", "2126", "2127", "2128", "2129", "2130", "2131", "2132", "2133", "2134", "2135", "2136", "2137", "2138", "2139", "2140", "2141", "2142", "2143", "2144", "2145", "2146", "2147", "2148", "2149", "2150", "2151", "2152"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx", ["2153", "2154", "2155", "2156", "2157", "2158"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx", ["2159"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx", ["2160"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx", ["2161"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js", ["2162", "2163"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx", ["2164", "2165", "2166", "2167", "2168", "2169", "2170", "2171"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx", ["2172"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx", ["2173", "2174", "2175", "2176", "2177"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx", ["2178", "2179", "2180"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx", ["2181", "2182"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx", ["2183", "2184"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx", ["2185", "2186", "2187"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx", ["2188"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js", ["2189", "2190", "2191", "2192", "2193"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\CustomSearchInput.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UnpaidTuitionModal.jsx", ["2194", "2195", "2196", "2197"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ExamAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sheet\\sheetSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\UpdateTuitionSheetModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\sheetApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ExamSearchInput.jsx", ["2198"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiLessonSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\LessonSearchInput.jsx", ["2199"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\FullSchedulePage.jsx", ["2200", "2201", "2202", "2203", "2204", "2205", "2206", "2207"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\calendar\\calendarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\SidebarCalender.jsx", ["2208"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\CalenderMonth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\WeekView.jsx", ["2209", "2210", "2211", "2212", "2213", "2214", "2215", "2216", "2217", "2218", "2219", "2220"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\MonthView.jsx", ["2221"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\DayView.jsx", ["2222", "2223", "2224"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\NavigateTimeButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\TimeSlots.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\constants\\UserType.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\AdminDashboard.jsx", ["2225", "2226"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\ocrExamApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\apin8n.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx", ["2227", "2228", "2229", "2230", "2231", "2232"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx", ["2233", "2234", "2235", "2236", "2237"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx", ["2238", "2239", "2240"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TableAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingData.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\setupKatexWarningFilter.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StaffManagement.jsx", ["2241", "2242", "2243"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\dashboard\\dashboardSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\dashboardApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\layout\\AdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AddExamAdmin.jsx", ["2244", "2245", "2246", "2247", "2248", "2249", "2250", "2251", "2252", "2253", "2254", "2255", "2256", "2257", "2258", "2259", "2260", "2261", "2262", "2263", "2264", "2265", "2266", "2267", "2268", "2269", "2270", "2271", "2272", "2273"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\RightContent.jsx", ["2274", "2275", "2276", "2277", "2278", "2279"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\LeftContent.jsx", ["2280", "2281", "2282", "2283", "2284", "2285", "2286", "2287", "2288", "2289"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\addExam\\addExamSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\CompactStepHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\NavigateBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\hooks\\useDebouncedEffect.js", ["2290", "2291"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddImagesModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\SolutionEditor.jsx", ["2292", "2293"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\LeftContent.jsx", ["2294"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\RightContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionsExam\\questionsExamSlice.js", ["2295"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\QuestionView.jsx", ["2296", "2297", "2298", "2299", "2300", "2301", "2302", "2303"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\QuestionContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\SortableQuestionItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\SortableStatementsContainer.jsx", ["2304", "2305"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\SortableStatementItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ImageDropZone.jsx", ["2306"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\TextArea.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\ImageView.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\LearningHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ButtonHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\common\\OutsideClickWrapper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingText.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\ExamContent.jsx", ["2307", "2308", "2309"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\ExamSideBar.jsx", ["2310"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\LoadingQuestions.jsx", ["2311", "2312"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\QuestionSectionTitle.jsx", ["2313"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\TrueFalseQuestion.jsx", ["2314", "2315", "2316"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\MultipleChoiceQuestion.jsx", ["2317", "2318", "2319", "2320"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\ShortAnswerQuestion.jsx", ["2321", "2322", "2323", "2324", "2325", "2326"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\QuestionImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\QuestionCounter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\ProgressBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\SubmitButton.jsx", ["2327"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\QuestionContent.jsx", ["2328"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageDoExam\\ModalSubmitExam.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\common\\WrapperWithTooltip.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule1.jsx", ["2329", "2330"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\BatteryLoading.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\SortBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterBar.jsx", ["2331"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\practice\\practiceSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examDetailSlice.js", ["2332", "2333", "2334", "2335", "2336", "2337"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\comments\\ExamCommentsSlice.js", ["2338", "2339", "2340", "2341"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examCommentsApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\comment\\CommentSection.jsx", ["2342", "2343", "2344", "2345", "2346", "2347"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\comment\\CommentItem.jsx", ["2348", "2349", "2350", "2351", "2352"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\comment\\EmojiPicker.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\comment\\CommentInput.jsx", ["2353"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\comment\\LoadingCommentItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\question\\QuestionPage.jsx", ["2354"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\examDetail\\RankingView.jsx", ["2355", "2356"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\examDetail\\UserInfoPanel.jsx", ["2357", "2358", "2359", "2360", "2361"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\examDetail\\HistoryView.jsx", ["2362"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\examDetail\\PreviewView.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StarRating.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\studentExamApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\ai\\aiSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\aiApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ExamOverviewHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\scorePage\\scorePageSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ai\\AiModal.jsx", ["2363"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ai\\AiSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ai\\QuestionDropdown.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ai\\FloatingAiButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ai\\AiChatWidget.jsx", ["2364", "2365", "2366"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\codeUtils.js", ["2367"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\OnlineLoading.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\shareUntil.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ActionButton.jsx", [], [], {"ruleId": "2368", "severity": 1, "message": "2369", "line": 49, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 49, "endColumn": 27}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 5, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2373", "line": 4, "column": 23, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 27}, {"ruleId": "2368", "severity": 1, "message": "2374", "line": 8, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 25}, {"ruleId": "2368", "severity": 1, "message": "2375", "line": 11, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 11, "endColumn": 21}, {"ruleId": "2376", "severity": 1, "message": "2377", "line": 42, "column": 8, "nodeType": "2378", "endLine": 42, "endColumn": 24, "suggestions": "2379"}, {"ruleId": "2368", "severity": 1, "message": "2380", "line": 49, "column": 15, "nodeType": "2370", "messageId": "2371", "endLine": 49, "endColumn": 27}, {"ruleId": "2368", "severity": 1, "message": "2381", "line": 1, "column": 38, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 44}, {"ruleId": "2368", "severity": 1, "message": "2382", "line": 11, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 11, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2383", "line": 10, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 10, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2384", "line": 18, "column": 24, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 36}, {"ruleId": "2376", "severity": 1, "message": "2385", "line": 26, "column": 8, "nodeType": "2378", "endLine": 26, "endColumn": 19, "suggestions": "2386"}, {"ruleId": "2387", "severity": 1, "message": "2388", "line": 393, "column": 45, "nodeType": "2389", "endLine": 398, "endColumn": 47}, {"ruleId": "2368", "severity": 1, "message": "2390", "line": 6, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 11}, {"ruleId": "2368", "severity": 1, "message": "2391", "line": 6, "column": 27, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 31}, {"ruleId": "2368", "severity": 1, "message": "2392", "line": 7, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 25}, {"ruleId": "2368", "severity": 1, "message": "2393", "line": 8, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 32}, {"ruleId": "2368", "severity": 1, "message": "2394", "line": 88, "column": 23, "nodeType": "2370", "messageId": "2371", "endLine": 88, "endColumn": 31}, {"ruleId": "2368", "severity": 1, "message": "2395", "line": 14, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 14, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2384", "line": 15, "column": 24, "nodeType": "2370", "messageId": "2371", "endLine": 15, "endColumn": 36}, {"ruleId": "2368", "severity": 1, "message": "2396", "line": 2, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2397", "line": 3, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2398", "line": 5, "column": 24, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 39}, {"ruleId": "2368", "severity": 1, "message": "2399", "line": 15, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 15, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2400", "line": 16, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 16, "endColumn": 14}, {"ruleId": "2368", "severity": 1, "message": "2401", "line": 24, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 24, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2402", "line": 30, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 30, "endColumn": 25}, {"ruleId": "2368", "severity": 1, "message": "2403", "line": 34, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 34, "endColumn": 23}, {"ruleId": "2376", "severity": 1, "message": "2404", "line": 73, "column": 31, "nodeType": "2370", "endLine": 73, "endColumn": 42}, {"ruleId": "2376", "severity": 1, "message": "2405", "line": 269, "column": 8, "nodeType": "2378", "endLine": 269, "endColumn": 18, "suggestions": "2406"}, {"ruleId": "2368", "severity": 1, "message": "2399", "line": 6, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2400", "line": 9, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 9, "endColumn": 14}, {"ruleId": "2368", "severity": 1, "message": "2401", "line": 18, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2402", "line": 20, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 20, "endColumn": 25}, {"ruleId": "2368", "severity": 1, "message": "2407", "line": 26, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 26, "endColumn": 26}, {"ruleId": "2368", "severity": 1, "message": "2408", "line": 1, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2408", "line": 1, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2409", "line": 8, "column": 37, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 48}, {"ruleId": "2368", "severity": 1, "message": "2410", "line": 11, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 11, "endColumn": 30}, {"ruleId": "2368", "severity": 1, "message": "2411", "line": 22, "column": 18, "nodeType": "2370", "messageId": "2371", "endLine": 22, "endColumn": 25}, {"ruleId": "2368", "severity": 1, "message": "2412", "line": 23, "column": 21, "nodeType": "2370", "messageId": "2371", "endLine": 23, "endColumn": 31}, {"ruleId": "2368", "severity": 1, "message": "2413", "line": 24, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 24, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2414", "line": 25, "column": 25, "nodeType": "2370", "messageId": "2371", "endLine": 25, "endColumn": 39}, {"ruleId": "2368", "severity": 1, "message": "2415", "line": 26, "column": 30, "nodeType": "2370", "messageId": "2371", "endLine": 26, "endColumn": 49}, {"ruleId": "2368", "severity": 1, "message": "2416", "line": 27, "column": 21, "nodeType": "2370", "messageId": "2371", "endLine": 27, "endColumn": 26}, {"ruleId": "2368", "severity": 1, "message": "2417", "line": 27, "column": 41, "nodeType": "2370", "messageId": "2371", "endLine": 27, "endColumn": 51}, {"ruleId": "2368", "severity": 1, "message": "2418", "line": 31, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 31, "endColumn": 30}, {"ruleId": "2368", "severity": 1, "message": "2419", "line": 34, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 34, "endColumn": 35}, {"ruleId": "2368", "severity": 1, "message": "2420", "line": 38, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 38, "endColumn": 33}, {"ruleId": "2368", "severity": 1, "message": "2421", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 21}, {"ruleId": "2376", "severity": 1, "message": "2422", "line": 25, "column": 8, "nodeType": "2378", "endLine": 25, "endColumn": 18, "suggestions": "2423"}, {"ruleId": "2368", "severity": 1, "message": "2424", "line": 15, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 15, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2384", "line": 15, "column": 24, "nodeType": "2370", "messageId": "2371", "endLine": 15, "endColumn": 36}, {"ruleId": "2368", "severity": 1, "message": "2408", "line": 1, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2396", "line": 2, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2425", "line": 6, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2426", "line": 7, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2427", "line": 8, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2418", "line": 25, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 25, "endColumn": 30}, {"ruleId": "2368", "severity": 1, "message": "2420", "line": 28, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 28, "endColumn": 33}, {"ruleId": "2368", "severity": 1, "message": "2428", "line": 31, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 31, "endColumn": 32}, {"ruleId": "2368", "severity": 1, "message": "2408", "line": 1, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2429", "line": 39, "column": 19, "nodeType": "2370", "messageId": "2371", "endLine": 39, "endColumn": 27}, {"ruleId": "2368", "severity": 1, "message": "2430", "line": 5, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2431", "line": 3, "column": 34, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 67}, {"ruleId": "2368", "severity": 1, "message": "2432", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2430", "line": 5, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 17}, {"ruleId": "2433", "severity": 1, "message": "2434", "line": 140, "column": 80, "nodeType": "2435", "messageId": "2436", "endLine": 140, "endColumn": 82}, {"ruleId": "2368", "severity": 1, "message": "2437", "line": 3, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2438", "line": 19, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 19, "endColumn": 22}, {"ruleId": "2376", "severity": 1, "message": "2439", "line": 18, "column": 8, "nodeType": "2378", "endLine": 18, "endColumn": 10, "suggestions": "2440"}, {"ruleId": "2368", "severity": 1, "message": "2441", "line": 1, "column": 20, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 29}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 3, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2442", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2443", "line": 12, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 12, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2444", "line": 13, "column": 36, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 45}, {"ruleId": "2368", "severity": 1, "message": "2445", "line": 14, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 14, "endColumn": 14}, {"ruleId": "2368", "severity": 1, "message": "2446", "line": 14, "column": 16, "nodeType": "2370", "messageId": "2371", "endLine": 14, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2447", "line": 29, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 29, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 3, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2448", "line": 5, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2449", "line": 10, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 10, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2450", "line": 18, "column": 36, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 46}, {"ruleId": "2376", "severity": 1, "message": "2451", "line": 43, "column": 8, "nodeType": "2378", "endLine": 43, "endColumn": 26, "suggestions": "2452"}, {"ruleId": "2368", "severity": 1, "message": "2441", "line": 2, "column": 20, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 29}, {"ruleId": "2368", "severity": 1, "message": "2453", "line": 7, "column": 47, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 68}, {"ruleId": "2368", "severity": 1, "message": "2454", "line": 17, "column": 7, "nodeType": "2370", "messageId": "2371", "endLine": 17, "endColumn": 16}, {"ruleId": "2368", "severity": 1, "message": "2455", "line": 29, "column": 7, "nodeType": "2370", "messageId": "2371", "endLine": 29, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2456", "line": 36, "column": 7, "nodeType": "2370", "messageId": "2371", "endLine": 36, "endColumn": 14}, {"ruleId": "2368", "severity": 1, "message": "2449", "line": 6, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2450", "line": 15, "column": 36, "nodeType": "2370", "messageId": "2371", "endLine": 15, "endColumn": 46}, {"ruleId": "2368", "severity": 1, "message": "2457", "line": 19, "column": 24, "nodeType": "2370", "messageId": "2371", "endLine": 19, "endColumn": 37}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 5, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2450", "line": 16, "column": 36, "nodeType": "2370", "messageId": "2371", "endLine": 16, "endColumn": 46}, {"ruleId": "2376", "severity": 1, "message": "2458", "line": 35, "column": 8, "nodeType": "2378", "endLine": 35, "endColumn": 53, "suggestions": "2459"}, {"ruleId": "2368", "severity": 1, "message": "2460", "line": 12, "column": 55, "nodeType": "2370", "messageId": "2371", "endLine": 12, "endColumn": 61}, {"ruleId": "2368", "severity": 1, "message": "2461", "line": 12, "column": 77, "nodeType": "2370", "messageId": "2371", "endLine": 12, "endColumn": 82}, {"ruleId": "2368", "severity": 1, "message": "2462", "line": 12, "column": 84, "nodeType": "2370", "messageId": "2371", "endLine": 12, "endColumn": 94}, {"ruleId": "2368", "severity": 1, "message": "2401", "line": 18, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2403", "line": 20, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 20, "endColumn": 23}, {"ruleId": "2376", "severity": 1, "message": "2404", "line": 39, "column": 31, "nodeType": "2370", "endLine": 39, "endColumn": 42}, {"ruleId": "2368", "severity": 1, "message": "2463", "line": 96, "column": 17, "nodeType": "2370", "messageId": "2371", "endLine": 96, "endColumn": 21}, {"ruleId": "2433", "severity": 1, "message": "2434", "line": 412, "column": 68, "nodeType": "2435", "messageId": "2436", "endLine": 412, "endColumn": 70}, {"ruleId": "2368", "severity": 1, "message": "2397", "line": 2, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2398", "line": 7, "column": 24, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 39}, {"ruleId": "2368", "severity": 1, "message": "2464", "line": 25, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 25, "endColumn": 26}, {"ruleId": "2368", "severity": 1, "message": "2465", "line": 26, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 26, "endColumn": 30}, {"ruleId": "2368", "severity": 1, "message": "2466", "line": 48, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 48, "endColumn": 22}, {"ruleId": "2376", "severity": 1, "message": "2467", "line": 69, "column": 8, "nodeType": "2378", "endLine": 69, "endColumn": 30, "suggestions": "2468"}, {"ruleId": "2368", "severity": 1, "message": "2469", "line": 79, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 79, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2470", "line": 87, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 87, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2471", "line": 8, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 28}, {"ruleId": "2376", "severity": 1, "message": "2472", "line": 45, "column": 8, "nodeType": "2378", "endLine": 45, "endColumn": 32, "suggestions": "2473"}, {"ruleId": "2368", "severity": 1, "message": "2474", "line": 6, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2475", "line": 7, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2399", "line": 13, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2400", "line": 14, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 14, "endColumn": 14}, {"ruleId": "2368", "severity": 1, "message": "2402", "line": 23, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 23, "endColumn": 25}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 3, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2476", "line": 5, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2475", "line": 6, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2471", "line": 7, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 28}, {"ruleId": "2376", "severity": 1, "message": "2477", "line": 57, "column": 8, "nodeType": "2378", "endLine": 57, "endColumn": 28, "suggestions": "2478"}, {"ruleId": "2368", "severity": 1, "message": "2420", "line": 71, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 71, "endColumn": 33}, {"ruleId": "2368", "severity": 1, "message": "2419", "line": 75, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 75, "endColumn": 35}, {"ruleId": "2368", "severity": 1, "message": "2428", "line": 79, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 79, "endColumn": 32}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 3, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2450", "line": 11, "column": 41, "nodeType": "2370", "messageId": "2371", "endLine": 11, "endColumn": 51}, {"ruleId": "2368", "severity": 1, "message": "2479", "line": 1, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 28}, {"ruleId": "2368", "severity": 1, "message": "2480", "line": 2, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2441", "line": 3, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 5, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2481", "line": 7, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 14}, {"ruleId": "2368", "severity": 1, "message": "2482", "line": 12, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 12, "endColumn": 31}, {"ruleId": "2368", "severity": 1, "message": "2483", "line": 1, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2484", "line": 2, "column": 37, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 45}, {"ruleId": "2368", "severity": 1, "message": "2485", "line": 2, "column": 47, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 60}, {"ruleId": "2368", "severity": 1, "message": "2486", "line": 2, "column": 62, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 67}, {"ruleId": "2368", "severity": 1, "message": "2487", "line": 2, "column": 69, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 77}, {"ruleId": "2368", "severity": 1, "message": "2488", "line": 3, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 26}, {"ruleId": "2376", "severity": 1, "message": "2489", "line": 36, "column": 8, "nodeType": "2378", "endLine": 36, "endColumn": 43, "suggestions": "2490"}, {"ruleId": "2368", "severity": 1, "message": "2483", "line": 1, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 5, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2383", "line": 7, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 18}, {"ruleId": "2376", "severity": 1, "message": "2491", "line": 25, "column": 8, "nodeType": "2378", "endLine": 25, "endColumn": 20, "suggestions": "2492"}, {"ruleId": "2368", "severity": 1, "message": "2493", "line": 1, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2487", "line": 7, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2461", "line": 8, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 10}, {"ruleId": "2368", "severity": 1, "message": "2484", "line": 9, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 9, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2485", "line": 10, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 10, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2494", "line": 11, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 11, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2495", "line": 12, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 12, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2496", "line": 13, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 16}, {"ruleId": "2368", "severity": 1, "message": "2497", "line": 22, "column": 7, "nodeType": "2370", "messageId": "2371", "endLine": 22, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2498", "line": 33, "column": 78, "nodeType": "2370", "messageId": "2371", "endLine": 33, "endColumn": 87}, {"ruleId": "2368", "severity": 1, "message": "2499", "line": 33, "column": 89, "nodeType": "2370", "messageId": "2371", "endLine": 33, "endColumn": 97}, {"ruleId": "2368", "severity": 1, "message": "2500", "line": 33, "column": 103, "nodeType": "2370", "messageId": "2371", "endLine": 33, "endColumn": 109}, {"ruleId": "2368", "severity": 1, "message": "2501", "line": 33, "column": 111, "nodeType": "2370", "messageId": "2371", "endLine": 33, "endColumn": 117}, {"ruleId": "2368", "severity": 1, "message": "2502", "line": 33, "column": 119, "nodeType": "2370", "messageId": "2371", "endLine": 33, "endColumn": 131}, {"ruleId": "2503", "severity": 1, "message": "2504", "line": 87, "column": 75, "nodeType": "2505", "messageId": "2506", "endLine": 87, "endColumn": 77}, {"ruleId": "2503", "severity": 1, "message": "2504", "line": 87, "column": 144, "nodeType": "2505", "messageId": "2506", "endLine": 87, "endColumn": 146}, {"ruleId": "2503", "severity": 1, "message": "2504", "line": 92, "column": 74, "nodeType": "2505", "messageId": "2506", "endLine": 92, "endColumn": 76}, {"ruleId": "2503", "severity": 1, "message": "2504", "line": 92, "column": 138, "nodeType": "2505", "messageId": "2506", "endLine": 92, "endColumn": 140}, {"ruleId": "2368", "severity": 1, "message": "2507", "line": 14, "column": 38, "nodeType": "2370", "messageId": "2371", "endLine": 14, "endColumn": 47}, {"ruleId": "2368", "severity": 1, "message": "2508", "line": 14, "column": 49, "nodeType": "2370", "messageId": "2371", "endLine": 14, "endColumn": 56}, {"ruleId": "2368", "severity": 1, "message": "2509", "line": 14, "column": 58, "nodeType": "2370", "messageId": "2371", "endLine": 14, "endColumn": 70}, {"ruleId": "2503", "severity": 1, "message": "2504", "line": 34, "column": 40, "nodeType": "2505", "messageId": "2506", "endLine": 34, "endColumn": 42}, {"ruleId": "2503", "severity": 1, "message": "2504", "line": 34, "column": 109, "nodeType": "2505", "messageId": "2506", "endLine": 34, "endColumn": 111}, {"ruleId": "2368", "severity": 1, "message": "2416", "line": 15, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 15, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2510", "line": 15, "column": 44, "nodeType": "2370", "messageId": "2371", "endLine": 15, "endColumn": 52}, {"ruleId": "2376", "severity": 1, "message": "2511", "line": 42, "column": 8, "nodeType": "2378", "endLine": 42, "endColumn": 40, "suggestions": "2512"}, {"ruleId": "2368", "severity": 1, "message": "2383", "line": 1, "column": 25, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 33}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 4, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2513", "line": 21, "column": 15, "nodeType": "2370", "messageId": "2371", "endLine": 21, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2381", "line": 1, "column": 20, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 26}, {"ruleId": "2368", "severity": 1, "message": "2514", "line": 10, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 10, "endColumn": 11}, {"ruleId": "2368", "severity": 1, "message": "2515", "line": 64, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 64, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2516", "line": 64, "column": 25, "nodeType": "2370", "messageId": "2371", "endLine": 64, "endColumn": 39}, {"ruleId": "2368", "severity": 1, "message": "2494", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2375", "line": 1, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2373", "line": 2, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 14}, {"ruleId": "2368", "severity": 1, "message": "2517", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2518", "line": 4, "column": 26, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 44}, {"ruleId": "2368", "severity": 1, "message": "2519", "line": 9, "column": 166, "nodeType": "2370", "messageId": "2371", "endLine": 9, "endColumn": 170}, {"ruleId": "2376", "severity": 1, "message": "2520", "line": 29, "column": 8, "nodeType": "2378", "endLine": 29, "endColumn": 10, "suggestions": "2521"}, {"ruleId": "2387", "severity": 1, "message": "2388", "line": 44, "column": 29, "nodeType": "2389", "endLine": 44, "endColumn": 98}, {"ruleId": "2522", "severity": 1, "message": "2523", "line": 205, "column": 24, "nodeType": "2524", "messageId": "2525", "endLine": 205, "endColumn": 25, "suggestions": "2526"}, {"ruleId": "2522", "severity": 1, "message": "2527", "line": 205, "column": 26, "nodeType": "2524", "messageId": "2525", "endLine": 205, "endColumn": 27, "suggestions": "2528"}, {"ruleId": "2522", "severity": 1, "message": "2523", "line": 207, "column": 27, "nodeType": "2524", "messageId": "2525", "endLine": 207, "endColumn": 28, "suggestions": "2529"}, {"ruleId": "2522", "severity": 1, "message": "2527", "line": 207, "column": 29, "nodeType": "2524", "messageId": "2525", "endLine": 207, "endColumn": 30, "suggestions": "2530"}, {"ruleId": "2522", "severity": 1, "message": "2531", "line": 254, "column": 30, "nodeType": "2524", "messageId": "2525", "endLine": 254, "endColumn": 31, "suggestions": "2532"}, {"ruleId": "2522", "severity": 1, "message": "2531", "line": 273, "column": 61, "nodeType": "2524", "messageId": "2525", "endLine": 273, "endColumn": 62, "suggestions": "2533"}, {"ruleId": "2522", "severity": 1, "message": "2531", "line": 293, "column": 29, "nodeType": "2524", "messageId": "2525", "endLine": 293, "endColumn": 30, "suggestions": "2534"}, {"ruleId": "2522", "severity": 1, "message": "2535", "line": 293, "column": 31, "nodeType": "2524", "messageId": "2525", "endLine": 293, "endColumn": 32, "suggestions": "2536"}, {"ruleId": "2522", "severity": 1, "message": "2531", "line": 294, "column": 51, "nodeType": "2524", "messageId": "2525", "endLine": 294, "endColumn": 52, "suggestions": "2537"}, {"ruleId": "2522", "severity": 1, "message": "2535", "line": 294, "column": 53, "nodeType": "2524", "messageId": "2525", "endLine": 294, "endColumn": 54, "suggestions": "2538"}, {"ruleId": "2522", "severity": 1, "message": "2531", "line": 394, "column": 30, "nodeType": "2524", "messageId": "2525", "endLine": 394, "endColumn": 31, "suggestions": "2539"}, {"ruleId": "2522", "severity": 1, "message": "2531", "line": 421, "column": 61, "nodeType": "2524", "messageId": "2525", "endLine": 421, "endColumn": 62, "suggestions": "2540"}, {"ruleId": "2522", "severity": 1, "message": "2531", "line": 441, "column": 29, "nodeType": "2524", "messageId": "2525", "endLine": 441, "endColumn": 30, "suggestions": "2541"}, {"ruleId": "2522", "severity": 1, "message": "2535", "line": 441, "column": 31, "nodeType": "2524", "messageId": "2525", "endLine": 441, "endColumn": 32, "suggestions": "2542"}, {"ruleId": "2522", "severity": 1, "message": "2531", "line": 442, "column": 51, "nodeType": "2524", "messageId": "2525", "endLine": 442, "endColumn": 52, "suggestions": "2543"}, {"ruleId": "2522", "severity": 1, "message": "2535", "line": 442, "column": 53, "nodeType": "2524", "messageId": "2525", "endLine": 442, "endColumn": 54, "suggestions": "2544"}, {"ruleId": "2522", "severity": 1, "message": "2531", "line": 530, "column": 30, "nodeType": "2524", "messageId": "2525", "endLine": 530, "endColumn": 31, "suggestions": "2545"}, {"ruleId": "2522", "severity": 1, "message": "2531", "line": 554, "column": 61, "nodeType": "2524", "messageId": "2525", "endLine": 554, "endColumn": 62, "suggestions": "2546"}, {"ruleId": "2376", "severity": 1, "message": "2547", "line": 89, "column": 8, "nodeType": "2378", "endLine": 89, "endColumn": 10, "suggestions": "2548"}, {"ruleId": "2387", "severity": 1, "message": "2388", "line": 56, "column": 37, "nodeType": "2389", "endLine": 56, "endColumn": 107}, {"ruleId": "2368", "severity": 1, "message": "2549", "line": 3, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 27}, {"ruleId": "2376", "severity": 1, "message": "2550", "line": 73, "column": 8, "nodeType": "2378", "endLine": 73, "endColumn": 25, "suggestions": "2551"}, {"ruleId": "2368", "severity": 1, "message": "2441", "line": 3, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2552", "line": 4, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2553", "line": 6, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 16}, {"ruleId": "2368", "severity": 1, "message": "2554", "line": 6, "column": 18, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 33}, {"ruleId": "2368", "severity": 1, "message": "2555", "line": 13, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 26}, {"ruleId": "2368", "severity": 1, "message": "2556", "line": 13, "column": 28, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 45}, {"ruleId": "2368", "severity": 1, "message": "2557", "line": 29, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 29, "endColumn": 20}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 116, "column": 56, "nodeType": "2435", "messageId": "2436", "endLine": 116, "endColumn": 58}, {"ruleId": "2368", "severity": 1, "message": "2480", "line": 2, "column": 23, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 34}, {"ruleId": "2368", "severity": 1, "message": "2559", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 28}, {"ruleId": "2368", "severity": 1, "message": "2560", "line": 5, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2561", "line": 5, "column": 33, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 40}, {"ruleId": "2368", "severity": 1, "message": "2562", "line": 5, "column": 42, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 51}, {"ruleId": "2368", "severity": 1, "message": "2563", "line": 8, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 21}, {"ruleId": "2564", "severity": 1, "message": "2565", "line": 12, "column": 25, "nodeType": "2389", "endLine": 12, "endColumn": 614}, {"ruleId": "2368", "severity": 1, "message": "2483", "line": 1, "column": 46, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 49}, {"ruleId": "2368", "severity": 1, "message": "2566", "line": 3, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2567", "line": 15, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 15, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2568", "line": 15, "column": 24, "nodeType": "2370", "messageId": "2371", "endLine": 15, "endColumn": 37}, {"ruleId": "2376", "severity": 1, "message": "2569", "line": 21, "column": 8, "nodeType": "2378", "endLine": 21, "endColumn": 26, "suggestions": "2570"}, {"ruleId": "2376", "severity": 1, "message": "2571", "line": 40, "column": 8, "nodeType": "2378", "endLine": 40, "endColumn": 10, "suggestions": "2572"}, {"ruleId": "2368", "severity": 1, "message": "2390", "line": 2, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 11}, {"ruleId": "2368", "severity": 1, "message": "2397", "line": 3, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2573", "line": 13, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 23}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 290, "column": 56, "nodeType": "2435", "messageId": "2436", "endLine": 290, "endColumn": 58}, {"ruleId": "2368", "severity": 1, "message": "2574", "line": 15, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 15, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2575", "line": 19, "column": 22, "nodeType": "2370", "messageId": "2371", "endLine": 19, "endColumn": 29}, {"ruleId": "2368", "severity": 1, "message": "2552", "line": 4, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2576", "line": 5, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 16}, {"ruleId": "2368", "severity": 1, "message": "2577", "line": 100, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 100, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2578", "line": 111, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 111, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2443", "line": 15, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 15, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2427", "line": 7, "column": 24, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 36}, {"ruleId": "2368", "severity": 1, "message": "2443", "line": 13, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2443", "line": 14, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 14, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2427", "line": 7, "column": 24, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 36}, {"ruleId": "2368", "severity": 1, "message": "2443", "line": 13, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2427", "line": 7, "column": 24, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 36}, {"ruleId": "2579", "severity": 1, "message": "2580", "line": 28, "column": 74, "nodeType": "2435", "messageId": "2581", "endLine": 28, "endColumn": 75}, {"ruleId": "2368", "severity": 1, "message": "2582", "line": 6, "column": 25, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 29}, {"ruleId": "2368", "severity": 1, "message": "2583", "line": 6, "column": 31, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 37}, {"ruleId": "2368", "severity": 1, "message": "2462", "line": 6, "column": 39, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 49}, {"ruleId": "2368", "severity": 1, "message": "2486", "line": 6, "column": 51, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 56}, {"ruleId": "2368", "severity": 1, "message": "2584", "line": 6, "column": 65, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 70}, {"ruleId": "2368", "severity": 1, "message": "2585", "line": 34, "column": 53, "nodeType": "2370", "messageId": "2371", "endLine": 34, "endColumn": 62}, {"ruleId": "2368", "severity": 1, "message": "2586", "line": 35, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 35, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2587", "line": 43, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 43, "endColumn": 27}, {"ruleId": "2376", "severity": 1, "message": "2588", "line": 154, "column": 8, "nodeType": "2378", "endLine": 154, "endColumn": 120, "suggestions": "2589"}, {"ruleId": "2368", "severity": 1, "message": "2513", "line": 162, "column": 19, "nodeType": "2370", "messageId": "2371", "endLine": 162, "endColumn": 25}, {"ruleId": "2368", "severity": 1, "message": "2590", "line": 2, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2591", "line": 4, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 14}, {"ruleId": "2368", "severity": 1, "message": "2592", "line": 18, "column": 25, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 29}, {"ruleId": "2368", "severity": 1, "message": "2593", "line": 18, "column": 137, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 146}, {"ruleId": "2368", "severity": 1, "message": "2594", "line": 18, "column": 148, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 161}, {"ruleId": "2368", "severity": 1, "message": "2595", "line": 18, "column": 163, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 173}, {"ruleId": "2368", "severity": 1, "message": "2596", "line": 18, "column": 175, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 182}, {"ruleId": "2368", "severity": 1, "message": "2597", "line": 18, "column": 184, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 189}, {"ruleId": "2368", "severity": 1, "message": "2598", "line": 18, "column": 191, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 205}, {"ruleId": "2368", "severity": 1, "message": "2599", "line": 34, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 34, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2600", "line": 39, "column": 7, "nodeType": "2370", "messageId": "2371", "endLine": 39, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2601", "line": 40, "column": 7, "nodeType": "2370", "messageId": "2371", "endLine": 40, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2602", "line": 64, "column": 7, "nodeType": "2370", "messageId": "2371", "endLine": 64, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2603", "line": 65, "column": 7, "nodeType": "2370", "messageId": "2371", "endLine": 65, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2604", "line": 67, "column": 7, "nodeType": "2370", "messageId": "2371", "endLine": 67, "endColumn": 17}, {"ruleId": "2376", "severity": 1, "message": "2605", "line": 197, "column": 8, "nodeType": "2378", "endLine": 197, "endColumn": 22, "suggestions": "2606"}, {"ruleId": "2607", "severity": 1, "message": "2608", "line": 205, "column": 91, "nodeType": "2609", "messageId": "2436", "endLine": 205, "endColumn": 95}, {"ruleId": "2376", "severity": 1, "message": "2610", "line": 206, "column": 8, "nodeType": "2378", "endLine": 206, "endColumn": 112, "suggestions": "2611"}, {"ruleId": "2376", "severity": 1, "message": "2612", "line": 299, "column": 8, "nodeType": "2378", "endLine": 299, "endColumn": 18, "suggestions": "2613"}, {"ruleId": "2368", "severity": 1, "message": "2614", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 35}, {"ruleId": "2368", "severity": 1, "message": "2615", "line": 4, "column": 58, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 73}, {"ruleId": "2368", "severity": 1, "message": "2616", "line": 4, "column": 102, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 117}, {"ruleId": "2368", "severity": 1, "message": "2617", "line": 4, "column": 119, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 126}, {"ruleId": "2368", "severity": 1, "message": "2381", "line": 6, "column": 31, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 37}, {"ruleId": "2368", "severity": 1, "message": "2618", "line": 8, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2619", "line": 14, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 14, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2620", "line": 17, "column": 15, "nodeType": "2370", "messageId": "2371", "endLine": 17, "endColumn": 25}, {"ruleId": "2368", "severity": 1, "message": "2621", "line": 18, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 8}, {"ruleId": "2368", "severity": 1, "message": "2622", "line": 26, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 26, "endColumn": 9}, {"ruleId": "2368", "severity": 1, "message": "2623", "line": 27, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 27, "endColumn": 10}, {"ruleId": "2368", "severity": 1, "message": "2624", "line": 28, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 28, "endColumn": 11}, {"ruleId": "2368", "severity": 1, "message": "2625", "line": 35, "column": 73, "nodeType": "2370", "messageId": "2371", "endLine": 35, "endColumn": 87}, {"ruleId": "2368", "severity": 1, "message": "2401", "line": 48, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 48, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2626", "line": 49, "column": 19, "nodeType": "2370", "messageId": "2371", "endLine": 49, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2627", "line": 320, "column": 48, "nodeType": "2370", "messageId": "2371", "endLine": 320, "endColumn": 52}, {"ruleId": "2368", "severity": 1, "message": "2401", "line": 349, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 349, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2628", "line": 350, "column": 19, "nodeType": "2370", "messageId": "2371", "endLine": 350, "endColumn": 26}, {"ruleId": "2368", "severity": 1, "message": "2629", "line": 351, "column": 23, "nodeType": "2370", "messageId": "2371", "endLine": 351, "endColumn": 33}, {"ruleId": "2368", "severity": 1, "message": "2630", "line": 355, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 355, "endColumn": 25}, {"ruleId": "2368", "severity": 1, "message": "2628", "line": 394, "column": 19, "nodeType": "2370", "messageId": "2371", "endLine": 394, "endColumn": 26}, {"ruleId": "2368", "severity": 1, "message": "2631", "line": 394, "column": 52, "nodeType": "2370", "messageId": "2371", "endLine": 394, "endColumn": 75}, {"ruleId": "2376", "severity": 1, "message": "2632", "line": 471, "column": 8, "nodeType": "2378", "endLine": 471, "endColumn": 48, "suggestions": "2633"}, {"ruleId": "2368", "severity": 1, "message": "2552", "line": 5, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2634", "line": 6, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2487", "line": 12, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 12, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2486", "line": 13, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 10}, {"ruleId": "2368", "severity": 1, "message": "2635", "line": 14, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 14, "endColumn": 11}, {"ruleId": "2368", "severity": 1, "message": "2636", "line": 16, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 16, "endColumn": 11}, {"ruleId": "2368", "severity": 1, "message": "2637", "line": 17, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 17, "endColumn": 16}, {"ruleId": "2368", "severity": 1, "message": "2638", "line": 79, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 79, "endColumn": 28}, {"ruleId": "2368", "severity": 1, "message": "2639", "line": 80, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 80, "endColumn": 18}, {"ruleId": "2376", "severity": 1, "message": "2640", "line": 86, "column": 8, "nodeType": "2378", "endLine": 86, "endColumn": 18, "suggestions": "2641"}, {"ruleId": "2368", "severity": 1, "message": "2642", "line": 3, "column": 48, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 64}, {"ruleId": "2368", "severity": 1, "message": "2401", "line": 24, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 24, "endColumn": 19}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 140, "column": 37, "nodeType": "2435", "messageId": "2436", "endLine": 140, "endColumn": 39}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 142, "column": 63, "nodeType": "2435", "messageId": "2436", "endLine": 142, "endColumn": 65}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 145, "column": 71, "nodeType": "2435", "messageId": "2436", "endLine": 145, "endColumn": 73}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 153, "column": 61, "nodeType": "2435", "messageId": "2436", "endLine": 153, "endColumn": 63}, {"ruleId": "2376", "severity": 1, "message": "2643", "line": 160, "column": 8, "nodeType": "2378", "endLine": 160, "endColumn": 76, "suggestions": "2644"}, {"ruleId": "2376", "severity": 1, "message": "2645", "line": 222, "column": 8, "nodeType": "2378", "endLine": 222, "endColumn": 21, "suggestions": "2646"}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 89, "column": 37, "nodeType": "2435", "messageId": "2436", "endLine": 89, "endColumn": 39}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 91, "column": 44, "nodeType": "2435", "messageId": "2436", "endLine": 91, "endColumn": 46}, {"ruleId": "2368", "severity": 1, "message": "2635", "line": 8, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 16}, {"ruleId": "2368", "severity": 1, "message": "2390", "line": 8, "column": 18, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2487", "line": 8, "column": 21, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 29}, {"ruleId": "2368", "severity": 1, "message": "2647", "line": 15, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 15, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2648", "line": 16, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 16, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2649", "line": 26, "column": 24, "nodeType": "2370", "messageId": "2371", "endLine": 26, "endColumn": 37}, {"ruleId": "2368", "severity": 1, "message": "2650", "line": 111, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 111, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 7, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2400", "line": 13, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 9}, {"ruleId": "2368", "severity": 1, "message": "2484", "line": 17, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 17, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2651", "line": 19, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 19, "endColumn": 13}, {"ruleId": "2376", "severity": 1, "message": "2652", "line": 129, "column": 8, "nodeType": "2378", "endLine": 129, "endColumn": 18, "suggestions": "2653"}, {"ruleId": "2368", "severity": 1, "message": "2654", "line": 155, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 155, "endColumn": 32}, {"ruleId": "2368", "severity": 1, "message": "2655", "line": 232, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 232, "endColumn": 29}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 7, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 22}, {"ruleId": "2376", "severity": 1, "message": "2656", "line": 40, "column": 8, "nodeType": "2378", "endLine": 40, "endColumn": 30, "suggestions": "2657"}, {"ruleId": "2387", "severity": 1, "message": "2388", "line": 223, "column": 69, "nodeType": "2389", "endLine": 227, "endColumn": 71}, {"ruleId": "2433", "severity": 1, "message": "2434", "line": 49, "column": 108, "nodeType": "2435", "messageId": "2436", "endLine": 49, "endColumn": 110}, {"ruleId": "2368", "severity": 1, "message": "2658", "line": 7, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 29}, {"ruleId": "2368", "severity": 1, "message": "2659", "line": 8, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 12}, {"ruleId": "2368", "severity": 1, "message": "2660", "line": 13, "column": 3, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2556", "line": 14, "column": 3, "nodeType": "2370", "messageId": "2371", "endLine": 14, "endColumn": 20}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 285, "column": 65, "nodeType": "2435", "messageId": "2436", "endLine": 285, "endColumn": 67}, {"ruleId": "2368", "severity": 1, "message": "2483", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2661", "line": 9, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 9, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 10, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 10, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2662", "line": 11, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 11, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2663", "line": 24, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 24, "endColumn": 14}, {"ruleId": "2368", "severity": 1, "message": "2664", "line": 32, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 32, "endColumn": 20}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 199, "column": 91, "nodeType": "2435", "messageId": "2436", "endLine": 199, "endColumn": 93}, {"ruleId": "2368", "severity": 1, "message": "2665", "line": 468, "column": 7, "nodeType": "2370", "messageId": "2371", "endLine": 468, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2666", "line": 674, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 674, "endColumn": 30}, {"ruleId": "2368", "severity": 1, "message": "2667", "line": 55, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 55, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2629", "line": 55, "column": 32, "nodeType": "2370", "messageId": "2371", "endLine": 55, "endColumn": 42}, {"ruleId": "2368", "severity": 1, "message": "2668", "line": 59, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 59, "endColumn": 27}, {"ruleId": "2368", "severity": 1, "message": "2669", "line": 70, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 70, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2670", "line": 82, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 82, "endColumn": 29}, {"ruleId": "2368", "severity": 1, "message": "2671", "line": 88, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 88, "endColumn": 30}, {"ruleId": "2368", "severity": 1, "message": "2654", "line": 94, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 94, "endColumn": 32}, {"ruleId": "2433", "severity": 1, "message": "2434", "line": 336, "column": 35, "nodeType": "2435", "messageId": "2436", "endLine": 336, "endColumn": 37}, {"ruleId": "2368", "severity": 1, "message": "2449", "line": 9, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 9, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2672", "line": 11, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 11, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2460", "line": 19, "column": 3, "nodeType": "2370", "messageId": "2371", "endLine": 19, "endColumn": 9}, {"ruleId": "2368", "severity": 1, "message": "2450", "line": 31, "column": 30, "nodeType": "2370", "messageId": "2371", "endLine": 31, "endColumn": 40}, {"ruleId": "2368", "severity": 1, "message": "2673", "line": 4, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2674", "line": 5, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 14}, {"ruleId": "2368", "severity": 1, "message": "2675", "line": 17, "column": 26, "nodeType": "2370", "messageId": "2371", "endLine": 17, "endColumn": 46}, {"ruleId": "2368", "severity": 1, "message": "2676", "line": 24, "column": 96, "nodeType": "2370", "messageId": "2371", "endLine": 24, "endColumn": 100}, {"ruleId": "2368", "severity": 1, "message": "2460", "line": 24, "column": 102, "nodeType": "2370", "messageId": "2371", "endLine": 24, "endColumn": 108}, {"ruleId": "2368", "severity": 1, "message": "2396", "line": 26, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 26, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2677", "line": 27, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 27, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2401", "line": 34, "column": 9, "nodeType": "2370", "messageId": "2371", "endLine": 34, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2628", "line": 35, "column": 47, "nodeType": "2370", "messageId": "2371", "endLine": 35, "endColumn": 54}, {"ruleId": "2368", "severity": 1, "message": "2450", "line": 37, "column": 17, "nodeType": "2370", "messageId": "2371", "endLine": 37, "endColumn": 27}, {"ruleId": "2368", "severity": 1, "message": "2678", "line": 91, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 91, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2679", "line": 92, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 92, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2680", "line": 98, "column": 20, "nodeType": "2370", "messageId": "2371", "endLine": 98, "endColumn": 31}, {"ruleId": "2368", "severity": 1, "message": "2681", "line": 99, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 99, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2682", "line": 99, "column": 22, "nodeType": "2370", "messageId": "2371", "endLine": 99, "endColumn": 35}, {"ruleId": "2368", "severity": 1, "message": "2683", "line": 100, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 100, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2684", "line": 100, "column": 20, "nodeType": "2370", "messageId": "2371", "endLine": 100, "endColumn": 31}, {"ruleId": "2368", "severity": 1, "message": "2685", "line": 103, "column": 9, "nodeType": "2370", "messageId": "2371", "endLine": 103, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2686", "line": 104, "column": 9, "nodeType": "2370", "messageId": "2371", "endLine": 104, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2687", "line": 105, "column": 9, "nodeType": "2370", "messageId": "2371", "endLine": 105, "endColumn": 29}, {"ruleId": "2368", "severity": 1, "message": "2688", "line": 106, "column": 9, "nodeType": "2370", "messageId": "2371", "endLine": 106, "endColumn": 27}, {"ruleId": "2368", "severity": 1, "message": "2689", "line": 136, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 136, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2690", "line": 137, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 137, "endColumn": 27}, {"ruleId": "2368", "severity": 1, "message": "2691", "line": 139, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 139, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2692", "line": 139, "column": 25, "nodeType": "2370", "messageId": "2371", "endLine": 139, "endColumn": 41}, {"ruleId": "2368", "severity": 1, "message": "2693", "line": 167, "column": 9, "nodeType": "2370", "messageId": "2371", "endLine": 167, "endColumn": 25}, {"ruleId": "2368", "severity": 1, "message": "2694", "line": 173, "column": 9, "nodeType": "2370", "messageId": "2371", "endLine": 173, "endColumn": 33}, {"ruleId": "2376", "severity": 1, "message": "2695", "line": 286, "column": 6, "nodeType": "2378", "endLine": 286, "endColumn": 32, "suggestions": "2696"}, {"ruleId": "2368", "severity": 1, "message": "2697", "line": 364, "column": 9, "nodeType": "2370", "messageId": "2371", "endLine": 364, "endColumn": 23}, {"ruleId": "2376", "severity": 1, "message": "2698", "line": 742, "column": 6, "nodeType": "2378", "endLine": 742, "endColumn": 25, "suggestions": "2699"}, {"ruleId": "2368", "severity": 1, "message": "2700", "line": 764, "column": 9, "nodeType": "2370", "messageId": "2371", "endLine": 764, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2701", "line": 776, "column": 9, "nodeType": "2370", "messageId": "2371", "endLine": 776, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2672", "line": 8, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2461", "line": 19, "column": 3, "nodeType": "2370", "messageId": "2371", "endLine": 19, "endColumn": 8}, {"ruleId": "2368", "severity": 1, "message": "2702", "line": 20, "column": 3, "nodeType": "2370", "messageId": "2371", "endLine": 20, "endColumn": 10}, {"ruleId": "2368", "severity": 1, "message": "2703", "line": 30, "column": 36, "nodeType": "2370", "messageId": "2371", "endLine": 30, "endColumn": 56}, {"ruleId": "2368", "severity": 1, "message": "2704", "line": 32, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 32, "endColumn": 30}, {"ruleId": "2368", "severity": 1, "message": "2705", "line": 32, "column": 32, "nodeType": "2370", "messageId": "2371", "endLine": 32, "endColumn": 55}, {"ruleId": "2368", "severity": 1, "message": "2706", "line": 2, "column": 26, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 32}, {"ruleId": "2376", "severity": 1, "message": "2404", "line": 35, "column": 29, "nodeType": "2370", "endLine": 35, "endColumn": 40}, {"ruleId": "2376", "severity": 1, "message": "2404", "line": 35, "column": 30, "nodeType": "2370", "endLine": 35, "endColumn": 41}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 347, "column": 68, "nodeType": "2435", "messageId": "2436", "endLine": 347, "endColumn": 70}, {"ruleId": "2433", "severity": 1, "message": "2434", "line": 360, "column": 64, "nodeType": "2435", "messageId": "2436", "endLine": 360, "endColumn": 66}, {"ruleId": "2368", "severity": 1, "message": "2483", "line": 1, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2707", "line": 12, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 12, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2399", "line": 24, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 24, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2400", "line": 26, "column": 119, "nodeType": "2370", "messageId": "2371", "endLine": 26, "endColumn": 123}, {"ruleId": "2368", "severity": 1, "message": "2708", "line": 26, "column": 135, "nodeType": "2370", "messageId": "2371", "endLine": 26, "endColumn": 140}, {"ruleId": "2368", "severity": 1, "message": "2401", "line": 99, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 99, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2450", "line": 107, "column": 9, "nodeType": "2370", "messageId": "2371", "endLine": 107, "endColumn": 19}, {"ruleId": "2376", "severity": 1, "message": "2605", "line": 217, "column": 8, "nodeType": "2378", "endLine": 217, "endColumn": 38, "suggestions": "2709"}, {"ruleId": "2376", "severity": 1, "message": "2710", "line": 67, "column": 8, "nodeType": "2378", "endLine": 67, "endColumn": 47, "suggestions": "2711"}, {"ruleId": "2368", "severity": 1, "message": "2408", "line": 4, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2460", "line": 17, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 17, "endColumn": 11}, {"ruleId": "2368", "severity": 1, "message": "2391", "line": 20, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 20, "endColumn": 9}, {"ruleId": "2368", "severity": 1, "message": "2712", "line": 27, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 27, "endColumn": 32}, {"ruleId": "2376", "severity": 1, "message": "2713", "line": 122, "column": 8, "nodeType": "2378", "endLine": 122, "endColumn": 10, "suggestions": "2714"}, {"ruleId": "2368", "severity": 1, "message": "2487", "line": 6, "column": 56, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 64}, {"ruleId": "2368", "severity": 1, "message": "2583", "line": 6, "column": 73, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 79}, {"ruleId": "2368", "severity": 1, "message": "2462", "line": 6, "column": 81, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 91}, {"ruleId": "2368", "severity": 1, "message": "2715", "line": 5, "column": 60, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 66}, {"ruleId": "2368", "severity": 1, "message": "2635", "line": 5, "column": 68, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 74}, {"ruleId": "2368", "severity": 1, "message": "2716", "line": 2, "column": 42, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 51}, {"ruleId": "2368", "severity": 1, "message": "2717", "line": 5, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 8, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2628", "line": 16, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 16, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2718", "line": 17, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 17, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2383", "line": 1, "column": 21, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 29}, {"ruleId": "2607", "severity": 1, "message": "2719", "line": 110, "column": 5, "nodeType": "2609", "messageId": "2436", "endLine": 110, "endColumn": 14}, {"ruleId": "2368", "severity": 1, "message": "2720", "line": 151, "column": 33, "nodeType": "2370", "messageId": "2371", "endLine": 151, "endColumn": 47}, {"ruleId": "2721", "severity": 1, "message": "2722", "line": 167, "column": 17, "nodeType": "2723", "messageId": "2724", "endLine": 177, "endColumn": 18}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 280, "column": 50, "nodeType": "2435", "messageId": "2436", "endLine": 280, "endColumn": 52}, {"ruleId": "2368", "severity": 1, "message": "2725", "line": 422, "column": 27, "nodeType": "2370", "messageId": "2371", "endLine": 422, "endColumn": 37}, {"ruleId": "2368", "severity": 1, "message": "2496", "line": 3, "column": 58, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 69}, {"ruleId": "2368", "severity": 1, "message": "2726", "line": 3, "column": 71, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 81}, {"ruleId": "2368", "severity": 1, "message": "2727", "line": 48, "column": 9, "nodeType": "2370", "messageId": "2371", "endLine": 48, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2728", "line": 56, "column": 9, "nodeType": "2370", "messageId": "2371", "endLine": 56, "endColumn": 29}, {"ruleId": "2376", "severity": 1, "message": "2404", "line": 37, "column": 30, "nodeType": "2370", "endLine": 37, "endColumn": 41}, {"ruleId": "2376", "severity": 1, "message": "2404", "line": 35, "column": 32, "nodeType": "2370", "endLine": 35, "endColumn": 43}, {"ruleId": "2368", "severity": 1, "message": "2487", "line": 3, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2461", "line": 3, "column": 20, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 25}, {"ruleId": "2368", "severity": 1, "message": "2729", "line": 3, "column": 27, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 38}, {"ruleId": "2368", "severity": 1, "message": "2494", "line": 3, "column": 40, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 52}, {"ruleId": "2368", "severity": 1, "message": "2496", "line": 3, "column": 54, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 65}, {"ruleId": "2368", "severity": 1, "message": "2730", "line": 6, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 32}, {"ruleId": "2368", "severity": 1, "message": "2731", "line": 8, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2732", "line": 8, "column": 25, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 39}, {"ruleId": "2368", "severity": 1, "message": "2383", "line": 2, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2487", "line": 3, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2729", "line": 3, "column": 27, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 38}, {"ruleId": "2368", "severity": 1, "message": "2494", "line": 3, "column": 40, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 52}, {"ruleId": "2368", "severity": 1, "message": "2733", "line": 4, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2734", "line": 5, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 30}, {"ruleId": "2368", "severity": 1, "message": "2735", "line": 7, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2731", "line": 8, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2736", "line": 9, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 9, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2628", "line": 15, "column": 39, "nodeType": "2370", "messageId": "2371", "endLine": 15, "endColumn": 46}, {"ruleId": "2368", "severity": 1, "message": "2573", "line": 17, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 17, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2737", "line": 18, "column": 37, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 48}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 181, "column": 54, "nodeType": "2435", "messageId": "2436", "endLine": 181, "endColumn": 56}, {"ruleId": "2376", "severity": 1, "message": "2738", "line": 57, "column": 8, "nodeType": "2378", "endLine": 57, "endColumn": 38, "suggestions": "2739"}, {"ruleId": "2368", "severity": 1, "message": "2483", "line": 1, "column": 17, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2740", "line": 2, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 26}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 186, "column": 50, "nodeType": "2435", "messageId": "2436", "endLine": 186, "endColumn": 52}, {"ruleId": "2368", "severity": 1, "message": "2741", "line": 79, "column": 29, "nodeType": "2370", "messageId": "2371", "endLine": 79, "endColumn": 45}, {"ruleId": "2368", "severity": 1, "message": "2742", "line": 79, "column": 47, "nodeType": "2370", "messageId": "2371", "endLine": 79, "endColumn": 59}, {"ruleId": "2368", "severity": 1, "message": "2743", "line": 3, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2744", "line": 3, "column": 22, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 29}, {"ruleId": "2368", "severity": 1, "message": "2745", "line": 3, "column": 31, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 38}, {"ruleId": "2368", "severity": 1, "message": "2746", "line": 3, "column": 40, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 50}, {"ruleId": "2368", "severity": 1, "message": "2747", "line": 7, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2748", "line": 7, "column": 19, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 27}, {"ruleId": "2368", "severity": 1, "message": "2383", "line": 1, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2483", "line": 1, "column": 31, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 34}, {"ruleId": "2368", "severity": 1, "message": "2749", "line": 75, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 75, "endColumn": 28}, {"ruleId": "2368", "severity": 1, "message": "2628", "line": 75, "column": 30, "nodeType": "2370", "messageId": "2371", "endLine": 75, "endColumn": 37}, {"ruleId": "2368", "severity": 1, "message": "2629", "line": 75, "column": 39, "nodeType": "2370", "messageId": "2371", "endLine": 75, "endColumn": 49}, {"ruleId": "2368", "severity": 1, "message": "2444", "line": 13, "column": 45, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 54}, {"ruleId": "2368", "severity": 1, "message": "2750", "line": 66, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 66, "endColumn": 25}, {"ruleId": "2368", "severity": 1, "message": "2417", "line": 67, "column": 26, "nodeType": "2370", "messageId": "2371", "endLine": 67, "endColumn": 36}, {"ruleId": "2368", "severity": 1, "message": "2751", "line": 11, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 11, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2444", "line": 96, "column": 29, "nodeType": "2370", "messageId": "2371", "endLine": 96, "endColumn": 38}, {"ruleId": "2368", "severity": 1, "message": "2752", "line": 166, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 166, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2383", "line": 1, "column": 17, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 25}, {"ruleId": "2368", "severity": 1, "message": "2753", "line": 7, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2754", "line": 7, "column": 19, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 30}, {"ruleId": "2368", "severity": 1, "message": "2755", "line": 7, "column": 32, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 40}, {"ruleId": "2368", "severity": 1, "message": "2756", "line": 7, "column": 42, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 50}, {"ruleId": "2368", "severity": 1, "message": "2757", "line": 7, "column": 52, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 60}, {"ruleId": "2368", "severity": 1, "message": "2758", "line": 7, "column": 76, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 90}, {"ruleId": "2368", "severity": 1, "message": "2475", "line": 8, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2471", "line": 9, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 9, "endColumn": 28}, {"ruleId": "2368", "severity": 1, "message": "2759", "line": 10, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 10, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2760", "line": 11, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 11, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2476", "line": 12, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 12, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 13, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2391", "line": 17, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 17, "endColumn": 9}, {"ruleId": "2368", "severity": 1, "message": "2761", "line": 18, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 8}, {"ruleId": "2368", "severity": 1, "message": "2619", "line": 19, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 19, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2762", "line": 20, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 20, "endColumn": 9}, {"ruleId": "2368", "severity": 1, "message": "2763", "line": 21, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 21, "endColumn": 11}, {"ruleId": "2368", "severity": 1, "message": "2764", "line": 22, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 22, "endColumn": 9}, {"ruleId": "2368", "severity": 1, "message": "2496", "line": 23, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 23, "endColumn": 16}, {"ruleId": "2368", "severity": 1, "message": "2494", "line": 24, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 24, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2729", "line": 25, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 25, "endColumn": 16}, {"ruleId": "2368", "severity": 1, "message": "2461", "line": 26, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 26, "endColumn": 10}, {"ruleId": "2368", "severity": 1, "message": "2486", "line": 27, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 27, "endColumn": 10}, {"ruleId": "2368", "severity": 1, "message": "2484", "line": 28, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 28, "endColumn": 13}, {"ruleId": "2368", "severity": 1, "message": "2765", "line": 29, "column": 14, "nodeType": "2370", "messageId": "2371", "endLine": 29, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2766", "line": 30, "column": 5, "nodeType": "2370", "messageId": "2371", "endLine": 30, "endColumn": 11}, {"ruleId": "2368", "severity": 1, "message": "2767", "line": 32, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 32, "endColumn": 19}, {"ruleId": "2376", "severity": 1, "message": "2422", "line": 52, "column": 8, "nodeType": "2378", "endLine": 52, "endColumn": 18, "suggestions": "2768"}, {"ruleId": "2368", "severity": 1, "message": "2395", "line": 54, "column": 123, "nodeType": "2370", "messageId": "2371", "endLine": 54, "endColumn": 132}, {"ruleId": "2368", "severity": 1, "message": "2769", "line": 7, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 26}, {"ruleId": "2368", "severity": 1, "message": "2770", "line": 8, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 26}, {"ruleId": "2368", "severity": 1, "message": "2432", "line": 9, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 9, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2771", "line": 97, "column": 23, "nodeType": "2370", "messageId": "2371", "endLine": 97, "endColumn": 32}, {"ruleId": "2368", "severity": 1, "message": "2772", "line": 97, "column": 34, "nodeType": "2370", "messageId": "2371", "endLine": 97, "endColumn": 42}, {"ruleId": "2368", "severity": 1, "message": "2773", "line": 332, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 332, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2763", "line": 26, "column": 137, "nodeType": "2370", "messageId": "2371", "endLine": 26, "endColumn": 143}, {"ruleId": "2368", "severity": 1, "message": "2774", "line": 26, "column": 145, "nodeType": "2370", "messageId": "2371", "endLine": 26, "endColumn": 149}, {"ruleId": "2368", "severity": 1, "message": "2775", "line": 32, "column": 43, "nodeType": "2370", "messageId": "2371", "endLine": 32, "endColumn": 63}, {"ruleId": "2368", "severity": 1, "message": "2662", "line": 35, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 35, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2476", "line": 37, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 37, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2772", "line": 42, "column": 34, "nodeType": "2370", "messageId": "2371", "endLine": 42, "endColumn": 42}, {"ruleId": "2387", "severity": 1, "message": "2388", "line": 457, "column": 33, "nodeType": "2389", "endLine": 461, "endColumn": 35}, {"ruleId": "2368", "severity": 1, "message": "2776", "line": 484, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 484, "endColumn": 25}, {"ruleId": "2368", "severity": 1, "message": "2577", "line": 485, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 485, "endColumn": 19}, {"ruleId": "2376", "severity": 1, "message": "2777", "line": 498, "column": 8, "nodeType": "2378", "endLine": 498, "endColumn": 118, "suggestions": "2778"}, {"ruleId": "2376", "severity": 1, "message": "2779", "line": 17, "column": 8, "nodeType": "2378", "endLine": 17, "endColumn": 24, "suggestions": "2780"}, {"ruleId": "2376", "severity": 1, "message": "2781", "line": 17, "column": 9, "nodeType": "2782", "endLine": 17, "endColumn": 16}, {"ruleId": "2368", "severity": 1, "message": "2441", "line": 1, "column": 35, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 44}, {"ruleId": "2368", "severity": 1, "message": "2766", "line": 2, "column": 21, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 27}, {"ruleId": "2368", "severity": 1, "message": "2783", "line": 8, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2784", "line": 2, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2441", "line": 2, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2785", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 26}, {"ruleId": "2368", "severity": 1, "message": "2476", "line": 5, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2482", "line": 6, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 31}, {"ruleId": "2368", "severity": 1, "message": "2786", "line": 7, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 27}, {"ruleId": "2368", "severity": 1, "message": "2787", "line": 22, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 22, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2788", "line": 44, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 44, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2789", "line": 110, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 110, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2397", "line": 2, "column": 23, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 34}, {"ruleId": "2368", "severity": 1, "message": "2476", "line": 18, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 18, "endColumn": 21}, {"ruleId": "2387", "severity": 1, "message": "2388", "line": 56, "column": 21, "nodeType": "2389", "endLine": 60, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2441", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2383", "line": 4, "column": 21, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 29}, {"ruleId": "2368", "severity": 1, "message": "2790", "line": 28, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 28, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2664", "line": 5, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2791", "line": 4, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 28}, {"ruleId": "2368", "severity": 1, "message": "2790", "line": 41, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 41, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2397", "line": 1, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 1, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2495", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2792", "line": 5, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2793", "line": 13, "column": 33, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 42}, {"ruleId": "2368", "severity": 1, "message": "2495", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2792", "line": 5, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2790", "line": 14, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 14, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2794", "line": 14, "column": 51, "nodeType": "2370", "messageId": "2371", "endLine": 14, "endColumn": 64}, {"ruleId": "2368", "severity": 1, "message": "2476", "line": 2, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2795", "line": 3, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2495", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2792", "line": 5, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2796", "line": 6, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2793", "line": 14, "column": 33, "nodeType": "2370", "messageId": "2371", "endLine": 14, "endColumn": 42}, {"ruleId": "2368", "severity": 1, "message": "2372", "line": 2, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 22}, {"ruleId": "2368", "severity": 1, "message": "2495", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 18}, {"ruleId": "2368", "severity": 1, "message": "2573", "line": 10, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 10, "endColumn": 23}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 287, "column": 56, "nodeType": "2435", "messageId": "2436", "endLine": 287, "endColumn": 58}, {"ruleId": "2376", "severity": 1, "message": "2797", "line": 31, "column": 8, "nodeType": "2378", "endLine": 31, "endColumn": 33, "suggestions": "2798"}, {"ruleId": "2368", "severity": 1, "message": "2799", "line": 4, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 32}, {"ruleId": "2368", "severity": 1, "message": "2800", "line": 4, "column": 34, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 52}, {"ruleId": "2368", "severity": 1, "message": "2801", "line": 5, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 28}, {"ruleId": "2368", "severity": 1, "message": "2802", "line": 5, "column": 30, "nodeType": "2370", "messageId": "2371", "endLine": 5, "endColumn": 44}, {"ruleId": "2368", "severity": 1, "message": "2803", "line": 7, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 28}, {"ruleId": "2368", "severity": 1, "message": "2804", "line": 118, "column": 29, "nodeType": "2370", "messageId": "2371", "endLine": 118, "endColumn": 35}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 74, "column": 47, "nodeType": "2435", "messageId": "2436", "endLine": 74, "endColumn": 49}, {"ruleId": "2433", "severity": 1, "message": "2434", "line": 160, "column": 70, "nodeType": "2435", "messageId": "2436", "endLine": 160, "endColumn": 72}, {"ruleId": "2433", "severity": 1, "message": "2434", "line": 164, "column": 100, "nodeType": "2435", "messageId": "2436", "endLine": 164, "endColumn": 102}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 166, "column": 38, "nodeType": "2435", "messageId": "2436", "endLine": 166, "endColumn": 40}, {"ruleId": "2368", "severity": 1, "message": "2805", "line": 7, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2594", "line": 8, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2806", "line": 13, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 13, "endColumn": 27}, {"ruleId": "2368", "severity": 1, "message": "2807", "line": 20, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 20, "endColumn": 27}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 26, "column": 18, "nodeType": "2435", "messageId": "2436", "endLine": 26, "endColumn": 20}, {"ruleId": "2368", "severity": 1, "message": "2808", "line": 29, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 29, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2622", "line": 2, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 2, "endColumn": 14}, {"ruleId": "2368", "severity": 1, "message": "2809", "line": 6, "column": 8, "nodeType": "2370", "messageId": "2371", "endLine": 6, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2628", "line": 19, "column": 12, "nodeType": "2370", "messageId": "2371", "endLine": 19, "endColumn": 19}, {"ruleId": "2376", "severity": 1, "message": "2810", "line": 40, "column": 8, "nodeType": "2378", "endLine": 40, "endColumn": 34, "suggestions": "2811"}, {"ruleId": "2376", "severity": 1, "message": "2812", "line": 40, "column": 9, "nodeType": "2813", "endLine": 40, "endColumn": 33}, {"ruleId": "2368", "severity": 1, "message": "2397", "line": 3, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 21}, {"ruleId": "2368", "severity": 1, "message": "2814", "line": 53, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 53, "endColumn": 23}, {"ruleId": "2368", "severity": 1, "message": "2815", "line": 15, "column": 25, "nodeType": "2370", "messageId": "2371", "endLine": 15, "endColumn": 30}, {"ruleId": "2368", "severity": 1, "message": "2816", "line": 40, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 40, "endColumn": 26}, {"ruleId": "2368", "severity": 1, "message": "2817", "line": 3, "column": 10, "nodeType": "2370", "messageId": "2371", "endLine": 3, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2761", "line": 4, "column": 42, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 45}, {"ruleId": "2368", "severity": 1, "message": "2401", "line": 7, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 7, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2577", "line": 8, "column": 11, "nodeType": "2370", "messageId": "2371", "endLine": 8, "endColumn": 19}, {"ruleId": "2368", "severity": 1, "message": "2626", "line": 9, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 9, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2626", "line": 12, "column": 13, "nodeType": "2370", "messageId": "2371", "endLine": 12, "endColumn": 17}, {"ruleId": "2368", "severity": 1, "message": "2818", "line": 4, "column": 15, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 24}, {"ruleId": "2368", "severity": 1, "message": "2622", "line": 4, "column": 33, "nodeType": "2370", "messageId": "2371", "endLine": 4, "endColumn": 37}, {"ruleId": "2376", "severity": 1, "message": "2819", "line": 43, "column": 8, "nodeType": "2378", "endLine": 43, "endColumn": 19, "suggestions": "2820"}, {"ruleId": "2376", "severity": 1, "message": "2605", "line": 50, "column": 8, "nodeType": "2378", "endLine": 50, "endColumn": 18, "suggestions": "2821"}, {"ruleId": "2433", "severity": 1, "message": "2558", "line": 1, "column": 88, "nodeType": "2435", "messageId": "2436", "endLine": 1, "endColumn": 90}, "no-unused-vars", "'AdminUserSearchPage' is defined but never used.", "Identifier", "unusedVar", "'LoadingSpinner' is defined but never used.", "'Link' is defined but never used.", "'GoogleLoginButton' is defined but never used.", "'BeeMathLogo' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'formData.username'. Either include it or remove the dependency array.", "ArrayExpression", ["2822"], "'resultAction' is assigned a value but never used.", "'useRef' is defined but never used.", "'headerHeight' is assigned a value but never used.", "'useState' is defined but never used.", "'isFilterVIew' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'didInit' and 'navigate'. Either include them or remove the dependency array.", ["2823"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'X' is defined but never used.", "'Save' is defined but never used.", "'PutMultipleImages' is defined but never used.", "'putSlideImagesForClass' is defined but never used.", "'category' is assigned a value but never used.", "'questions' is assigned a value but never used.", "'FunctionBarAdmin' is defined but never used.", "'useSelector' is defined but never used.", "'setIsFilterView' is defined but never used.", "'AdminSidebar' is defined but never used.", "'Home' is defined but never used.", "'navigate' is assigned a value but never used.", "'closeSidebar' is assigned a value but never used.", "'loadingExam' is assigned a value but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has a missing dependency: 'fetchPdfFiles'. Either include it or remove the dependency array.", ["2824"], "'showAddStudent' is assigned a value but never used.", "'AdminLayout' is defined but never used.", "'setAttempts' is defined but never used.", "'ScoreDistributionChart' is defined but never used.", "'setLogs' is assigned a value but never used.", "'setAnswers' is assigned a value but never used.", "'prevRanks' is assigned a value but never used.", "'setRankChanges' is assigned a value but never used.", "'setUpdatedAttemptId' is assigned a value but never used.", "'limit' is assigned a value but never used.", "'totalItems' is assigned a value but never used.", "'handleClickedDetail' is assigned a value but never used.", "'handleClickedPreviewExam' is assigned a value but never used.", "'handleClickedQuestions' is assigned a value but never used.", "'useNavigate' is defined but never used.", "React Hook useEffect has a missing dependency: 'folder'. Either include it or remove the dependency array.", ["2825"], "'isAddView' is assigned a value but never used.", "'AdminModal' is defined but never used.", "'AddQuestionModal' is defined but never used.", "'setIsAddView' is defined but never used.", "'handleClickedTracking' is assigned a value but never used.", "'errorMsg' is assigned a value but never used.", "'setExam' is defined but never used.", "'getQuestionAndAnswersByAttemptAPI' is defined but never used.", "'setQuestions' is defined but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "'backgroundImage' is defined but never used.", "'shouldFloat' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'options', 'selected', and 'type'. Either include them or remove the dependency array. If 'setPlaceholder' needs the current value of 'selected', you can also switch to useReducer instead of useState and read 'selected' in the reducer.", ["2826"], "'useEffect' is defined but never used.", "'fetchAllCodes' is defined but never used.", "'search' is assigned a value but never used.", "'sortOrder' is assigned a value but never used.", "'id' is assigned a value but never used.", "'setId' is assigned a value but never used.", "'typeExists' is assigned a value but never used.", "'setSortOrder' is defined but never used.", "'resetFilters' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array. If 'fetchQuestions' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2827"], "'processInputForUpdate' is defined but never used.", "'ClockIcon' is assigned a value but never used.", "'AcademicCapIcon' is assigned a value but never used.", "'EyeIcon' is assigned a value but never used.", "'setDeleteMode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'classId'. Either include it or remove the dependency array.", ["2828"], "'Search' is defined but never used.", "'Clock' is defined but never used.", "'TrendingUp' is defined but never used.", "'data' is assigned a value but never used.", "'isDropdownOpen' is assigned a value but never used.", "'isDropdownOpenPage' is assigned a value but never used.", "'optionsPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSearch'. Either include it or remove the dependency array. If 'setSearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2829"], "'iconFilter' is assigned a value but never used.", "'iconExport' is assigned a value but never used.", "'SuggestInputBarAdmin' is defined but never used.", "React Hook useEffect has a missing dependency: 'question'. Either include it or remove the dependency array.", ["2830"], "'setClass' is defined but never used.", "'DropMenuBarAdmin' is defined but never used.", "'LatexRenderer' is defined but never used.", "React Hook useEffect has a missing dependency: 'exam'. Either include it or remove the dependency array.", ["2831"], "'fetchExamQuestions' is defined but never used.", "'useDispatch' is defined but never used.", "'header' is defined but never used.", "'MarkdownPreviewWithMath' is defined but never used.", "'use' is defined but never used.", "'BookOpen' is defined but never used.", "'GraduationCap' is defined but never used.", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'defaultImageBanner' is defined but never used.", "React Hook useEffect has a missing dependency: 'startAutoPlay'. Either include it or remove the dependency array.", ["2832"], "React Hook useEffect has a missing dependency: 'calculateTimeLeft'. Either include it or remove the dependency array.", ["2833"], "'ExamDefaultImage' is defined but never used.", "'ChevronRight' is defined but never used.", "'Bookmark' is defined but never used.", "'CheckCircle' is defined but never used.", "'formatDate' is assigned a value but never used.", "'createdAt' is assigned a value but never used.", "'imageUrl' is assigned a value but never used.", "'isSave' is assigned a value but never used.", "'isDone' is assigned a value but never used.", "'acceptDoExam' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'examClass' is assigned a value but never used.", "'chapter' is assigned a value but never used.", "'testDuration' is assigned a value but never used.", "'isSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExams'. Either include it or remove the dependency array.", ["2834"], "'result' is assigned a value but never used.", "'LogOut' is defined but never used.", "'isModalOpen' is assigned a value but never used.", "'setIsModalOpen' is assigned a value but never used.", "'toggleDropdown' is defined but never used.", "'toggleExamDropdown' is defined but never used.", "'User' is defined but never used.", "React Hook useEffect has a missing dependency: 'maxLength'. Either include it or remove the dependency array.", ["2835"], "no-useless-escape", "Unnecessary escape character: \\}.", "Literal", "unnecessaryEscape", ["2836", "2837"], "Unnecessary escape character: \\{.", ["2838", "2839"], ["2840", "2841"], ["2842", "2843"], "Unnecessary escape character: \\..", ["2844", "2845"], ["2846", "2847"], ["2848", "2849"], "Unnecessary escape character: \\).", ["2850", "2851"], ["2852", "2853"], ["2854", "2855"], ["2856", "2857"], ["2858", "2859"], ["2860", "2861"], ["2862", "2863"], ["2864", "2865"], ["2866", "2867"], ["2868", "2869"], ["2870", "2871"], "React Hook useEffect has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2872"], "'setSuccessMessage' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'putImageFunction'. Either include them or remove the dependency array. If 'putImageFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2873"], "'InputSearch' is defined but never used.", "'motion' is defined but never used.", "'AnimatePresence' is defined but never used.", "'fetchUnreadCount' is defined but never used.", "'updateUnreadCount' is defined but never used.", "'isMobile' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'toggleCloseSidebar' is defined but never used.", "'ArrowRight' is defined but never used.", "'ArrowUp' is defined but never used.", "'ArrowDown' is defined but never used.", "'isHovered' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'DefaultAvatar' is defined but never used.", "'avatarFile' is assigned a value but never used.", "'setAvatarFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'onImageChange'. Either include them or remove the dependency array. If 'onImageChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2874"], "React Hook useEffect has a missing dependency: 'handleFile'. Either include it or remove the dependency array.", ["2875"], "'currentTime' is assigned a value but never used.", "'setSuccess' is defined but never used.", "'success' is assigned a value but never used.", "'logout' is defined but never used.", "'dispatch' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "no-useless-concat", "Unexpected string concatenation of literals.", "unexpectedConcat", "'Star' is defined but never used.", "'Target' is defined but never used.", "'Crown' is defined but never used.", "'startTime' is assigned a value but never used.", "'user' is assigned a value but never used.", "'remainingTimeRef' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["2876"], "'TeacherImage' is defined but never used.", "'Footer' is defined but never used.", "'Flag' is defined but never used.", "'Lightbulb' is defined but never used.", "'MessageCircle' is defined but never used.", "'HelpCircle' is defined but never used.", "'Contact' is defined but never used.", "'Phone' is defined but never used.", "'HeadphonesIcon' is defined but never used.", "'banner304151' is defined but never used.", "'calenderSlides' is assigned a value but never used.", "'bannerSlides' is assigned a value but never used.", "'vnRedColor' is assigned a value but never used.", "'vnYellowColor' is assigned a value but never used.", "'MomentCard' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["2877"], "no-dupe-keys", "Duplicate key 'sort'.", "ObjectExpression", "React Hook useEffect has a duplicate dependency: 'sort'. Either omit it or remove the dependency array.", ["2878"], "React Hook useEffect has a missing dependency: 'classDetail'. Either include it or remove the dependency array.", ["2879"], "'fetchExamRatingStatistics' is defined but never used.", "'saveExamForUser' is defined but never used.", "'rateExamForUser' is defined but never used.", "'setStar' is defined but never used.", "'StarIcon' is defined but never used.", "'FileText' is defined but never used.", "'QrCodeIcon' is defined but never used.", "'Pin' is defined but never used.", "'Send' is defined but never used.", "'Smile' is defined but never used.", "'Share2' is defined but never used.", "'setCurrentPage' is defined but never used.", "'exam' is assigned a value but never used.", "'view' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'pagination' is assigned a value but never used.", "'handleCopyLink' is assigned a value but never used.", "'loadingRatingStatistics' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleClickHistory', 'handleClickPreviewExam', and 'handleClickRanking'. Either include them or remove the dependency array.", ["2880"], "'ClassImage' is defined but never used.", "'Filter' is defined but never used.", "'Loader' is defined but never used.", "'ChevronDown' is defined but never used.", "'showSortDropdown' is assigned a value but never used.", "'choice' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'limit'. Either include it or remove the dependency array.", ["2881"], "'resetClassDetail' is defined but never used.", "React Hook useEffect has a missing dependency: 'classCode'. Either include it or remove the dependency array.", ["2882"], "React Hook useEffect has a missing dependency: 'activeItem?.index'. Either include it or remove the dependency array.", ["2883"], "'SearchBar' is defined but never used.", "'formatDate' is defined but never used.", "'setSearchTerm' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'BookText' is defined but never used.", "React Hook useEffect has a missing dependency: 'location.search'. Either include it or remove the dependency array.", ["2884"], "'getChapterDescription' is assigned a value but never used.", "'handleResetFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadReports'. Either include it or remove the dependency array.", ["2885"], "'formatDistanceToNow' is defined but never used.", "'vi' is defined but never used.", "'addNotification' is defined but never used.", "'useMemo' is defined but never used.", "'PdfViewer' is defined but never used.", "'BarChart3' is defined but never used.", "'formatTime' is defined but never used.", "'BackButton' is assigned a value but never used.", "'handleReExamination' is assigned a value but never used.", "'articles' is assigned a value but never used.", "'handlePageChange' is assigned a value but never used.", "'currentPage' is assigned a value but never used.", "'getTypeDescription' is assigned a value but never used.", "'getClassDescription' is assigned a value but never used.", "'formatCurrency' is defined but never used.", "'html2canvas' is defined but never used.", "'find' is defined but never used.", "'formatNumberWithDots' is defined but never used.", "'List' is defined but never used.", "'Chart' is defined but never used.", "'selectedUserId' is assigned a value but never used.", "'userSearchTerm' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'startMonth' is assigned a value but never used.", "'setStartMonth' is assigned a value but never used.", "'endMonth' is assigned a value but never used.", "'setEndMonth' is assigned a value but never used.", "'monthlyChartRef' is assigned a value but never used.", "'classChartRef' is assigned a value but never used.", "'monthlyChartInstance' is assigned a value but never used.", "'classChartInstance' is assigned a value but never used.", "'viewPayment' is assigned a value but never used.", "'viewClassTuitions' is assigned a value but never used.", "'exportLoading' is assigned a value but never used.", "'setExportLoading' is assigned a value but never used.", "'handleSelectUser' is assigned a value but never used.", "'handleClearUserSelection' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filterClass', 'filterGraduationYear', 'filterIsPaid', 'filterMonth', 'filterOverdue', and 'inputValue'. Either include them or remove the dependency array.", ["2886"], "'handleBatchAdd' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'tuitionPayments'. Either include it or remove the dependency array.", ["2887"], "'iconBatch' is assigned a value but never used.", "'iconUsers' is assigned a value but never used.", "'Receipt' is defined but never used.", "'studentClassTuitions' is assigned a value but never used.", "'classTuitionsLoading' is assigned a value but never used.", "'setClassTuitionsLoading' is assigned a value but never used.", "'QrCode' is defined but never used.", "'setLimit' is defined but never used.", "'Award' is defined but never used.", ["2888"], "React Hook useEffect has a missing dependency: 'user?.id'. Either include it or remove the dependency array.", ["2889"], "'clearUserAttendancesFilters' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'userId'. Either include them or remove the dependency array.", ["2890"], "'MapPin' is defined but never used.", "'RefreshCw' is defined but never used.", "'handleRefresh' is assigned a value but never used.", "'student' is assigned a value but never used.", "Duplicate key 'startTime'.", "'typeOfQuestion' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'wasInError' is assigned a value but never used.", "'DollarSign' is defined but never used.", "'getStatusBadge' is assigned a value but never used.", "'getPaymentStatusIcon' is assigned a value but never used.", "'ChevronLeft' is defined but never used.", "'getLearningItemWeekend' is defined but never used.", "'resetCalendar' is defined but never used.", "'setSelectedDay' is defined but never used.", "'UserLayout' is defined but never used.", "'fetchClassesOverview' is defined but never used.", "'CalendarMonth' is defined but never used.", "'SidebarCalendar' is defined but never used.", "'selectedDay' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'firstGridDate' and 'lastGridDate'. Either include them or remove the dependency array.", ["2891"], "'NavigateTimeButton' is defined but never used.", "'recentActivities' is assigned a value but never used.", "'systemStatus' is assigned a value but never used.", "'TableAdmin' is defined but never used.", "'TdAdmin' is defined but never used.", "'ThAdmin' is defined but never used.", "'TheadAdmin' is defined but never used.", "'month' is assigned a value but never used.", "'setMonth' is assigned a value but never used.", "'tuitionPayments' is assigned a value but never used.", "'userAttempts' is assigned a value but never used.", "'Pagination' is defined but never used.", "'staff' is assigned a value but never used.", "'setStep' is defined but never used.", "'setExamData' is defined but never used.", "'postExam' is defined but never used.", "'nextStep' is defined but never used.", "'prevStep' is defined but never used.", "'setCreatedExam' is defined but never used.", "'ImageUpload' is defined but never used.", "'UploadPdf' is defined but never used.", "'Eye' is defined but never used.", "'Plus' is defined but never used.", "'Trash2' is defined but never used.", "'Edit' is defined but never used.", "'ImageIcon' is defined but never used.", "'Upload' is defined but never used.", "'resetData' is defined but never used.", ["2892"], "'questionUntil' is defined but never used.", "'useDebouncedEffect' is defined but never used.", "'examImage' is assigned a value but never used.", "'examFile' is assigned a value but never used.", "'examData' is assigned a value but never used.", "'Info' is defined but never used.", "'splitMarkdownToParts' is defined but never used.", "'markDownExam' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'isViewAdd'. Either include it or remove the dependency array.", ["2893"], "React Hook useEffect has a missing dependency: 'effect'. Either include it or remove the dependency array. If 'effect' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2894"], "React Hook useEffect has a spread element in its dependency array. This means we can't statically verify whether you've passed the correct dependencies.", "SpreadElement", "'setQuestion' is defined but never used.", "'examApi' is defined but never used.", "'setSelectedIndex' is defined but never used.", "'reorderStatements' is defined but never used.", "'QuestionContent' is defined but never used.", "'prefixTN' is assigned a value but never used.", "'prefixDS' is assigned a value but never used.", "'darkMode' is assigned a value but never used.", "'QuestionSectionTitle' is defined but never used.", "'ReportButton' is defined but never used.", "'imageSize' is assigned a value but never used.", "'saveQuestions' is assigned a value but never used.", "'QuestionImage' is defined but never used.", "'NoTranslate' is defined but never used.", "React Hook useEffect has a missing dependency: 'extendedOptions'. Either include it or remove the dependency array.", ["2895"], "'initialPaginationState' is defined but never used.", "'paginationReducers' is defined but never used.", "'initialFilterState' is defined but never used.", "'filterReducers' is defined but never used.", "'examCommentsApi' is defined but never used.", "'examId' is assigned a value but never used.", "'LoadingData' is defined but never used.", "'showEmojiPicker' is assigned a value but never used.", "'handleEmojiClick' is assigned a value but never used.", "'handleSend' is assigned a value but never used.", "'EmojiPicker' is defined but never used.", "React Hook useEffect has missing dependencies: 'comment.id' and 'repliesState'. Either include them or remove the dependency array. If 'setReplies' needs the current value of 'comment.id', you can also switch to useReducer instead of useState and read 'comment.id' in the reducer.", ["2896"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "MemberExpression", "'questionId' is assigned a value but never used.", "'Medal' is defined but never used.", "'getRandomAvatar' is assigned a value but never used.", "'setView' is defined but never used.", "'ChevronUp' is defined but never used.", "React Hook useEffect has a missing dependency: 'thinkingMessages.length'. Either include it or remove the dependency array.", ["2897"], ["2898"], {"desc": "2899", "fix": "2900"}, {"desc": "2901", "fix": "2902"}, {"desc": "2903", "fix": "2904"}, {"desc": "2905", "fix": "2906"}, {"desc": "2907", "fix": "2908"}, {"desc": "2909", "fix": "2910"}, {"desc": "2911", "fix": "2912"}, {"desc": "2913", "fix": "2914"}, {"desc": "2915", "fix": "2916"}, {"desc": "2917", "fix": "2918"}, {"desc": "2919", "fix": "2920"}, {"desc": "2921", "fix": "2922"}, {"desc": "2923", "fix": "2924"}, {"desc": "2925", "fix": "2926"}, {"messageId": "2927", "fix": "2928", "desc": "2929"}, {"messageId": "2930", "fix": "2931", "desc": "2932"}, {"messageId": "2927", "fix": "2933", "desc": "2929"}, {"messageId": "2930", "fix": "2934", "desc": "2932"}, {"messageId": "2927", "fix": "2935", "desc": "2929"}, {"messageId": "2930", "fix": "2936", "desc": "2932"}, {"messageId": "2927", "fix": "2937", "desc": "2929"}, {"messageId": "2930", "fix": "2938", "desc": "2932"}, {"messageId": "2927", "fix": "2939", "desc": "2929"}, {"messageId": "2930", "fix": "2940", "desc": "2932"}, {"messageId": "2927", "fix": "2941", "desc": "2929"}, {"messageId": "2930", "fix": "2942", "desc": "2932"}, {"messageId": "2927", "fix": "2943", "desc": "2929"}, {"messageId": "2930", "fix": "2944", "desc": "2932"}, {"messageId": "2927", "fix": "2945", "desc": "2929"}, {"messageId": "2930", "fix": "2946", "desc": "2932"}, {"messageId": "2927", "fix": "2947", "desc": "2929"}, {"messageId": "2930", "fix": "2948", "desc": "2932"}, {"messageId": "2927", "fix": "2949", "desc": "2929"}, {"messageId": "2930", "fix": "2950", "desc": "2932"}, {"messageId": "2927", "fix": "2951", "desc": "2929"}, {"messageId": "2930", "fix": "2952", "desc": "2932"}, {"messageId": "2927", "fix": "2953", "desc": "2929"}, {"messageId": "2930", "fix": "2954", "desc": "2932"}, {"messageId": "2927", "fix": "2955", "desc": "2929"}, {"messageId": "2930", "fix": "2956", "desc": "2932"}, {"messageId": "2927", "fix": "2957", "desc": "2929"}, {"messageId": "2930", "fix": "2958", "desc": "2932"}, {"messageId": "2927", "fix": "2959", "desc": "2929"}, {"messageId": "2930", "fix": "2960", "desc": "2932"}, {"messageId": "2927", "fix": "2961", "desc": "2929"}, {"messageId": "2930", "fix": "2962", "desc": "2932"}, {"messageId": "2927", "fix": "2963", "desc": "2929"}, {"messageId": "2930", "fix": "2964", "desc": "2932"}, {"messageId": "2927", "fix": "2965", "desc": "2929"}, {"messageId": "2930", "fix": "2966", "desc": "2932"}, {"desc": "2967", "fix": "2968"}, {"desc": "2969", "fix": "2970"}, {"desc": "2971", "fix": "2972"}, {"desc": "2973", "fix": "2974"}, {"desc": "2975", "fix": "2976"}, {"desc": "2977", "fix": "2978"}, {"desc": "2979", "fix": "2980"}, {"desc": "2981", "fix": "2982"}, {"desc": "2983", "fix": "2984"}, {"desc": "2985", "fix": "2986"}, {"desc": "2987", "fix": "2988"}, {"desc": "2989", "fix": "2990"}, {"desc": "2991", "fix": "2992"}, {"desc": "2993", "fix": "2994"}, {"desc": "2995", "fix": "2996"}, {"desc": "2997", "fix": "2998"}, {"desc": "2999", "fix": "3000"}, {"desc": "3001", "fix": "3002"}, {"desc": "3003", "fix": "3004"}, {"desc": "3005", "fix": "3006"}, {"desc": "2905", "fix": "3007"}, {"desc": "3008", "fix": "3009"}, {"desc": "3010", "fix": "3011"}, {"desc": "3012", "fix": "3013"}, {"desc": "3014", "fix": "3015"}, {"desc": "3016", "fix": "3017"}, {"desc": "3018", "fix": "3019"}, "Update the dependencies array to be: [user, navigate, formData.username]", {"range": "3020", "text": "3021"}, "Update the dependencies array to be: [did<PERSON><PERSON><PERSON>, isAddView, navigate]", {"range": "3022", "text": "3023"}, "Update the dependencies array to be: [dispatch, fetchPdfFiles]", {"range": "3024", "text": "3025"}, "Update the dependencies array to be: [dispatch, folder]", {"range": "3026", "text": "3027"}, "Update the dependencies array to be: [options, selected, type]", {"range": "3028", "text": "3029"}, "Update the dependencies array to be: [dispatch, fetchQuestions, params]", {"range": "3030", "text": "3031"}, "Update the dependencies array to be: [dispatch, search, page, pageSize, sortOrder, classId]", {"range": "3032", "text": "3033"}, "Update the dependencies array to be: [inputValue, dispatch, setSearch]", {"range": "3034", "text": "3035"}, "Update the dependencies array to be: [codes, question, question.class]", {"range": "3036", "text": "3037"}, "Update the dependencies array to be: [codes, exam, exam?.class]", {"range": "3038", "text": "3039"}, "Update the dependencies array to be: [images.length, autoPlay, interval, startAutoPlay]", {"range": "3040", "text": "3041"}, "Update the dependencies array to be: [calculateTimeLeft, targetTime]", {"range": "3042", "text": "3043"}, "Update the dependencies array to be: [dispatch, currentPage, didInit, fetchExams]", {"range": "3044", "text": "3045"}, "Update the dependencies array to be: [maxLength]", {"range": "3046", "text": "3047"}, "removeEscape", {"range": "3048", "text": "3049"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "3050", "text": "3051"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "3052", "text": "3049"}, {"range": "3053", "text": "3051"}, {"range": "3054", "text": "3049"}, {"range": "3055", "text": "3051"}, {"range": "3056", "text": "3049"}, {"range": "3057", "text": "3051"}, {"range": "3058", "text": "3049"}, {"range": "3059", "text": "3051"}, {"range": "3060", "text": "3049"}, {"range": "3061", "text": "3051"}, {"range": "3062", "text": "3049"}, {"range": "3063", "text": "3051"}, {"range": "3064", "text": "3049"}, {"range": "3065", "text": "3051"}, {"range": "3066", "text": "3049"}, {"range": "3067", "text": "3051"}, {"range": "3068", "text": "3049"}, {"range": "3069", "text": "3051"}, {"range": "3070", "text": "3049"}, {"range": "3071", "text": "3051"}, {"range": "3072", "text": "3049"}, {"range": "3073", "text": "3051"}, {"range": "3074", "text": "3049"}, {"range": "3075", "text": "3051"}, {"range": "3076", "text": "3049"}, {"range": "3077", "text": "3051"}, {"range": "3078", "text": "3049"}, {"range": "3079", "text": "3051"}, {"range": "3080", "text": "3049"}, {"range": "3081", "text": "3051"}, {"range": "3082", "text": "3049"}, {"range": "3083", "text": "3051"}, {"range": "3084", "text": "3049"}, {"range": "3085", "text": "3051"}, "Update the dependencies array to be: [handlePaste]", {"range": "3086", "text": "3087"}, "Update the dependencies array to be: [id, image, imageUrl, putImageFunction]", {"range": "3088", "text": "3089"}, "Update the dependencies array to be: [image, avatarUrl, onImageChange, id]", {"range": "3090", "text": "3091"}, "Update the dependencies array to be: [handleFile]", {"range": "3092", "text": "3093"}, "Update the dependencies array to be: [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, isTimeUp, resetDone, handleSubmit]", {"range": "3094", "text": "3095"}, "Update the dependencies array to be: [codes, dispatch, grade]", {"range": "3096", "text": "3097"}, "Update the dependencies array to be: [dispatch, search, page, sort, typeOfExam, grade, chapter, view, isClassroomExam, year, firstLoad]", {"range": "3098", "text": "3099"}, "Update the dependencies array to be: [classDetail, dispatch]", {"range": "3100", "text": "3101"}, "Update the dependencies array to be: [location.search, dispatch, firstRender, handleClickRanking, handleClickPreviewExam, handleClickHistory]", {"range": "3102", "text": "3103"}, "Update the dependencies array to be: [dispatch, limit]", {"range": "3104", "text": "3105"}, "Update the dependencies array to be: [lessonId, classDetail, sortedLessons, learningItemId, firstHandled, classCode]", {"range": "3106", "text": "3107"}, "Update the dependencies array to be: [activeItem?.index, classDetail]", {"range": "3108", "text": "3109"}, "Update the dependencies array to be: [dispatch, location.search]", {"range": "3110", "text": "3111"}, "Update the dependencies array to be: [currentPage, didInit, loadReports]", {"range": "3112", "text": "3113"}, "Update the dependencies array to be: [dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", {"range": "3114", "text": "3115"}, "Update the dependencies array to be: [tuitionPayments, tuitionStatistics]", {"range": "3116", "text": "3117"}, "Update the dependencies array to be: [selected<PERSON><PERSON><PERSON>, monthTuition, dispatch]", {"range": "3118", "text": "3119"}, "Update the dependencies array to be: [dispatch, selectedYear, selectedMonth, user?.id]", {"range": "3120", "text": "3121"}, "Update the dependencies array to be: [dispatch, userId]", {"range": "3122", "text": "3123"}, "Update the dependencies array to be: [dispatch, currentMonth, view, firstGridDate, lastGridDate]", {"range": "3124", "text": "3125"}, {"range": "3126", "text": "3027"}, "Update the dependencies array to be: [questionT<PERSON>ontent, correctAnswerTN, questionDS<PERSON>ontent, correctAnswerDS, questionTLNContent, correctAnswerTLN, isViewAdd]", {"range": "3127", "text": "3128"}, "Update the dependencies array to be: [delay, effect]", {"range": "3129", "text": "3130"}, "Update the dependencies array to be: [selected, filterOptions, extendedOptions]", {"range": "3131", "text": "3132"}, "Update the dependencies array to be: [comment.id, repliesState]", {"range": "3133", "text": "3134"}, "Update the dependencies array to be: [aiLoading, thinkingMessages.length]", {"range": "3135", "text": "3136"}, "Update the dependencies array to be: [dispatch, question]", {"range": "3137", "text": "3138"}, [1894, 1910], "[user, navigate, formData.username]", [922, 933], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, navigate]", [9548, 9558], "[dispatch, fetchPdfFiles]", [1187, 1197], "[dispatch, folder]", [669, 671], "[options, selected, type]", [1903, 1921], "[dispatch, fetchQuestions, params]", [1569, 1614], "[dispatch, search, page, pageSize, sortOrder, classId]", [2683, 2705], "[inputValue, dispatch, setSearch]", [2124, 2148], "[codes, question, question.class]", [2394, 2414], "[codes, exam, exam?.class]", [1142, 1177], "[images.length, autoPlay, interval, startAutoPlay]", [865, 877], "[calculateTimeLeft, targetTime]", [1868, 1900], "[dispatch, currentPage, didInit, fetchExams]", [972, 974], "[maxLength]", [7921, 7922], "", [7921, 7921], "\\", [7923, 7924], [7923, 7923], [8024, 8025], [8024, 8024], [8026, 8027], [8026, 8026], [9722, 9723], [9722, 9722], [10479, 10480], [10479, 10479], [11110, 11111], [11110, 11110], [11112, 11113], [11112, 11112], [11182, 11183], [11182, 11182], [11184, 11185], [11184, 11184], [14059, 14060], [14059, 14059], [15200, 15201], [15200, 15200], [15806, 15807], [15806, 15806], [15808, 15809], [15808, 15808], [15878, 15879], [15878, 15878], [15880, 15881], [15880, 15880], [18750, 18751], [18750, 18750], [19665, 19666], [19665, 19665], [2566, 2568], "[handlePaste]", [2840, 2857], "[id, image, imageUrl, putImageFunction]", [757, 775], "[image, avatarUrl, onImageChange, id]", [1462, 1464], "[handleFile]", [5971, 6083], "[exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, isTimeUp, resetDone, handleSubmit]", [8245, 8259], "[codes, dispatch, grade]", [8578, 8682], "[dispatch, search, page, sort, typeOfExam, grade, chapter, view, isClassroomExam, year, firstLoad]", [14882, 14892], "[classDetail, dispatch]", [19570, 19610], "[location.search, dispatch, firstRender, handleClickRanking, handleClickPreviewExam, handleClickHistory]", [4183, 4193], "[dispatch, limit]", [5970, 6038], "[lessonId, classDetail, sortedLessons, learningItemId, firstHandled, classCode]", [7974, 7987], "[activeItem?.index, classDetail]", [6098, 6108], "[dispatch, location.search]", [1654, 1676], "[currentPage, didInit, loadReports]", [10387, 10413], "[dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", [24202, 24221], "[tuitionPayments, tuitionStatistics]", [8386, 8416], "[<PERSON><PERSON><PERSON><PERSON>, monthTuition, dispatch]", [2133, 2172], "[dispatch, selected<PERSON>ear, selected<PERSON><PERSON><PERSON>, user?.id]", [3996, 3998], "[dispatch, userId]", [2456, 2486], "[dispatch, currentMonth, view, firstGridDate, lastGridDate]", [2255, 2265], [21202, 21312], "[question<PERSON><PERSON><PERSON><PERSON>, correctAnswerTN, question<PERSON><PERSON><PERSON>nt, correctAnswerDS, questionTLNContent, correctAnswerTLN, isViewAdd]", [511, 527], "[delay, effect]", [995, 1020], "[selected, filterOptions, extendedOptions]", [1560, 1586], "[comment.id, repliesState]", [1846, 1857], "[ai<PERSON><PERSON><PERSON>, thinkingMessages.length]", [1970, 1980], "[dispatch, question]"]