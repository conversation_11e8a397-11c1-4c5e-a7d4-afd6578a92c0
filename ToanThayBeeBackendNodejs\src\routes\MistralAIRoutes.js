import express from 'express';
import asyncHandler from '../middlewares/asyncHandler.js';
import * as MistralAIController from '../controllers/MistralAIController.js';
import uploadPDF from '../middlewares/pdfGoogleUpload.js';

const router = express.Router();

router.post('/v1/AI/ocr/pdf',
    uploadPDF.single('pdf'),
    asyncHandler(MistralAIController.ocrPdfWithMistral)
)

router.post('/v1/AI/handle/exam/pdf',
    uploadPDF.single('pdf'),
    async<PERSON>and<PERSON>(MistralAIController.handleExamPdfWithAI)
)

export default router;