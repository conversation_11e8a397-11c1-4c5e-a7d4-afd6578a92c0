{"ast": null, "code": "// src/app/store.js\nimport { configureStore } from '@reduxjs/toolkit';\nimport authReducer from '../features/auth/authSlice';\nimport sidebarReducer from '../features/sidebar/sidebarSlice';\nimport usersReducer from '../features/user/userSlice';\nimport filterReducer from '../features/filter/filterSlice';\nimport questionReducer from '../features/question/questionSlice';\nimport codeReducer from '../features/code/codeSlice';\nimport stateReducer from '../features/state/stateApiSlice';\nimport examReducer from '../features/exam/examSlice';\nimport classReducer from '../features/class/classSlice';\nimport answerReducer from '../features/answer/answerSlice';\nimport attemptReducer from '../features/attempt/attemptSlice';\nimport articleReducer from '../features/article/articleSlice';\nimport imageReducer from '../features/image/imageSlice';\nimport achievementReducer from '../features/achievement/achievementSlice';\nimport questionReportReducer from '../features/questionReport/questionReportSlice';\nimport notificationReducer from '../features/notification/notificationSlice';\nimport tuitionReducer from '../features/tuition/tuitionSlice';\nimport attendanceReducer from '../features/attendance/attendanceSlice';\nimport lessonReducer from '../features/lesson/lessonSlice';\nimport learningItemReducer from '../features/learningItem/learningItemSlice';\nimport doExamReducer from '../features/doExam/doExamSlice';\nimport sheetReducer from '../features/sheet/sheetSlice';\nimport calendarReducer from '../features/calendar/calendarSlice';\nimport dashboardReducer from '../features/dashboard/dashboardSlice';\nimport addExamReducer from '../features/addExam/addExamSlice';\nimport questionsExamReducer from '../features/questionsExam/questionsExamSlice';\nimport practiceReducer from '../features/practice/practiceSlice';\nimport examDetailReducer from '../features/exam/examDetailSlice';\nimport examCommentsReducer from '../features/comments/ExamCommentsSlice';\nimport aiReducer from '../features/ai/aiSlice';\nimport scorePageReducer from '../features/scorePage/scorePageSlice';\nexport const store = configureStore({\n  reducer: {\n    auth: authReducer,\n    sidebar: sidebarReducer,\n    users: usersReducer,\n    filter: filterReducer,\n    questions: questionReducer,\n    codes: codeReducer,\n    states: stateReducer,\n    exams: examReducer,\n    classes: classReducer,\n    answers: answerReducer,\n    attempts: attemptReducer,\n    articles: articleReducer,\n    images: imageReducer,\n    achievements: achievementReducer,\n    questionReports: questionReportReducer,\n    notifications: notificationReducer,\n    tuition: tuitionReducer,\n    attendances: attendanceReducer,\n    lessons: lessonReducer,\n    learningItems: learningItemReducer,\n    doExam: doExamReducer,\n    sheet: sheetReducer,\n    calendar: calendarReducer,\n    dashboard: dashboardReducer,\n    addExam: addExamReducer,\n    questionsExam: questionsExamReducer,\n    practice: practiceReducer,\n    examDetail: examDetailReducer,\n    comments: examCommentsReducer,\n    ai: aiReducer,\n    scorePage: scorePageReducer\n  }\n});", "map": {"version": 3, "names": ["configureStore", "authReducer", "sidebarReducer", "usersReducer", "filterReducer", "questionReducer", "codeReducer", "stateReducer", "examReducer", "classReducer", "answerReducer", "attemptReducer", "articleReducer", "imageReducer", "achievementReducer", "questionReportReducer", "notificationReducer", "tuitionReducer", "attendanceReducer", "lessonReducer", "learningItemReducer", "doExamReducer", "sheetReducer", "calendarReducer", "dashboardReducer", "addExamReducer", "questionsExamReducer", "practiceReducer", "examDetailReducer", "examCommentsReducer", "aiReducer", "scorePageReducer", "store", "reducer", "auth", "sidebar", "users", "filter", "questions", "codes", "states", "exams", "classes", "answers", "attempts", "articles", "images", "achievements", "questionReports", "notifications", "tuition", "attendances", "lessons", "learningItems", "doExam", "sheet", "calendar", "dashboard", "addExam", "questionsExam", "practice", "examDetail", "comments", "ai", "scorePage"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/redux/store.js"], "sourcesContent": ["// src/app/store.js\r\nimport { configureStore } from '@reduxjs/toolkit';\r\nimport authReducer from '../features/auth/authSlice';\r\nimport sidebarReducer from '../features/sidebar/sidebarSlice';\r\nimport usersReducer from '../features/user/userSlice';\r\nimport filterReducer from '../features/filter/filterSlice';\r\nimport questionReducer from '../features/question/questionSlice';\r\nimport codeReducer from '../features/code/codeSlice';\r\nimport stateReducer from '../features/state/stateApiSlice';\r\nimport examReducer from '../features/exam/examSlice';\r\nimport classReducer from '../features/class/classSlice';\r\nimport answerReducer from '../features/answer/answerSlice';\r\nimport attemptReducer from '../features/attempt/attemptSlice';\r\nimport articleReducer from '../features/article/articleSlice';\r\nimport imageReducer from '../features/image/imageSlice';\r\nimport achievementReducer from '../features/achievement/achievementSlice';\r\nimport questionReportReducer from '../features/questionReport/questionReportSlice';\r\nimport notificationReducer from '../features/notification/notificationSlice';\r\nimport tuitionReducer from '../features/tuition/tuitionSlice';\r\nimport attendanceReducer from '../features/attendance/attendanceSlice';\r\nimport lessonReducer from '../features/lesson/lessonSlice';\r\nimport learningItemReducer from '../features/learningItem/learningItemSlice';\r\nimport doExamReducer from '../features/doExam/doExamSlice';\r\nimport sheetReducer from '../features/sheet/sheetSlice';\r\nimport calendarReducer from '../features/calendar/calendarSlice';\r\nimport dashboardReducer from '../features/dashboard/dashboardSlice';\r\nimport addExamReducer from '../features/addExam/addExamSlice';\r\nimport questionsExamReducer from '../features/questionsExam/questionsExamSlice';\r\nimport practiceReducer from '../features/practice/practiceSlice';\r\nimport examDetailReducer from '../features/exam/examDetailSlice';\r\nimport examCommentsReducer from '../features/comments/ExamCommentsSlice';\r\nimport aiReducer from '../features/ai/aiSlice';\r\nimport scorePageReducer from '../features/scorePage/scorePageSlice';\r\n\r\nexport const store = configureStore({\r\n    reducer: {\r\n        auth: authReducer,\r\n        sidebar: sidebarReducer,\r\n        users: usersReducer,\r\n        filter: filterReducer,\r\n        questions: questionReducer,\r\n        codes: codeReducer,\r\n        states: stateReducer,\r\n        exams: examReducer,\r\n        classes: classReducer,\r\n        answers: answerReducer,\r\n        attempts: attemptReducer,\r\n        articles: articleReducer,\r\n        images: imageReducer,\r\n        achievements: achievementReducer,\r\n        questionReports: questionReportReducer,\r\n        notifications: notificationReducer,\r\n        tuition: tuitionReducer,\r\n        attendances: attendanceReducer,\r\n        lessons: lessonReducer,\r\n        learningItems: learningItemReducer,\r\n        doExam: doExamReducer,\r\n        sheet: sheetReducer,\r\n        calendar: calendarReducer,\r\n        dashboard: dashboardReducer,\r\n        addExam: addExamReducer,\r\n        questionsExam: questionsExamReducer,\r\n        practice: practiceReducer,\r\n        examDetail: examDetailReducer,\r\n        comments: examCommentsReducer,\r\n        ai: aiReducer,\r\n        scorePage: scorePageReducer,\r\n    },\r\n});\r\n"], "mappings": "AAAA;AACA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,YAAY,MAAM,iCAAiC;AAC1D,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,kBAAkB,MAAM,0CAA0C;AACzE,OAAOC,qBAAqB,MAAM,gDAAgD;AAClF,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,iBAAiB,MAAM,wCAAwC;AACtE,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,mBAAmB,MAAM,wCAAwC;AACxE,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,gBAAgB,MAAM,sCAAsC;AAEnE,OAAO,MAAMC,KAAK,GAAGhC,cAAc,CAAC;EAChCiC,OAAO,EAAE;IACLC,IAAI,EAAEjC,WAAW;IACjBkC,OAAO,EAAEjC,cAAc;IACvBkC,KAAK,EAAEjC,YAAY;IACnBkC,MAAM,EAAEjC,aAAa;IACrBkC,SAAS,EAAEjC,eAAe;IAC1BkC,KAAK,EAAEjC,WAAW;IAClBkC,MAAM,EAAEjC,YAAY;IACpBkC,KAAK,EAAEjC,WAAW;IAClBkC,OAAO,EAAEjC,YAAY;IACrBkC,OAAO,EAAEjC,aAAa;IACtBkC,QAAQ,EAAEjC,cAAc;IACxBkC,QAAQ,EAAEjC,cAAc;IACxBkC,MAAM,EAAEjC,YAAY;IACpBkC,YAAY,EAAEjC,kBAAkB;IAChCkC,eAAe,EAAEjC,qBAAqB;IACtCkC,aAAa,EAAEjC,mBAAmB;IAClCkC,OAAO,EAAEjC,cAAc;IACvBkC,WAAW,EAAEjC,iBAAiB;IAC9BkC,OAAO,EAAEjC,aAAa;IACtBkC,aAAa,EAAEjC,mBAAmB;IAClCkC,MAAM,EAAEjC,aAAa;IACrBkC,KAAK,EAAEjC,YAAY;IACnBkC,QAAQ,EAAEjC,eAAe;IACzBkC,SAAS,EAAEjC,gBAAgB;IAC3BkC,OAAO,EAAEjC,cAAc;IACvBkC,aAAa,EAAEjC,oBAAoB;IACnCkC,QAAQ,EAAEjC,eAAe;IACzBkC,UAAU,EAAEjC,iBAAiB;IAC7BkC,QAAQ,EAAEjC,mBAAmB;IAC7BkC,EAAE,EAAEjC,SAAS;IACbkC,SAAS,EAAEjC;EACf;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}