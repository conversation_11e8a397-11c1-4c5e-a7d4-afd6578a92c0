import { ClientSDK } from "../lib/sdks.js";
import { Conversations } from "./conversations.js";
import { Libraries } from "./libraries.js";
import { MistralAgents } from "./mistralagents.js";
export declare class Beta extends ClientSDK {
    private _conversations?;
    get conversations(): Conversations;
    private _agents?;
    get agents(): MistralAgents;
    private _libraries?;
    get libraries(): Libraries;
}
//# sourceMappingURL=beta.d.ts.map