import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type LibrariesGetV1Request = {
    libraryId: string;
};
/** @internal */
export declare const LibrariesGetV1Request$inboundSchema: z.ZodType<LibrariesGetV1Request, z.ZodTypeDef, unknown>;
/** @internal */
export type LibrariesGetV1Request$Outbound = {
    library_id: string;
};
/** @internal */
export declare const LibrariesGetV1Request$outboundSchema: z.ZodType<LibrariesGetV1Request$Outbound, z.ZodTypeDef, LibrariesGetV1Request>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace LibrariesGetV1Request$ {
    /** @deprecated use `LibrariesGetV1Request$inboundSchema` instead. */
    const inboundSchema: z.ZodType<LibrariesGetV1Request, z.ZodTypeDef, unknown>;
    /** @deprecated use `LibrariesGetV1Request$outboundSchema` instead. */
    const outboundSchema: z.ZodType<LibrariesGetV1Request$Outbound, z.ZodTypeDef, LibrariesGetV1Request>;
    /** @deprecated use `LibrariesGetV1Request$Outbound` instead. */
    type Outbound = LibrariesGetV1Request$Outbound;
}
export declare function librariesGetV1RequestToJSON(librariesGetV1Request: LibrariesGetV1Request): string;
export declare function librariesGetV1RequestFromJSON(jsonString: string): SafeParseResult<LibrariesGetV1Request, SDKValidationError>;
//# sourceMappingURL=librariesgetv1.d.ts.map