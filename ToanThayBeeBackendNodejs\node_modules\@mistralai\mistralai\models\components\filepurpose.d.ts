import * as z from "zod";
import { OpenEnum } from "../../types/enums.js";
export declare const FilePurpose: {
    readonly FineTune: "fine-tune";
    readonly Batch: "batch";
    readonly Ocr: "ocr";
};
export type FilePurpose = OpenEnum<typeof FilePurpose>;
/** @internal */
export declare const FilePurpose$inboundSchema: z.ZodType<FilePurpose, z.ZodTypeDef, unknown>;
/** @internal */
export declare const FilePurpose$outboundSchema: z.ZodType<FilePurpose, z.ZodTypeDef, FilePurpose>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace FilePurpose$ {
    /** @deprecated use `FilePurpose$inboundSchema` instead. */
    const inboundSchema: z.ZodType<FilePurpose, z.ZodTypeDef, unknown>;
    /** @deprecated use `FilePurpose$outboundSchema` instead. */
    const outboundSchema: z.ZodType<FilePurpose, z.ZodTypeDef, FilePurpose>;
}
//# sourceMappingURL=filepurpose.d.ts.map