/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  FunctionT,
  FunctionT$inboundSchema,
  FunctionT$Outbound,
  FunctionT$outboundSchema,
} from "./function.js";

export const FunctionToolType = {
  Function: "function",
} as const;
export type FunctionToolType = ClosedEnum<typeof FunctionToolType>;

export type FunctionTool = {
  type?: FunctionToolType | undefined;
  function: FunctionT;
};

/** @internal */
export const FunctionToolType$inboundSchema: z.ZodNativeEnum<
  typeof FunctionToolType
> = z.nativeEnum(FunctionToolType);

/** @internal */
export const FunctionToolType$outboundSchema: z.ZodNativeEnum<
  typeof FunctionToolType
> = FunctionToolType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FunctionToolType$ {
  /** @deprecated use `FunctionToolType$inboundSchema` instead. */
  export const inboundSchema = FunctionToolType$inboundSchema;
  /** @deprecated use `FunctionToolType$outboundSchema` instead. */
  export const outboundSchema = FunctionToolType$outboundSchema;
}

/** @internal */
export const FunctionTool$inboundSchema: z.ZodType<
  FunctionTool,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: FunctionToolType$inboundSchema.default("function"),
  function: FunctionT$inboundSchema,
});

/** @internal */
export type FunctionTool$Outbound = {
  type: string;
  function: FunctionT$Outbound;
};

/** @internal */
export const FunctionTool$outboundSchema: z.ZodType<
  FunctionTool$Outbound,
  z.ZodTypeDef,
  FunctionTool
> = z.object({
  type: FunctionToolType$outboundSchema.default("function"),
  function: FunctionT$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FunctionTool$ {
  /** @deprecated use `FunctionTool$inboundSchema` instead. */
  export const inboundSchema = FunctionTool$inboundSchema;
  /** @deprecated use `FunctionTool$outboundSchema` instead. */
  export const outboundSchema = FunctionTool$outboundSchema;
  /** @deprecated use `FunctionTool$Outbound` instead. */
  export type Outbound = FunctionTool$Outbound;
}

export function functionToolToJSON(functionTool: FunctionTool): string {
  return JSON.stringify(FunctionTool$outboundSchema.parse(functionTool));
}

export function functionToolFromJSON(
  jsonString: string,
): SafeParseResult<FunctionTool, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FunctionTool$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FunctionTool' from JSON`,
  );
}
