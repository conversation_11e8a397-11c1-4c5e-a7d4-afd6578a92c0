import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";
/**
 * Server side events sent when streaming a conversation response.
 */
export declare const SSETypes: {
    readonly ConversationResponseStarted: "conversation.response.started";
    readonly ConversationResponseDone: "conversation.response.done";
    readonly ConversationResponseError: "conversation.response.error";
    readonly MessageOutputDelta: "message.output.delta";
    readonly ToolExecutionStarted: "tool.execution.started";
    readonly ToolExecutionDelta: "tool.execution.delta";
    readonly ToolExecutionDone: "tool.execution.done";
    readonly AgentHandoffStarted: "agent.handoff.started";
    readonly AgentHandoffDone: "agent.handoff.done";
    readonly FunctionCallDelta: "function.call.delta";
};
/**
 * Server side events sent when streaming a conversation response.
 */
export type SSETypes = ClosedEnum<typeof SSETypes>;
/** @internal */
export declare const SSETypes$inboundSchema: z.<PERSON>odNative<PERSON>num<typeof SSETypes>;
/** @internal */
export declare const SSETypes$outboundSchema: z.ZodNativeEnum<typeof SSETypes>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace SSETypes$ {
    /** @deprecated use `SSETypes$inboundSchema` instead. */
    const inboundSchema: z.ZodNativeEnum<{
        readonly ConversationResponseStarted: "conversation.response.started";
        readonly ConversationResponseDone: "conversation.response.done";
        readonly ConversationResponseError: "conversation.response.error";
        readonly MessageOutputDelta: "message.output.delta";
        readonly ToolExecutionStarted: "tool.execution.started";
        readonly ToolExecutionDelta: "tool.execution.delta";
        readonly ToolExecutionDone: "tool.execution.done";
        readonly AgentHandoffStarted: "agent.handoff.started";
        readonly AgentHandoffDone: "agent.handoff.done";
        readonly FunctionCallDelta: "function.call.delta";
    }>;
    /** @deprecated use `SSETypes$outboundSchema` instead. */
    const outboundSchema: z.ZodNativeEnum<{
        readonly ConversationResponseStarted: "conversation.response.started";
        readonly ConversationResponseDone: "conversation.response.done";
        readonly ConversationResponseError: "conversation.response.error";
        readonly MessageOutputDelta: "message.output.delta";
        readonly ToolExecutionStarted: "tool.execution.started";
        readonly ToolExecutionDelta: "tool.execution.delta";
        readonly ToolExecutionDone: "tool.execution.done";
        readonly AgentHandoffStarted: "agent.handoff.started";
        readonly AgentHandoffDone: "agent.handoff.done";
        readonly FunctionCallDelta: "function.call.delta";
    }>;
}
//# sourceMappingURL=ssetypes.d.ts.map