{"ast": null, "code": "import api from \"./api\";\n\n// Hỏi đáp AI với câu hỏi từ database\nexport const askQuestionWithAI = async _ref => {\n  let {\n    questionId,\n    messageId = 1\n  } = _ref;\n  const payload = {\n    messageId\n  };\n  return await api.post(\"/v1/ai/ask-question/\".concat(questionId), payload);\n};\n\n// Gọi GPT API trực tiếp (route gốc)\nexport const callGPT = async messages => {\n  return await api.post('/v1/gpt', {\n    messages\n  });\n};\nexport const classifyQuestions = async questions => {\n  return await api.post('/v1/ai/classify-questions', {\n    questions\n  });\n};\n\n// Sửa chính tả và ký hiệu LaTeX\nexport const fixTextAndLatex = async text => {\n  return await api.post('/v1/ai/fix-text-latex', {\n    text\n  });\n};", "map": {"version": 3, "names": ["api", "askQuestionWithAI", "_ref", "questionId", "messageId", "payload", "post", "concat", "callGPT", "messages", "classifyQuestions", "questions", "fixTextAndLatex", "text"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/services/aiApi.js"], "sourcesContent": ["import api from \"./api\";\n\n// Hỏi đáp AI với câu hỏi từ database\nexport const askQuestionWithAI = async ({ questionId, messageId = 1 }) => {\n    const payload = { messageId };\n\n    return await api.post(`/v1/ai/ask-question/${questionId}`, payload);\n};\n\n// Gọi GPT API trực tiếp (route gốc)\nexport const callGPT = async (messages) => {\n    return await api.post('/v1/gpt', { messages });\n};\n\nexport const classifyQuestions = async (questions) => {\n    return await api.post('/v1/ai/classify-questions', { questions });\n};\n\n// Sửa chính tả và ký hiệu LaTeX\nexport const fixTextAndLatex = async (text) => {\n    return await api.post('/v1/ai/fix-text-latex', { text });\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;;AAEvB;AACA,OAAO,MAAMC,iBAAiB,GAAG,MAAAC,IAAA,IAAyC;EAAA,IAAlC;IAAEC,UAAU;IAAEC,SAAS,GAAG;EAAE,CAAC,GAAAF,IAAA;EACjE,MAAMG,OAAO,GAAG;IAAED;EAAU,CAAC;EAE7B,OAAO,MAAMJ,GAAG,CAACM,IAAI,wBAAAC,MAAA,CAAwBJ,UAAU,GAAIE,OAAO,CAAC;AACvE,CAAC;;AAED;AACA,OAAO,MAAMG,OAAO,GAAG,MAAOC,QAAQ,IAAK;EACvC,OAAO,MAAMT,GAAG,CAACM,IAAI,CAAC,SAAS,EAAE;IAAEG;EAAS,CAAC,CAAC;AAClD,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAG,MAAOC,SAAS,IAAK;EAClD,OAAO,MAAMX,GAAG,CAACM,IAAI,CAAC,2BAA2B,EAAE;IAAEK;EAAU,CAAC,CAAC;AACrE,CAAC;;AAED;AACA,OAAO,MAAMC,eAAe,GAAG,MAAOC,IAAI,IAAK;EAC3C,OAAO,MAAMb,GAAG,CAACM,IAAI,CAAC,uBAAuB,EAAE;IAAEO;EAAK,CAAC,CAAC;AAC5D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}