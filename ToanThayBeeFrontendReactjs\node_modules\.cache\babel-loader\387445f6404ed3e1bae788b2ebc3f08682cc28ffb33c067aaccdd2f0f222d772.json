{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\RightContent.jsx\",\n  _s = $RefreshSig$();\nimport NavigateBar from \"../PageAddExam/NavigateBar\";\nimport { Eye, Edit, Sparkles } from \"lucide-react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setViewRightContent, putQuestionsExam, setQuestions } from \"src/features/questionsExam/questionsExamSlice\";\nimport QuestionView from \"./QuestionView\";\nimport ImageView from \"./ImageView\";\nimport LoadingSpinner from \"../loading/LoadingSpinner\";\nimport { classifyQuestions } from \"src/features/ai/aiSlice\";\nimport { useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RightContent = _ref => {\n  _s();\n  let {\n    examId\n  } = _ref;\n  const {\n    view,\n    loadingPut,\n    questionsExam\n  } = useSelector(state => state.questionsExam);\n  const dispatch = useDispatch();\n  const handlePutQuestions = () => {\n    if (!examId) return;\n    const questionsData = questionsExam.map((question, index) => {\n      var _question$ExamQuestio;\n      return {\n        id: question.id,\n        content: question.content,\n        correctAnswer: question.correctAnswer || null,\n        difficulty: question.difficulty || null,\n        chapter: question.chapter || null,\n        class: question.class,\n        solution: question.solution,\n        solutionUrl: question.solutionUrl || null,\n        imageUrl: question.imageUrl || null,\n        solutionImageUrl: question.solutionImageUrl || null,\n        statements: question.statements.map(statement => ({\n          id: statement.id,\n          content: statement.content,\n          isCorrect: statement.isCorrect,\n          difficulty: statement.difficulty,\n          imageUrl: statement.imageUrl,\n          isNewStatement: statement.isNewStatement || false\n        })),\n        ExamQuestions: {\n          order: ((_question$ExamQuestio = question.ExamQuestions) === null || _question$ExamQuestio === void 0 ? void 0 : _question$ExamQuestio.order) || index\n        },\n        isNewQuestion: question.isNewQuestion || false,\n        typeOfQuestion: question.typeOfQuestion,\n        description: question.description\n      };\n    });\n    // console.log(\"questionsData\", questionsData)\n\n    dispatch(putQuestionsExam({\n      examId,\n      questions: questionsData\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_138px)] bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Eye, {\n          className: \"w-3 h-3 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xs font-semibold text-gray-900\",\n          children: \"Xem tr\\u01B0\\u1EDBc\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          disabled: loadingPut,\n          onClick: handlePutQuestions,\n          className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-orange-600 hover:bg-orange-700 text-white\",\n          children: [loadingPut ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n            className: \"w-3 h-3\",\n            minHeight: \"min-h-0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 39\n          }, this) : /*#__PURE__*/_jsxDEV(Edit, {\n            className: \"w-3 h-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 100\n          }, this), \"C\\u1EADp nh\\u1EADt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded border border-gray-200 \",\n        children: [/*#__PURE__*/_jsxDEV(NavigateBar, {\n          list: [{\n            id: 1,\n            name: 'Câu hỏi',\n            value: 'question'\n          }, {\n            id: 2,\n            name: 'Ảnh',\n            value: 'image'\n          }],\n          active: view,\n          setActive: value => dispatch(setViewRightContent(value))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 21\n        }, this), view === 'question' && /*#__PURE__*/_jsxDEV(QuestionView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 45\n        }, this), view === 'image' && /*#__PURE__*/_jsxDEV(ImageView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 42\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 9\n  }, this);\n};\n_s(RightContent, \"CumCkfm2CeHVvBGSzRzw9HcPluY=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = RightContent;\nexport default RightContent;\nvar _c;\n$RefreshReg$(_c, \"RightContent\");", "map": {"version": 3, "names": ["NavigateBar", "Eye", "Edit", "<PERSON><PERSON><PERSON>", "useSelector", "useDispatch", "setViewRightContent", "putQuestionsExam", "setQuestions", "Question<PERSON>iew", "ImageView", "LoadingSpinner", "classifyQuestions", "useEffect", "jsxDEV", "_jsxDEV", "RightContent", "_ref", "_s", "examId", "view", "loadingPut", "questionsExam", "state", "dispatch", "handlePutQuestions", "questionsData", "map", "question", "index", "_question$ExamQuestio", "id", "content", "<PERSON><PERSON><PERSON><PERSON>", "difficulty", "chapter", "class", "solution", "solutionUrl", "imageUrl", "solutionImageUrl", "statements", "statement", "isCorrect", "isNewStatement", "ExamQuestions", "order", "isNewQuestion", "typeOfQuestion", "description", "questions", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "onClick", "minHeight", "list", "name", "value", "active", "setActive", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/RightContent.jsx"], "sourcesContent": ["import NavigateBar from \"../PageAddExam/NavigateBar\";\r\nimport { Eye, Edit, Sparkles } from \"lucide-react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { setViewRightContent, putQuestionsExam, setQuestions } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport QuestionView from \"./QuestionView\";\r\nimport ImageView from \"./ImageView\";\r\nimport LoadingSpinner from \"../loading/LoadingSpinner\";\r\nimport { classifyQuestions } from \"src/features/ai/aiSlice\";\r\nimport { useEffect } from \"react\";\r\n\r\nconst RightContent = ({ examId }) => {\r\n    const { view, loadingPut, questionsExam } = useSelector((state) => state.questionsExam);\r\n    const dispatch = useDispatch();\r\n\r\n    const handlePutQuestions = () => {\r\n        if (!examId) return;\r\n\r\n        const questionsData = questionsExam.map((question, index) => {\r\n            return {\r\n                id: question.id,\r\n                content: question.content, \r\n                correctAnswer: question.correctAnswer || null, \r\n                difficulty: question.difficulty || null,\r\n                chapter: question.chapter || null, \r\n                class: question.class, \r\n                solution: question.solution,\r\n                solutionUrl: question.solutionUrl || null,\r\n                imageUrl: question.imageUrl || null,\r\n                solutionImageUrl: question.solutionImageUrl || null,\r\n                statements: question.statements.map((statement) => ({\r\n                    id: statement.id,\r\n                    content: statement.content,\r\n                    isCorrect: statement.isCorrect,\r\n                    difficulty: statement.difficulty,\r\n                    imageUrl: statement.imageUrl,\r\n                    isNewStatement: statement.isNewStatement || false,\r\n                })),\r\n                ExamQuestions: {\r\n                    order: question.ExamQuestions?.order || index,\r\n                },\r\n                isNewQuestion: question.isNewQuestion || false,\r\n                typeOfQuestion: question.typeOfQuestion,\r\n                description: question.description,\r\n            }\r\n        })\r\n        // console.log(\"questionsData\", questionsData)\r\n\r\n        dispatch(putQuestionsExam({ examId, questions: questionsData }));\r\n    }\r\n\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_138px)] bg-gray-50\">\r\n            {/* Compact Preview Header */}\r\n            <div className=\"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <Eye className=\"w-3 h-3 text-gray-600\" />\r\n                    <h2 className=\"text-xs font-semibold text-gray-900\">Xem trước</h2>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                    <button\r\n                        disabled={loadingPut}\r\n                        onClick={handlePutQuestions}\r\n                        className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-orange-600 hover:bg-orange-700 text-white`}\r\n                    >\r\n                        {loadingPut ? <LoadingSpinner className=\"w-3 h-3\" minHeight=\"min-h-0\" /> : <Edit className=\"w-3 h-3\" />}\r\n                        \r\n                        Cập nhật\r\n                    </button>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Scrollable Preview Content */}\r\n            <div className=\"flex-1 overflow-y-auto p-3\">\r\n                <div className=\"bg-white rounded border border-gray-200 \">\r\n                    <NavigateBar\r\n                        list={[\r\n                            {\r\n                                id: 1,\r\n                                name: 'Câu hỏi',\r\n                                value: 'question'\r\n                            },\r\n                            {\r\n                                id: 2,\r\n                                name: 'Ảnh',\r\n                                value: 'image'\r\n                            }\r\n                        ]}\r\n                        active={view}\r\n                        setActive={(value) => dispatch(setViewRightContent(value))}\r\n                    />\r\n                    {view === 'question' && <QuestionView />}\r\n                    {view === 'image' && <ImageView />}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default RightContent;"], "mappings": ";;AAAA,OAAOA,WAAW,MAAM,4BAA4B;AACpD,SAASC,GAAG,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,cAAc;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,mBAAmB,EAAEC,gBAAgB,EAAEC,YAAY,QAAQ,+CAA+C;AACnH,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,YAAY,GAAGC,IAAA,IAAgB;EAAAC,EAAA;EAAA,IAAf;IAAEC;EAAO,CAAC,GAAAF,IAAA;EAC5B,MAAM;IAAEG,IAAI;IAAEC,UAAU;IAAEC;EAAc,CAAC,GAAGlB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACD,aAAa,CAAC;EACvF,MAAME,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAE9B,MAAMoB,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACN,MAAM,EAAE;IAEb,MAAMO,aAAa,GAAGJ,aAAa,CAACK,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;MAAA,IAAAC,qBAAA;MACzD,OAAO;QACHC,EAAE,EAAEH,QAAQ,CAACG,EAAE;QACfC,OAAO,EAAEJ,QAAQ,CAACI,OAAO;QACzBC,aAAa,EAAEL,QAAQ,CAACK,aAAa,IAAI,IAAI;QAC7CC,UAAU,EAAEN,QAAQ,CAACM,UAAU,IAAI,IAAI;QACvCC,OAAO,EAAEP,QAAQ,CAACO,OAAO,IAAI,IAAI;QACjCC,KAAK,EAAER,QAAQ,CAACQ,KAAK;QACrBC,QAAQ,EAAET,QAAQ,CAACS,QAAQ;QAC3BC,WAAW,EAAEV,QAAQ,CAACU,WAAW,IAAI,IAAI;QACzCC,QAAQ,EAAEX,QAAQ,CAACW,QAAQ,IAAI,IAAI;QACnCC,gBAAgB,EAAEZ,QAAQ,CAACY,gBAAgB,IAAI,IAAI;QACnDC,UAAU,EAAEb,QAAQ,CAACa,UAAU,CAACd,GAAG,CAAEe,SAAS,KAAM;UAChDX,EAAE,EAAEW,SAAS,CAACX,EAAE;UAChBC,OAAO,EAAEU,SAAS,CAACV,OAAO;UAC1BW,SAAS,EAAED,SAAS,CAACC,SAAS;UAC9BT,UAAU,EAAEQ,SAAS,CAACR,UAAU;UAChCK,QAAQ,EAAEG,SAAS,CAACH,QAAQ;UAC5BK,cAAc,EAAEF,SAAS,CAACE,cAAc,IAAI;QAChD,CAAC,CAAC,CAAC;QACHC,aAAa,EAAE;UACXC,KAAK,EAAE,EAAAhB,qBAAA,GAAAF,QAAQ,CAACiB,aAAa,cAAAf,qBAAA,uBAAtBA,qBAAA,CAAwBgB,KAAK,KAAIjB;QAC5C,CAAC;QACDkB,aAAa,EAAEnB,QAAQ,CAACmB,aAAa,IAAI,KAAK;QAC9CC,cAAc,EAAEpB,QAAQ,CAACoB,cAAc;QACvCC,WAAW,EAAErB,QAAQ,CAACqB;MAC1B,CAAC;IACL,CAAC,CAAC;IACF;;IAEAzB,QAAQ,CAACjB,gBAAgB,CAAC;MAAEY,MAAM;MAAE+B,SAAS,EAAExB;IAAc,CAAC,CAAC,CAAC;EACpE,CAAC;EAGD,oBACIX,OAAA;IAAKoC,SAAS,EAAC,kDAAkD;IAAAC,QAAA,gBAE7DrC,OAAA;MAAKoC,SAAS,EAAC,oFAAoF;MAAAC,QAAA,gBAC/FrC,OAAA;QAAKoC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACpCrC,OAAA,CAACd,GAAG;UAACkD,SAAS,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCzC,OAAA;UAAIoC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,eACNzC,OAAA;QAAKoC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpCrC,OAAA;UACI0C,QAAQ,EAAEpC,UAAW;UACrBqC,OAAO,EAAEjC,kBAAmB;UAC5B0B,SAAS,qGAAsG;UAAAC,QAAA,GAE9G/B,UAAU,gBAAGN,OAAA,CAACJ,cAAc;YAACwC,SAAS,EAAC,SAAS;YAACQ,SAAS,EAAC;UAAS;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGzC,OAAA,CAACb,IAAI;YAACiD,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAAC,oBAG5G;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNzC,OAAA;MAAKoC,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACvCrC,OAAA;QAAKoC,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACrDrC,OAAA,CAACf,WAAW;UACR4D,IAAI,EAAE,CACF;YACI7B,EAAE,EAAE,CAAC;YACL8B,IAAI,EAAE,SAAS;YACfC,KAAK,EAAE;UACX,CAAC,EACD;YACI/B,EAAE,EAAE,CAAC;YACL8B,IAAI,EAAE,KAAK;YACXC,KAAK,EAAE;UACX,CAAC,CACH;UACFC,MAAM,EAAE3C,IAAK;UACb4C,SAAS,EAAGF,KAAK,IAAKtC,QAAQ,CAAClB,mBAAmB,CAACwD,KAAK,CAAC;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,EACDpC,IAAI,KAAK,UAAU,iBAAIL,OAAA,CAACN,YAAY;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACvCpC,IAAI,KAAK,OAAO,iBAAIL,OAAA,CAACL,SAAS;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAtC,EAAA,CAvFKF,YAAY;EAAA,QAC8BZ,WAAW,EACtCC,WAAW;AAAA;AAAA4D,EAAA,GAF1BjD,YAAY;AAyFlB,eAAeA,YAAY;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}