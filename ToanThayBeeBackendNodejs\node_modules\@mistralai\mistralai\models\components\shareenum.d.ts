import * as z from "zod";
import { OpenEnum } from "../../types/enums.js";
export declare const ShareEnum: {
    readonly Viewer: "Viewer";
    readonly Editor: "Editor";
};
export type ShareEnum = OpenEnum<typeof ShareEnum>;
/** @internal */
export declare const ShareEnum$inboundSchema: z.ZodType<ShareEnum, z.ZodTypeDef, unknown>;
/** @internal */
export declare const ShareEnum$outboundSchema: z.ZodType<ShareEnum, z.ZodTypeDef, ShareEnum>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ShareEnum$ {
    /** @deprecated use `ShareEnum$inboundSchema` instead. */
    const inboundSchema: z.ZodType<ShareEnum, z.ZodTypeDef, unknown>;
    /** @deprecated use `ShareEnum$outboundSchema` instead. */
    const outboundSchema: z.ZodType<ShareEnum, z.ZodTypeDef, ShareEnum>;
}
//# sourceMappingURL=shareenum.d.ts.map