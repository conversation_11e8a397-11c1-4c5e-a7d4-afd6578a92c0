import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";
export declare const ToolChoiceEnum: {
    readonly Auto: "auto";
    readonly None: "none";
    readonly Any: "any";
    readonly Required: "required";
};
export type ToolChoiceEnum = ClosedEnum<typeof ToolChoiceEnum>;
/** @internal */
export declare const ToolChoiceEnum$inboundSchema: z.ZodNativeEnum<typeof ToolChoiceEnum>;
/** @internal */
export declare const ToolChoiceEnum$outboundSchema: z.ZodNativeEnum<typeof ToolChoiceEnum>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ToolChoiceEnum$ {
    /** @deprecated use `ToolChoiceEnum$inboundSchema` instead. */
    const inboundSchema: z.ZodNativeEnum<{
        readonly Auto: "auto";
        readonly None: "none";
        readonly Any: "any";
        readonly Required: "required";
    }>;
    /** @deprecated use `ToolChoiceEnum$outboundSchema` instead. */
    const outboundSchema: z.ZodNativeEnum<{
        readonly Auto: "auto";
        readonly None: "none";
        readonly Any: "any";
        readonly Required: "required";
    }>;
}
//# sourceMappingURL=toolchoiceenum.d.ts.map