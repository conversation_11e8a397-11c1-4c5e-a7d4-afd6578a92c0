import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type DocumentTextContent = {
    text: string;
};
/** @internal */
export declare const DocumentTextContent$inboundSchema: z.ZodType<DocumentTextContent, z.ZodTypeDef, unknown>;
/** @internal */
export type DocumentTextContent$Outbound = {
    text: string;
};
/** @internal */
export declare const DocumentTextContent$outboundSchema: z.ZodType<DocumentTextContent$Outbound, z.ZodTypeDef, DocumentTextContent>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace DocumentTextContent$ {
    /** @deprecated use `DocumentTextContent$inboundSchema` instead. */
    const inboundSchema: z.ZodType<DocumentTextContent, z.ZodTypeDef, unknown>;
    /** @deprecated use `DocumentTextContent$outboundSchema` instead. */
    const outboundSchema: z.ZodType<DocumentTextContent$Outbound, z.ZodTypeDef, DocumentTextContent>;
    /** @deprecated use `DocumentTextContent$Outbound` instead. */
    type Outbound = DocumentTextContent$Outbound;
}
export declare function documentTextContentToJSON(documentTextContent: DocumentTextContent): string;
export declare function documentTextContentFromJSON(jsonString: string): SafeParseResult<DocumentTextContent, SDKValidationError>;
//# sourceMappingURL=documenttextcontent.d.ts.map