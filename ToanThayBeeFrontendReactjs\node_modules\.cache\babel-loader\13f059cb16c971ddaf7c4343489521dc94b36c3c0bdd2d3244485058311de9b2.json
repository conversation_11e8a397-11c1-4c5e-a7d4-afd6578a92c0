{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport * as examApi from \"../../services/examApi\";\n// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from \"../filter/filterSlice\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nimport { ocrPdfWithMistralAPI } from \"../../services/ocrExamApi\";\nexport const postExam = createAsyncThunk(\"addExam/postExam\", async (_ref, _ref2) => {\n  let {\n    examData,\n    examImage,\n    questions,\n    examFile\n  } = _ref;\n  let {\n    dispatch\n  } = _ref2;\n  return await apiHandler(dispatch, examApi.postExamAPI, {\n    examData,\n    examImage,\n    questions,\n    examFile\n  }, () => {}, true, false);\n});\nexport const ocrPdfWithMistral = createAsyncThunk(\"addExam/ocrPdfWithMistral\", async (file, _ref3) => {\n  let {\n    dispatch\n  } = _ref3;\n  return await apiHandler(dispatch, ocrPdfWithMistralAPI, file, () => {}, true, false);\n});\nconst initialState = {\n  loading: false,\n  step: 1,\n  view: \"exam\",\n  selectedIndex: 0,\n  questionTNContent: \"\",\n  questionDSContent: \"\",\n  questionTLNContent: \"\",\n  folder: \"questionImage\",\n  correctAnswerTN: \"\",\n  correctAnswerDS: \"\",\n  correctAnswerTLN: \"\",\n  showAddImagesModal: false,\n  examData: {\n    name: \"\",\n    typeOfExam: null,\n    class: null,\n    chapter: null,\n    year: null,\n    description: \"\",\n    testDuration: null,\n    passRate: null,\n    solutionUrl: \"\",\n    imageUrl: \"\",\n    public: false,\n    isClassroomExam: false\n  },\n  examImage: null,\n  questions: [],\n  examFile: null,\n  markDownExam: \"\",\n  loadingOcr: false,\n  ocrFile: null,\n  base64Images: []\n};\nconst addExamSlice = createSlice({\n  name: \"addExam\",\n  initialState,\n  reducers: {\n    nextStep: state => {\n      state.step++;\n    },\n    prevStep: state => {\n      state.step--;\n    },\n    setViewRightContent: (state, action) => {\n      state.view = action.payload;\n    },\n    setSelectedIndex: (state, action) => {\n      state.selectedIndex = action.payload;\n    },\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    setQuestionTNContent: (state, action) => {\n      state.questionTNContent = action.payload;\n    },\n    setQuestionDSContent: (state, action) => {\n      state.questionDSContent = action.payload;\n    },\n    setQuestionTLNContent: (state, action) => {\n      state.questionTLNContent = action.payload;\n    },\n    setCorrectAnswerTN: (state, action) => {\n      state.correctAnswerTN = action.payload;\n    },\n    setCorrectAnswerDS: (state, action) => {\n      state.correctAnswerDS = action.payload;\n    },\n    setCorrectAnswerTLN: (state, action) => {\n      state.correctAnswerTLN = action.payload;\n    },\n    setExamData: (state, action) => {\n      const {\n        field,\n        value\n      } = action.payload;\n      // console.log('Setting exam data in slice:', field, value);\n      state.examData[field] = value;\n    },\n    setExamImage: (state, action) => {\n      state.examImage = action.payload;\n    },\n    setExamFile: (state, action) => {\n      state.examFile = action.payload;\n    },\n    setStep: (state, action) => {\n      state.step = action.payload;\n    },\n    resetData: state => {\n      state = initialState;\n    },\n    setQuestions: (state, action) => {\n      state.questions = action.payload;\n    },\n    setShowAddImagesModal: (state, action) => {\n      state.showAddImagesModal = action.payload;\n    },\n    setOcrFile: (state, action) => {\n      state.ocrFile = action.payload;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(postExam.pending, state => {\n      state.loading = true;\n    }).addCase(postExam.fulfilled, () => initialState).addCase(postExam.rejected, state => {\n      state.loading = false;\n    }).addCase(ocrPdfWithMistral.pending, state => {\n      state.loadingOcr = true;\n    }).addCase(ocrPdfWithMistral.fulfilled, (state, action) => {\n      state.markDownExam = action.payload.markdown;\n      state.base64Images = action.payload.base64Images;\n      state.loadingOcr = false;\n    }).addCase(ocrPdfWithMistral.rejected, state => {\n      state.loadingOcr = false;\n    });\n  }\n});\nexport const {\n  setLoading,\n  resetData,\n  nextStep,\n  prevStep,\n  setExamData,\n  setStep,\n  setExamImage,\n  setExamFile,\n  setQuestionTNContent,\n  setQuestionDSContent,\n  setQuestionTLNContent,\n  setCorrectAnswerTN,\n  setCorrectAnswerDS,\n  setCorrectAnswerTLN,\n  setQuestions,\n  setSelectedIndex,\n  setShowAddImagesModal,\n  setViewRightContent,\n  setOcrFile\n} = addExamSlice.actions;\nexport default addExamSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "examApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ocrPdfWithMistralAPI", "postExam", "_ref", "_ref2", "examData", "examImage", "questions", "examFile", "dispatch", "postExamAPI", "ocrPdfWithMistral", "file", "_ref3", "initialState", "loading", "step", "view", "selectedIndex", "questionT<PERSON>ontent", "question<PERSON><PERSON><PERSON><PERSON>", "questionTLNContent", "folder", "correctAnswerTN", "correctAnswerDS", "correctAnswerTLN", "showAddImagesModal", "name", "typeOfExam", "class", "chapter", "year", "description", "testDuration", "passRate", "solutionUrl", "imageUrl", "public", "isClassroomExam", "markDownExam", "loadingOcr", "ocrFile", "base64Images", "addExamSlice", "reducers", "nextStep", "state", "prevStep", "setViewRightContent", "action", "payload", "setSelectedIndex", "setLoading", "setQuestionTNContent", "setQuestionDSContent", "setQuestionTLNContent", "setCorrectAnswerTN", "setCorrectAnswerDS", "setCorrectAnswerTLN", "setExamData", "field", "value", "setExamImage", "setExamFile", "setStep", "resetData", "setQuestions", "setShowAddImagesModal", "setOcrFile", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "markdown", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/addExam/addExamSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport * as examApi from \"../../services/examApi\";\r\n// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from \"../filter/filterSlice\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\nimport { ocrPdfWithMistralAPI } from \"../../services/ocrExamApi\";\r\n\r\nexport const postExam = createAsyncThunk(\r\n    \"addExam/postExam\",\r\n    async ({ examData, examImage, questions, examFile }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.postExamAPI, { examData, examImage, questions, examFile }, () => { }, true, false);\r\n    }\r\n);\r\n\r\nexport const ocrPdfWithMistral = createAsyncThunk(\r\n    \"addExam/ocrPdfWithMistral\",\r\n    async (file, { dispatch }) => {\r\n        return await apiHandler(dispatch, ocrPdfWithMistralAPI, file, () => { }, true, false);\r\n    }\r\n);\r\n\r\nconst initialState = {\r\n    loading: false,\r\n    step: 1,\r\n    view: \"exam\",\r\n    selectedIndex: 0,\r\n    questionTNContent: \"\",\r\n    questionDSContent: \"\",\r\n    questionTLNContent: \"\",\r\n    folder: \"questionImage\",\r\n    correctAnswerTN: \"\",\r\n    correctAnswerDS: \"\",\r\n    correctAnswerTLN: \"\",\r\n    showAddImagesModal: false,\r\n    examData:\r\n    {\r\n        name: \"\",\r\n        typeOfExam: null,\r\n        class: null,\r\n        chapter: null,\r\n        year: null,\r\n        description: \"\",\r\n        testDuration: null,\r\n        passRate: null,\r\n        solutionUrl: \"\",\r\n        imageUrl: \"\",\r\n        public: false,\r\n        isClassroomExam: false,\r\n    },\r\n    examImage: null,\r\n    questions: [],\r\n    examFile: null,\r\n    markDownExam: \"\",\r\n    loadingOcr: false,\r\n    ocrFile: null,\r\n    base64Images: [],\r\n}\r\n\r\nconst addExamSlice = createSlice({\r\n    name: \"addExam\",\r\n    initialState,\r\n    reducers: {\r\n        nextStep: (state) => {\r\n            state.step++;\r\n        },\r\n        prevStep: (state) => {\r\n            state.step--;\r\n        },\r\n        setViewRightContent: (state, action) => {\r\n            state.view = action.payload;\r\n        },\r\n        setSelectedIndex: (state, action) => {\r\n            state.selectedIndex = action.payload;\r\n        },\r\n        setLoading: (state, action) => {\r\n            state.loading = action.payload;\r\n        },\r\n        setQuestionTNContent: (state, action) => {\r\n            state.questionTNContent = action.payload;\r\n        },\r\n        setQuestionDSContent: (state, action) => {\r\n            state.questionDSContent = action.payload;\r\n        },\r\n        setQuestionTLNContent: (state, action) => {\r\n            state.questionTLNContent = action.payload;\r\n        },\r\n        setCorrectAnswerTN: (state, action) => {\r\n            state.correctAnswerTN = action.payload;\r\n        },\r\n        setCorrectAnswerDS: (state, action) => {\r\n            state.correctAnswerDS = action.payload;\r\n        },\r\n        setCorrectAnswerTLN: (state, action) => {\r\n            state.correctAnswerTLN = action.payload;\r\n        },\r\n\r\n        setExamData: (state, action) => {\r\n            const { field, value } = action.payload;\r\n            // console.log('Setting exam data in slice:', field, value);\r\n            state.examData[field] = value;\r\n        },\r\n        setExamImage: (state, action) => {\r\n            state.examImage = action.payload;\r\n        },\r\n        setExamFile: (state, action) => {\r\n            state.examFile = action.payload;\r\n        },\r\n        setStep: (state, action) => {\r\n            state.step = action.payload;\r\n        },\r\n        resetData: (state) => {\r\n            state = initialState;\r\n        },\r\n        setQuestions: (state, action) => {\r\n            state.questions = action.payload;\r\n        },\r\n        setShowAddImagesModal: (state, action) => {\r\n            state.showAddImagesModal = action.payload;\r\n        },\r\n        setOcrFile: (state, action) => {\r\n            state.ocrFile = action.payload;\r\n        },\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(postExam.pending, (state) => {\r\n                state.loading = true;\r\n            })\r\n            .addCase(postExam.fulfilled, () => initialState)\r\n            .addCase(postExam.rejected, (state) => {\r\n                state.loading = false;\r\n            })\r\n            .addCase(ocrPdfWithMistral.pending, (state) => {\r\n                state.loadingOcr = true;\r\n            })\r\n            .addCase(ocrPdfWithMistral.fulfilled, (state, action) => {\r\n                state.markDownExam = action.payload.markdown;\r\n                state.base64Images = action.payload.base64Images;\r\n                state.loadingOcr = false;\r\n            })\r\n            .addCase(ocrPdfWithMistral.rejected, (state) => {\r\n                state.loadingOcr = false;\r\n            })\r\n    }\r\n});\r\n\r\nexport const {\r\n    setLoading,\r\n    resetData,\r\n    nextStep,\r\n    prevStep,\r\n    setExamData,\r\n    setStep,\r\n    setExamImage,\r\n    setExamFile,\r\n    setQuestionTNContent,\r\n    setQuestionDSContent,\r\n    setQuestionTLNContent,\r\n    setCorrectAnswerTN,\r\n    setCorrectAnswerDS,\r\n    setCorrectAnswerTLN,\r\n    setQuestions,\r\n    setSelectedIndex,\r\n    setShowAddImagesModal,\r\n    setViewRightContent,\r\n    setOcrFile,\r\n} = addExamSlice.actions;\r\nexport default addExamSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,OAAO,KAAKC,OAAO,MAAM,wBAAwB;AACjD;AACA,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,oBAAoB,QAAQ,2BAA2B;AAEhE,OAAO,MAAMC,QAAQ,GAAGJ,gBAAgB,CACpC,kBAAkB,EAClB,OAAAK,IAAA,EAAAC,KAAA,KAAsE;EAAA,IAA/D;IAAEC,QAAQ;IAAEC,SAAS;IAAEC,SAAS;IAAEC;EAAS,CAAC,GAAAL,IAAA;EAAA,IAAE;IAAEM;EAAS,CAAC,GAAAL,KAAA;EAC7D,OAAO,MAAMJ,UAAU,CAACS,QAAQ,EAAEV,OAAO,CAACW,WAAW,EAAE;IAAEL,QAAQ;IAAEC,SAAS;IAAEC,SAAS;IAAEC;EAAS,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AAChI,CACJ,CAAC;AAED,OAAO,MAAMG,iBAAiB,GAAGb,gBAAgB,CAC7C,2BAA2B,EAC3B,OAAOc,IAAI,EAAAC,KAAA,KAAmB;EAAA,IAAjB;IAAEJ;EAAS,CAAC,GAAAI,KAAA;EACrB,OAAO,MAAMb,UAAU,CAACS,QAAQ,EAAER,oBAAoB,EAAEW,IAAI,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACzF,CACJ,CAAC;AAED,MAAME,YAAY,GAAG;EACjBC,OAAO,EAAE,KAAK;EACdC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,MAAM;EACZC,aAAa,EAAE,CAAC;EAChBC,iBAAiB,EAAE,EAAE;EACrBC,iBAAiB,EAAE,EAAE;EACrBC,kBAAkB,EAAE,EAAE;EACtBC,MAAM,EAAE,eAAe;EACvBC,eAAe,EAAE,EAAE;EACnBC,eAAe,EAAE,EAAE;EACnBC,gBAAgB,EAAE,EAAE;EACpBC,kBAAkB,EAAE,KAAK;EACzBrB,QAAQ,EACR;IACIsB,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,KAAK;IACbC,eAAe,EAAE;EACrB,CAAC;EACDhC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,IAAI;EACd+B,YAAY,EAAE,EAAE;EAChBC,UAAU,EAAE,KAAK;EACjBC,OAAO,EAAE,IAAI;EACbC,YAAY,EAAE;AAClB,CAAC;AAED,MAAMC,YAAY,GAAG9C,WAAW,CAAC;EAC7B8B,IAAI,EAAE,SAAS;EACfb,YAAY;EACZ8B,QAAQ,EAAE;IACNC,QAAQ,EAAGC,KAAK,IAAK;MACjBA,KAAK,CAAC9B,IAAI,EAAE;IAChB,CAAC;IACD+B,QAAQ,EAAGD,KAAK,IAAK;MACjBA,KAAK,CAAC9B,IAAI,EAAE;IAChB,CAAC;IACDgC,mBAAmB,EAAEA,CAACF,KAAK,EAAEG,MAAM,KAAK;MACpCH,KAAK,CAAC7B,IAAI,GAAGgC,MAAM,CAACC,OAAO;IAC/B,CAAC;IACDC,gBAAgB,EAAEA,CAACL,KAAK,EAAEG,MAAM,KAAK;MACjCH,KAAK,CAAC5B,aAAa,GAAG+B,MAAM,CAACC,OAAO;IACxC,CAAC;IACDE,UAAU,EAAEA,CAACN,KAAK,EAAEG,MAAM,KAAK;MAC3BH,KAAK,CAAC/B,OAAO,GAAGkC,MAAM,CAACC,OAAO;IAClC,CAAC;IACDG,oBAAoB,EAAEA,CAACP,KAAK,EAAEG,MAAM,KAAK;MACrCH,KAAK,CAAC3B,iBAAiB,GAAG8B,MAAM,CAACC,OAAO;IAC5C,CAAC;IACDI,oBAAoB,EAAEA,CAACR,KAAK,EAAEG,MAAM,KAAK;MACrCH,KAAK,CAAC1B,iBAAiB,GAAG6B,MAAM,CAACC,OAAO;IAC5C,CAAC;IACDK,qBAAqB,EAAEA,CAACT,KAAK,EAAEG,MAAM,KAAK;MACtCH,KAAK,CAACzB,kBAAkB,GAAG4B,MAAM,CAACC,OAAO;IAC7C,CAAC;IACDM,kBAAkB,EAAEA,CAACV,KAAK,EAAEG,MAAM,KAAK;MACnCH,KAAK,CAACvB,eAAe,GAAG0B,MAAM,CAACC,OAAO;IAC1C,CAAC;IACDO,kBAAkB,EAAEA,CAACX,KAAK,EAAEG,MAAM,KAAK;MACnCH,KAAK,CAACtB,eAAe,GAAGyB,MAAM,CAACC,OAAO;IAC1C,CAAC;IACDQ,mBAAmB,EAAEA,CAACZ,KAAK,EAAEG,MAAM,KAAK;MACpCH,KAAK,CAACrB,gBAAgB,GAAGwB,MAAM,CAACC,OAAO;IAC3C,CAAC;IAEDS,WAAW,EAAEA,CAACb,KAAK,EAAEG,MAAM,KAAK;MAC5B,MAAM;QAAEW,KAAK;QAAEC;MAAM,CAAC,GAAGZ,MAAM,CAACC,OAAO;MACvC;MACAJ,KAAK,CAACzC,QAAQ,CAACuD,KAAK,CAAC,GAAGC,KAAK;IACjC,CAAC;IACDC,YAAY,EAAEA,CAAChB,KAAK,EAAEG,MAAM,KAAK;MAC7BH,KAAK,CAACxC,SAAS,GAAG2C,MAAM,CAACC,OAAO;IACpC,CAAC;IACDa,WAAW,EAAEA,CAACjB,KAAK,EAAEG,MAAM,KAAK;MAC5BH,KAAK,CAACtC,QAAQ,GAAGyC,MAAM,CAACC,OAAO;IACnC,CAAC;IACDc,OAAO,EAAEA,CAAClB,KAAK,EAAEG,MAAM,KAAK;MACxBH,KAAK,CAAC9B,IAAI,GAAGiC,MAAM,CAACC,OAAO;IAC/B,CAAC;IACDe,SAAS,EAAGnB,KAAK,IAAK;MAClBA,KAAK,GAAGhC,YAAY;IACxB,CAAC;IACDoD,YAAY,EAAEA,CAACpB,KAAK,EAAEG,MAAM,KAAK;MAC7BH,KAAK,CAACvC,SAAS,GAAG0C,MAAM,CAACC,OAAO;IACpC,CAAC;IACDiB,qBAAqB,EAAEA,CAACrB,KAAK,EAAEG,MAAM,KAAK;MACtCH,KAAK,CAACpB,kBAAkB,GAAGuB,MAAM,CAACC,OAAO;IAC7C,CAAC;IACDkB,UAAU,EAAEA,CAACtB,KAAK,EAAEG,MAAM,KAAK;MAC3BH,KAAK,CAACL,OAAO,GAAGQ,MAAM,CAACC,OAAO;IAClC;EACJ,CAAC;EACDmB,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAACrE,QAAQ,CAACsE,OAAO,EAAG1B,KAAK,IAAK;MAClCA,KAAK,CAAC/B,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACDwD,OAAO,CAACrE,QAAQ,CAACuE,SAAS,EAAE,MAAM3D,YAAY,CAAC,CAC/CyD,OAAO,CAACrE,QAAQ,CAACwE,QAAQ,EAAG5B,KAAK,IAAK;MACnCA,KAAK,CAAC/B,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDwD,OAAO,CAAC5D,iBAAiB,CAAC6D,OAAO,EAAG1B,KAAK,IAAK;MAC3CA,KAAK,CAACN,UAAU,GAAG,IAAI;IAC3B,CAAC,CAAC,CACD+B,OAAO,CAAC5D,iBAAiB,CAAC8D,SAAS,EAAE,CAAC3B,KAAK,EAAEG,MAAM,KAAK;MACrDH,KAAK,CAACP,YAAY,GAAGU,MAAM,CAACC,OAAO,CAACyB,QAAQ;MAC5C7B,KAAK,CAACJ,YAAY,GAAGO,MAAM,CAACC,OAAO,CAACR,YAAY;MAChDI,KAAK,CAACN,UAAU,GAAG,KAAK;IAC5B,CAAC,CAAC,CACD+B,OAAO,CAAC5D,iBAAiB,CAAC+D,QAAQ,EAAG5B,KAAK,IAAK;MAC5CA,KAAK,CAACN,UAAU,GAAG,KAAK;IAC5B,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EACTY,UAAU;EACVa,SAAS;EACTpB,QAAQ;EACRE,QAAQ;EACRY,WAAW;EACXK,OAAO;EACPF,YAAY;EACZC,WAAW;EACXV,oBAAoB;EACpBC,oBAAoB;EACpBC,qBAAqB;EACrBC,kBAAkB;EAClBC,kBAAkB;EAClBC,mBAAmB;EACnBQ,YAAY;EACZf,gBAAgB;EAChBgB,qBAAqB;EACrBnB,mBAAmB;EACnBoB;AACJ,CAAC,GAAGzB,YAAY,CAACiC,OAAO;AACxB,eAAejC,YAAY,CAACkC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}