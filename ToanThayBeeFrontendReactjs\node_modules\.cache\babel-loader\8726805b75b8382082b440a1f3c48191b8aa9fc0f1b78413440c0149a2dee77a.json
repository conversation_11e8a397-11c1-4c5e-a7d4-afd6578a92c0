{"ast": null, "code": "import api from \"./api\";\nexport const ocrPdfWithMistralAPI = async file => {\n  const formData = new FormData();\n  formData.append(\"pdf\", file);\n  const response = await api.post(\"/v1/AI/handle/exam/pdf\", formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\"\n    }\n  });\n  return response.data;\n};", "map": {"version": 3, "names": ["api", "ocrPdfWithMistralAPI", "file", "formData", "FormData", "append", "response", "post", "headers", "data"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/services/ocrExamApi.js"], "sourcesContent": ["import api from \"./api\";\r\n\r\nexport const ocrPdfWithMistralAPI = async (file) => {\r\n    const formData = new FormData();\r\n    formData.append(\"pdf\", file);\r\n    const response = await api.post(\"/v1/AI/handle/exam/pdf\", formData, {\r\n        headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n    });\r\n    return response.data;\r\n}\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,OAAO,MAAMC,oBAAoB,GAAG,MAAOC,IAAI,IAAK;EAChD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAEH,IAAI,CAAC;EAC5B,MAAMI,QAAQ,GAAG,MAAMN,GAAG,CAACO,IAAI,CAAC,wBAAwB,EAAEJ,QAAQ,EAAE;IAChEK,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,OAAOF,QAAQ,CAACG,IAAI;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}