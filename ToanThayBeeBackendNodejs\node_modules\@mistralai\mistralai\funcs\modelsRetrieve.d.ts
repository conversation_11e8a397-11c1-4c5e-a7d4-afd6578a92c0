import { MistralCore } from "../core.js";
import { RequestOptions } from "../lib/sdks.js";
import { ConnectionError, InvalidRequestError, RequestAbortedError, RequestTimeoutError, UnexpectedClientError } from "../models/errors/httpclienterrors.js";
import * as errors from "../models/errors/index.js";
import { SDKError } from "../models/errors/sdkerror.js";
import { SDKValidationError } from "../models/errors/sdkvalidationerror.js";
import * as operations from "../models/operations/index.js";
import { APIPromise } from "../types/async.js";
import { Result } from "../types/fp.js";
/**
 * Retrieve Model
 *
 * @remarks
 * Retrieve information about a model.
 */
export declare function modelsRetrieve(client: MistralCore, request: operations.RetrieveModelV1ModelsModelIdGetRequest, options?: RequestOptions): APIPromise<Result<operations.RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet, errors.HTTPValidationError | SDKError | SDKValidationError | UnexpectedClientError | InvalidRequestError | RequestAbortedError | RequestTimeoutError | ConnectionError>>;
//# sourceMappingURL=modelsRetrieve.d.ts.map