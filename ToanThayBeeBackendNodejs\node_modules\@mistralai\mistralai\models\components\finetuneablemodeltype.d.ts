import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";
export declare const FineTuneableModelType: {
    readonly Completion: "completion";
    readonly Classifier: "classifier";
};
export type FineTuneableModelType = ClosedEnum<typeof FineTuneableModelType>;
/** @internal */
export declare const FineTuneableModelType$inboundSchema: z.ZodNativeEnum<typeof FineTuneableModelType>;
/** @internal */
export declare const FineTuneableModelType$outboundSchema: z.ZodNativeEnum<typeof FineTuneableModelType>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace FineTuneableModelType$ {
    /** @deprecated use `FineTuneableModelType$inboundSchema` instead. */
    const inboundSchema: z.ZodNativeEnum<{
        readonly Completion: "completion";
        readonly Classifier: "classifier";
    }>;
    /** @deprecated use `FineTuneableModelType$outboundSchema` instead. */
    const outboundSchema: z.<PERSON><{
        readonly Completion: "completion";
        readonly Classifier: "classifier";
    }>;
}
//# sourceMappingURL=finetuneablemodeltype.d.ts.map