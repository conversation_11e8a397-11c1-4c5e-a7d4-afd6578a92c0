{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport TextArea from \"src/components/input/TextArea\";\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\nimport SolutionEditor from \"src/components/PageAddExam/SolutionEditor\";\nimport { setQuestion } from \"src/features/questionsExam/questionsExamSlice\";\nimport { CheckCircle, Plus, Video, Sparkles } from \"lucide-react\";\nimport { setQuestions, setNewQuestion, addQuestion } from \"src/features/questionsExam/questionsExamSlice\";\nimport { addStatement } from \"src/features/questionsExam/questionsExamSlice\";\nimport { fixTextAndLatex } from \"src/features/ai/aiSlice\";\nimport { setErrorMessage } from \"src/features/state/stateApiSlice\";\nimport YouTubePlayer from \"../YouTubePlayer\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DetailQuestionView = () => {\n  _s();\n  const {\n    questionsExam,\n    selectedId,\n    view\n  } = useSelector(state => state.questionsExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const dispatch = useDispatch();\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const [optionChapter, setOptionChapter] = useState([]);\n  const [question, setQuestion] = useState(null);\n  useEffect(() => {\n    // console.log(selectedId);\n\n    if (selectedId && (questionsExam === null || questionsExam === void 0 ? void 0 : questionsExam.length) > 0) {\n      const selectedQuestion = questionsExam === null || questionsExam === void 0 ? void 0 : questionsExam.find(q => q.id === selectedId);\n      setQuestion(selectedQuestion);\n    }\n  }, [selectedId, questionsExam]);\n  const handleQuestionChange = (e, field) => {\n    const updatedQuestion = {\n      ...question,\n      [field]: e.target.value\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleStatementChange = (index, value, field) => {\n    const updatedStatements = [...question.statements];\n    updatedStatements[index] = {\n      ...updatedStatements[index],\n      [field]: value\n    };\n    const updatedQuestion = {\n      ...question,\n      statements: updatedStatements\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleSolutionQuestionChange = value => {\n    const updatedQuestion = {\n      ...question,\n      solution: value\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleAddStatement = () => {\n    dispatch(addStatement());\n  };\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      if (question !== null && question !== void 0 && question.class && (question === null || question === void 0 ? void 0 : question.class.trim()) !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.startsWith(question === null || question === void 0 ? void 0 : question.class) && code.code.length === 5));\n      } else {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, question === null || question === void 0 ? void 0 : question.class]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3 w-full\",\n    children: question && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Ph\\xE2n lo\\u1EA1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-row gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: question.class,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'class'),\n            options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n            selectedOption: question.chapter,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'chapter'),\n            options: optionChapter,\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: question.difficulty,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'difficulty'),\n            options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \" bg-gray-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Th\\xF4ng tin c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(TextArea, {\n            value: question.content,\n            onChange: e => handleQuestionChange(e, 'content'),\n            placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n            label: \"C\\xE2u h\\u1ECFi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 29\n          }, this), (view === 'image' || question.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n            imageUrl: question.imageUrl,\n            onImageDrop: image => handleQuestionChange({\n              target: {\n                value: image\n              }\n            }, 'imageUrl'),\n            onImageRemove: () => handleQuestionChange({\n              target: {\n                value: ''\n              }\n            }, 'imageUrl')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 33\n          }, this), question.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [question.statements.length < 4 && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleAddStatement(),\n              className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-blue-600 hover:bg-blue-700 text-white\",\n              children: [/*#__PURE__*/_jsxDEV(Plus, {\n                className: \"w-3 h-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 45\n              }, this), \" Th\\xEAm m\\u1EC7nh \\u0111\\u1EC1\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 41\n            }, this), question.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-2 items-center w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row gap-2 items-center w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs font-bold whitespace-nowrap\",\n                  children: question.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n                  value: statement.content,\n                  onChange: e => handleStatementChange(index, e.target.value, 'content'),\n                  placeholder: \"Nh\\u1EADp n\\u1ED9i dung m\\u1EC7nh \\u0111\\u1EC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 49\n                }, this), question.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"flex items-center gap-2 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"statement-\".concat(index),\n                      checked: statement.isCorrect,\n                      onChange: () => handleStatementChange(index, true, 'isCorrect'),\n                      className: \"w-4 h-4 text-green-600 focus:ring-green-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 61\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-green-600 font-medium\",\n                      children: \"\\u0110\\xFAng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 144,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 57\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"flex items-center gap-2 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"statement-\".concat(index),\n                      checked: !statement.isCorrect,\n                      onChange: () => handleStatementChange(index, false, 'isCorrect'),\n                      className: \"w-4 h-4 text-red-600 focus:ring-red-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 61\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-red-600 font-medium\",\n                      children: \"Sai\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 57\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 53\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 45\n              }, this), (view === 'image' || statement.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n                imageUrl: statement.imageUrl,\n                onImageDrop: image => handleStatementChange(index, image, 'imageUrl'),\n                onImageRemove: () => handleStatementChange(index, '', 'imageUrl')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 49\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 41\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 33\n          }, this), question.typeOfQuestion === 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              value: question.correctAnswer,\n              onChange: e => handleQuestionChange(e, 'correctAnswer'),\n              placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n              label: \"\\u0110\\xE1p \\xE1n\",\n              Icon: CheckCircle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n            value: question.solutionUrl || '',\n            onChange: e => handleQuestionChange(e, 'solutionUrl'),\n            placeholder: \"Nh\\u1EADp link l\\u1EDDi gi\\u1EA3i youtube\",\n            label: \"Link l\\u1EDDi gi\\u1EA3i\",\n            Icon: Video\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 29\n          }, this), question.solutionUrl && /*#__PURE__*/_jsxDEV(YouTubePlayer, {\n            url: question.solutionUrl\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(SolutionEditor, {\n            solution: question.solution,\n            onSolutionChange: handleSolutionQuestionChange,\n            preview: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 9\n  }, this);\n};\n_s(DetailQuestionView, \"DXB6iz/PdnPpihu4Y8AFD12VQkc=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = DetailQuestionView;\nconst AddQuestionView = () => {\n  _s2();\n  const dispatch = useDispatch();\n  const {\n    newQuestion\n  } = useSelector(state => state.questionsExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const [optionChapter, setOptionChapter] = useState([]);\n  const handleNewQuestionChange = (e, field) => {\n    const updatedQuestion = {\n      ...newQuestion,\n      [field]: e.target.value\n    };\n    dispatch(setNewQuestion(updatedQuestion));\n  };\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      if (newQuestion !== null && newQuestion !== void 0 && newQuestion.class && (newQuestion === null || newQuestion === void 0 ? void 0 : newQuestion.class.trim()) !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.startsWith(newQuestion === null || newQuestion === void 0 ? void 0 : newQuestion.class) && code.code.length === 5));\n      } else {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, newQuestion === null || newQuestion === void 0 ? void 0 : newQuestion.class]);\n  const handleNewStatementChange = (index, value, field) => {\n    const updatedStatements = [...newQuestion.statements];\n    updatedStatements[index] = {\n      ...updatedStatements[index],\n      [field]: value\n    };\n    const updatedQuestion = {\n      ...newQuestion,\n      statements: updatedStatements\n    };\n    dispatch(setNewQuestion(updatedQuestion));\n  };\n  const handleNewSolutionQuestionChange = value => {\n    const updatedQuestion = {\n      ...newQuestion,\n      solution: value\n    };\n    dispatch(setNewQuestion(updatedQuestion));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3 w-full\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-medium text-gray-900 mb-1\",\n        children: \"Ph\\xE2n lo\\u1EA1i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: newQuestion.typeOfQuestion,\n          onChange: option => handleNewQuestionChange({\n            target: {\n              value: option\n            }\n          }, 'typeOfQuestion'),\n          options: [{\n            code: \"TN\",\n            description: \"Trắc nghiệm\"\n          }, {\n            code: \"DS\",\n            description: \"Đúng sai\"\n          }, {\n            code: \"TLN\",\n            description: \"Trả lời ngắn\"\n          }],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: newQuestion.class,\n          onChange: option => handleNewQuestionChange({\n            target: {\n              value: option\n            }\n          }, 'class'),\n          options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n          selectedOption: newQuestion.chapter,\n          onChange: option => handleNewQuestionChange({\n            target: {\n              value: option\n            }\n          }, 'chapter'),\n          options: optionChapter,\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: newQuestion.difficulty,\n          onChange: option => handleNewQuestionChange({\n            target: {\n              value: option\n            }\n          }, 'difficulty'),\n          options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \" bg-gray-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Th\\xF4ng tin c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(TextArea, {\n            value: newQuestion.content,\n            onChange: e => handleNewQuestionChange(e, 'content'),\n            placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n            label: \"C\\xE2u h\\u1ECFi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(ImageDropZone, {\n            imageUrl: newQuestion.imageUrl,\n            onImageDrop: image => handleNewQuestionChange({\n              target: {\n                value: image\n              }\n            }, 'imageUrl'),\n            onImageRemove: () => handleNewQuestionChange({\n              target: {\n                value: ''\n              }\n            }, 'imageUrl')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 25\n          }, this), newQuestion.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: newQuestion.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-2 items-center w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row gap-2 items-center w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs font-bold whitespace-nowrap\",\n                  children: newQuestion.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n                  value: statement.content,\n                  onChange: e => handleNewStatementChange(index, e.target.value, 'content'),\n                  placeholder: \"Nh\\u1EADp n\\u1ED9i dung m\\u1EC7nh \\u0111\\u1EC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"flex items-center gap-2 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"statement-\".concat(index),\n                      checked: statement.isCorrect,\n                      onChange: () => handleNewStatementChange(index, true, 'isCorrect'),\n                      className: \"w-4 h-4 text-green-600 focus:ring-green-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-green-600 font-medium\",\n                      children: \"\\u0110\\xFAng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"flex items-center gap-2 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"statement-\".concat(index),\n                      checked: !statement.isCorrect,\n                      onChange: () => handleNewStatementChange(index, false, 'isCorrect'),\n                      className: \"w-4 h-4 text-red-600 focus:ring-red-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-red-600 font-medium\",\n                      children: \"Sai\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 328,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(ImageDropZone, {\n                imageUrl: statement.imageUrl,\n                onImageDrop: image => handleNewStatementChange(index, image, 'imageUrl'),\n                onImageRemove: () => handleNewStatementChange(index, '', 'imageUrl')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 41\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 29\n          }, this), newQuestion.typeOfQuestion === 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              value: newQuestion.correctAnswer,\n              onChange: e => handleNewQuestionChange(e, 'correctAnswer'),\n              placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n              label: \"\\u0110\\xE1p \\xE1n\",\n              Icon: CheckCircle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(TextArea, {\n          value: newQuestion.solutionUrl,\n          onChange: e => handleNewQuestionChange(e, 'solutionUrl'),\n          placeholder: \"Nh\\u1EADp link l\\u1EDDi gi\\u1EA3i youtube\",\n          label: \"Link l\\u1EDDi gi\\u1EA3i\",\n          Icon: Video\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 21\n        }, this), newQuestion.solutionUrl && /*#__PURE__*/_jsxDEV(YouTubePlayer, {\n          url: newQuestion.solutionUrl\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(SolutionEditor, {\n          solution: newQuestion.solution,\n          onSolutionChange: handleNewSolutionQuestionChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 9\n  }, this);\n};\n_s2(AddQuestionView, \"R7UojSO0cGUgJuTtpZmH3ejLoVQ=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c2 = AddQuestionView;\nconst LeftContent = () => {\n  _s3();\n  const [view, setView] = useState('questionDetail');\n  const {\n    newQuestion\n  } = useSelector(state => state.questionsExam);\n  const dispatch = useDispatch();\n  const handleAddQuestion = () => {\n    if (!newQuestion.content.trim()) {\n      dispatch(setErrorMessage(\"Nội dung câu hỏi không được để trống!\"));\n      return;\n    }\n    if (!newQuestion.typeOfQuestion) {\n      dispatch(setErrorMessage(\"Loại câu hỏi không được để trống!\"));\n      return;\n    }\n    if (!newQuestion.class) {\n      dispatch(setErrorMessage(\"Lớp không được để trống!\"));\n      return;\n    }\n    if (newQuestion.typeOfQuestion !== 'TLN' && newQuestion.statements.filter(statement => statement.content.trim() !== \"\").length < 4) {\n      dispatch(setErrorMessage(\"Câu hỏi TN phải có ít nhất 4 đáp án!\"));\n      return;\n    }\n    if (newQuestion.typeOfQuestion === 'TLN' && !newQuestion.correctAnswer.trim()) {\n      dispatch(setErrorMessage(\"Đáp án không được để trống!\"));\n      return;\n    }\n    dispatch(addQuestion());\n    setView('questionDetail');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_138px)] overflow-y-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xs font-semibold text-gray-900\",\n          children: \"Chi ti\\u1EBFt c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: view === 'questionDetail' ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setView('addQuestion'),\n          className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-sky-600 hover:bg-sky-700 text-white\",\n          children: \"Th\\xEAm c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddQuestion,\n            className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-emerald-600 hover:bg-emerald-700 text-white\",\n            children: \"L\\u01B0u\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setView('questionDetail'),\n            className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-red-600 hover:bg-red-700 text-white\",\n            children: \"H\\u1EE7y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 13\n    }, this), view === 'questionDetail' && /*#__PURE__*/_jsxDEV(DetailQuestionView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 43\n    }, this), view === 'addQuestion' && /*#__PURE__*/_jsxDEV(AddQuestionView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 40\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 409,\n    columnNumber: 9\n  }, this);\n};\n_s3(LeftContent, \"NcpmA1bQnPPdje5SWgr/AuDkEK4=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c3 = LeftContent;\nexport default LeftContent;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"DetailQuestionView\");\n$RefreshReg$(_c2, \"AddQuestionView\");\n$RefreshReg$(_c3, \"LeftContent\");", "map": {"version": 3, "names": ["useEffect", "useState", "useSelector", "useDispatch", "DropMenuBarAdmin", "SuggestInputBarAdmin", "TextArea", "ImageDropZone", "SolutionEditor", "setQuestion", "CheckCircle", "Plus", "Video", "<PERSON><PERSON><PERSON>", "setQuestions", "setNewQuestion", "addQuestion", "addStatement", "fixTextAndLatex", "setErrorMessage", "YouTubePlayer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DetailQuestionView", "_s", "questionsExam", "selectedId", "view", "state", "codes", "dispatch", "prefixTN", "prefixDS", "optionChapter", "setOptionChapter", "question", "length", "selectedQuestion", "find", "q", "id", "handleQuestionChange", "e", "field", "updatedQuestion", "target", "value", "handleStatementChange", "index", "updatedStatements", "statements", "handleSolutionQuestionChange", "solution", "handleAddStatement", "Array", "isArray", "class", "trim", "filter", "code", "startsWith", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selectedOption", "onChange", "option", "options", "chapter", "difficulty", "content", "placeholder", "label", "imageUrl", "onImageDrop", "image", "onImageRemove", "typeOfQuestion", "onClick", "map", "statement", "type", "name", "concat", "checked", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "Icon", "solutionUrl", "url", "onSolutionChange", "preview", "_c", "AddQuestionView", "_s2", "newQuestion", "handleNewQuestionChange", "handleNewStatementChange", "handleNewSolutionQuestionChange", "description", "_c2", "LeftContent", "_s3", "<PERSON><PERSON><PERSON><PERSON>", "handleAddQuestion", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/LeftContent.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport TextArea from \"src/components/input/TextArea\";\r\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\r\nimport SolutionEditor from \"src/components/PageAddExam/SolutionEditor\";\r\nimport { setQuestion } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport { CheckCircle, Plus, Video, Sparkles } from \"lucide-react\";\r\nimport { setQuestions, setNewQuestion, addQuestion } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport { addStatement } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport { fixTextAndLatex } from \"src/features/ai/aiSlice\";\r\nimport { setErrorMessage } from \"src/features/state/stateApiSlice\";\r\nimport YouTubePlayer from \"../YouTubePlayer\";\r\n\r\nconst DetailQuestionView = () => {\r\n    const { questionsExam, selectedId, view } = useSelector((state) => state.questionsExam);\r\n    const { codes } = useSelector((state) => state.codes);\r\n    const dispatch = useDispatch();\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n    const [question, setQuestion] = useState(null);\r\n\r\n    useEffect(() => {\r\n        // console.log(selectedId);\r\n\r\n        if (selectedId && questionsExam?.length > 0) {\r\n            const selectedQuestion = questionsExam?.find(q => q.id === selectedId);\r\n            setQuestion(selectedQuestion);\r\n        }\r\n    }, [selectedId, questionsExam]);\r\n\r\n    const handleQuestionChange = (e, field) => {\r\n        const updatedQuestion = { ...question, [field]: e.target.value };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleStatementChange = (index, value, field) => {\r\n        const updatedStatements = [...question.statements];\r\n        updatedStatements[index] = { ...updatedStatements[index], [field]: value };\r\n        const updatedQuestion = { ...question, statements: updatedStatements };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleSolutionQuestionChange = (value) => {\r\n        const updatedQuestion = { ...question, solution: value };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleAddStatement = () => {\r\n        dispatch(addStatement());\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (question?.class && question?.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.startsWith(question?.class) && code.code.length === 5)\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"].filter((code) => code.code.length === 5));\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, question?.class]);\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3 w-full\">\r\n            {question && (\r\n                <div className=\"space-y-3 w-full\">\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Phân loại</h3>\r\n                        <div className=\"flex flex-row gap-2\">\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={question.class}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'class')}\r\n                                options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                                className=\"text-xs\"\r\n                            />\r\n                            <SuggestInputBarAdmin\r\n                                selectedOption={question.chapter}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'chapter')}\r\n                                options={optionChapter}\r\n                                className=\"text-xs\"\r\n                            />\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={question.difficulty}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'difficulty')}\r\n                                options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                                className=\"text-xs\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <hr className=\" bg-gray-200\"></hr>\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thông tin câu hỏi</h3>\r\n                        <div className=\"space-y-2\">\r\n                            <TextArea\r\n                                value={question.content}\r\n                                onChange={(e) => handleQuestionChange(e, 'content')}\r\n                                placeholder=\"Nhập nội dung câu hỏi\"\r\n                                label=\"Câu hỏi\"\r\n                            />\r\n                            {(view === 'image' || question.imageUrl) && (\r\n                                <ImageDropZone\r\n                                    imageUrl={question.imageUrl}\r\n                                    onImageDrop={(image) => handleQuestionChange({ target: { value: image } }, 'imageUrl')}\r\n                                    onImageRemove={() => handleQuestionChange({ target: { value: '' } }, 'imageUrl')}\r\n                                />\r\n                            )}\r\n                            {question.typeOfQuestion !== 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    {question.statements.length < 4 && (\r\n                                        <button\r\n                                            onClick={() => handleAddStatement()}\r\n                                            className=\"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-blue-600 hover:bg-blue-700 text-white\"\r\n                                        >\r\n                                            <Plus className=\"w-3 h-3\" /> Thêm mệnh đề\r\n                                        </button>\r\n                                    )}\r\n                                    {question.statements.map((statement, index) => (\r\n                                        <div key={index} className=\"flex flex-col gap-2 items-center w-full\">\r\n                                            <div className=\"flex flex-row gap-2 items-center w-full\">\r\n                                                <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                                    {question.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}\r\n                                                </p>\r\n                                                <TextArea\r\n                                                    value={statement.content}\r\n                                                    onChange={(e) => handleStatementChange(index, e.target.value, 'content')}\r\n                                                    placeholder=\"Nhập nội dung mệnh đề\"\r\n                                                />\r\n                                                {question.typeOfQuestion !== 'TLN' && (\r\n                                                    <div className=\"flex items-center gap-2\">\r\n                                                        <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                                            <input\r\n                                                                type=\"radio\"\r\n                                                                name={`statement-${index}`}\r\n                                                                checked={statement.isCorrect}\r\n                                                                onChange={() => handleStatementChange(index, true, 'isCorrect')}\r\n                                                                className=\"w-4 h-4 text-green-600 focus:ring-green-500\"\r\n                                                            />\r\n                                                            <span className=\"text-sm text-green-600 font-medium\">Đúng</span>\r\n                                                        </label>\r\n                                                        <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                                            <input\r\n                                                                type=\"radio\"\r\n                                                                name={`statement-${index}`}\r\n                                                                checked={!statement.isCorrect}\r\n                                                                onChange={() => handleStatementChange(index, false, 'isCorrect')}\r\n                                                                className=\"w-4 h-4 text-red-600 focus:ring-red-500\"\r\n                                                            />\r\n                                                            <span className=\"text-sm text-red-600 font-medium\">Sai</span>\r\n                                                        </label>\r\n                                                    </div>\r\n                                                )}\r\n                                            </div>\r\n                                            {(view === 'image' || statement.imageUrl) && (\r\n                                                <ImageDropZone\r\n                                                    imageUrl={statement.imageUrl}\r\n                                                    onImageDrop={(image) => handleStatementChange(index, image, 'imageUrl')}\r\n                                                    onImageRemove={() => handleStatementChange(index, '', 'imageUrl')}\r\n                                                />\r\n                                            )}\r\n\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            )}\r\n                            {question.typeOfQuestion === 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    <TextArea\r\n                                        value={question.correctAnswer}\r\n                                        onChange={(e) => handleQuestionChange(e, 'correctAnswer')}\r\n                                        placeholder=\"Nhập đáp án\"\r\n                                        label=\"Đáp án\"\r\n                                        Icon={CheckCircle}\r\n                                    />\r\n                                </div>\r\n                            )}\r\n                            {/* <TextArea\r\n                                        value={question.solution}\r\n                                        onChange={(e) => handleQuestionChange(e, 'solution')}\r\n                                        placeholder=\"Nhập lời giải\"\r\n                                        label=\"Lời giải\"\r\n                                        Icon={CheckCircle}\r\n                                    /> */}\r\n                            <TextArea\r\n                                value={question.solutionUrl || ''}\r\n                                onChange={(e) => handleQuestionChange(e, 'solutionUrl')}\r\n                                placeholder=\"Nhập link lời giải youtube\"\r\n                                label=\"Link lời giải\"\r\n                                Icon={Video}\r\n                            />\r\n                            {question.solutionUrl && (\r\n                                <YouTubePlayer url={question.solutionUrl} />\r\n                            )}\r\n                            <SolutionEditor\r\n                                solution={question.solution}\r\n                                onSolutionChange={handleSolutionQuestionChange}\r\n                                preview={false}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst AddQuestionView = () => {\r\n    const dispatch = useDispatch();\r\n    const { newQuestion } = useSelector((state) => state.questionsExam);\r\n    const { codes } = useSelector((state) => state.codes);\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n    const handleNewQuestionChange = (e, field) => {\r\n        const updatedQuestion = { ...newQuestion, [field]: e.target.value };\r\n        dispatch(setNewQuestion(updatedQuestion));\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (newQuestion?.class && newQuestion?.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.startsWith(newQuestion?.class) && code.code.length === 5)\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"].filter((code) => code.code.length === 5));\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, newQuestion?.class]);\r\n\r\n    const handleNewStatementChange = (index, value, field) => {\r\n        const updatedStatements = [...newQuestion.statements];\r\n        updatedStatements[index] = { ...updatedStatements[index], [field]: value };\r\n        const updatedQuestion = { ...newQuestion, statements: updatedStatements };\r\n        dispatch(setNewQuestion(updatedQuestion));\r\n    };\r\n\r\n    const handleNewSolutionQuestionChange = (value) => {\r\n        const updatedQuestion = { ...newQuestion, solution: value };\r\n        dispatch(setNewQuestion(updatedQuestion));\r\n    };\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3 w-full\">\r\n            <div className=\"flex flex-col gap-2\">\r\n                <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Phân loại</h3>\r\n                <div className=\"flex flex-row gap-2\">\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={newQuestion.typeOfQuestion}\r\n                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'typeOfQuestion')}\r\n                        options={[{ code: \"TN\", description: \"Trắc nghiệm\" }, { code: \"DS\", description: \"Đúng sai\" }, { code: \"TLN\", description: \"Trả lời ngắn\" }]}\r\n                        className=\"text-xs\"\r\n                    />\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={newQuestion.class}\r\n                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'class')}\r\n                        options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                    <SuggestInputBarAdmin\r\n                        selectedOption={newQuestion.chapter}\r\n                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'chapter')}\r\n                        options={optionChapter}\r\n                        className=\"text-xs\"\r\n                    />\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={newQuestion.difficulty}\r\n                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'difficulty')}\r\n                        options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n                <hr className=\" bg-gray-200\"></hr>\r\n                <div className=\"flex flex-col gap-2\">\r\n                    <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thông tin câu hỏi</h3>\r\n                    <div className=\"space-y-2\">\r\n                        <TextArea\r\n                            value={newQuestion.content}\r\n                            onChange={(e) => handleNewQuestionChange(e, 'content')}\r\n                            placeholder=\"Nhập nội dung câu hỏi\"\r\n                            label=\"Câu hỏi\"\r\n                        />\r\n                        <ImageDropZone\r\n                            imageUrl={newQuestion.imageUrl}\r\n                            onImageDrop={(image) => handleNewQuestionChange({ target: { value: image } }, 'imageUrl')}\r\n                            onImageRemove={() => handleNewQuestionChange({ target: { value: '' } }, 'imageUrl')}\r\n                        />\r\n                        {newQuestion.typeOfQuestion !== 'TLN' && (\r\n                            <div className=\"space-y-2\">\r\n                                {newQuestion.statements.map((statement, index) => (\r\n                                    <div key={index} className=\"flex flex-col gap-2 items-center w-full\">\r\n                                        <div className=\"flex flex-row gap-2 items-center w-full\">\r\n                                            <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                                {newQuestion.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}\r\n                                            </p>\r\n                                            <TextArea\r\n                                                value={statement.content}\r\n                                                onChange={(e) => handleNewStatementChange(index, e.target.value, 'content')}\r\n                                                placeholder=\"Nhập nội dung mệnh đề\"\r\n                                            />\r\n                                            <div className=\"flex items-center gap-2\">\r\n                                                <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                                    <input\r\n                                                        type=\"radio\"\r\n                                                        name={`statement-${index}`}\r\n                                                        checked={statement.isCorrect}\r\n                                                        onChange={() => handleNewStatementChange(index, true, 'isCorrect')}\r\n                                                        className=\"w-4 h-4 text-green-600 focus:ring-green-500\"\r\n                                                    />\r\n                                                    <span className=\"text-sm text-green-600 font-medium\">Đúng</span>\r\n                                                </label>\r\n                                                <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                                    <input\r\n                                                        type=\"radio\"\r\n                                                        name={`statement-${index}`}\r\n                                                        checked={!statement.isCorrect}\r\n                                                        onChange={() => handleNewStatementChange(index, false, 'isCorrect')}\r\n                                                        className=\"w-4 h-4 text-red-600 focus:ring-red-500\"\r\n                                                    />\r\n                                                    <span className=\"text-sm text-red-600 font-medium\">Sai</span>\r\n                                                </label>\r\n                                            </div>\r\n                                        </div>\r\n                                        <ImageDropZone\r\n                                            imageUrl={statement.imageUrl}\r\n                                            onImageDrop={(image) => handleNewStatementChange(index, image, 'imageUrl')}\r\n                                            onImageRemove={() => handleNewStatementChange(index, '', 'imageUrl')}\r\n                                        />\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        )}\r\n                        {newQuestion.typeOfQuestion === 'TLN' && (\r\n                            <div className=\"space-y-2\">\r\n                                <TextArea\r\n                                    value={newQuestion.correctAnswer}\r\n                                    onChange={(e) => handleNewQuestionChange(e, 'correctAnswer')}\r\n                                    placeholder=\"Nhập đáp án\"\r\n                                    label=\"Đáp án\"\r\n                                    Icon={CheckCircle}\r\n                                />\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n                <div className=\"space-y-2\">\r\n                    <TextArea\r\n                        value={newQuestion.solutionUrl}\r\n                        onChange={(e) => handleNewQuestionChange(e, 'solutionUrl')}\r\n                        placeholder=\"Nhập link lời giải youtube\"\r\n                        label=\"Link lời giải\"\r\n                        Icon={Video}\r\n                    />\r\n                    {newQuestion.solutionUrl && (\r\n                        <YouTubePlayer url={newQuestion.solutionUrl} />\r\n                    )}\r\n                    <SolutionEditor\r\n                        solution={newQuestion.solution}\r\n                        onSolutionChange={handleNewSolutionQuestionChange}\r\n                    />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nconst LeftContent = () => {\r\n    const [view, setView] = useState('questionDetail');\r\n    const { newQuestion } = useSelector((state) => state.questionsExam);\r\n    const dispatch = useDispatch();\r\n\r\n    const handleAddQuestion = () => {\r\n        if (!newQuestion.content.trim()) {\r\n            dispatch(setErrorMessage(\"Nội dung câu hỏi không được để trống!\"));\r\n            return;\r\n        }\r\n        if (!newQuestion.typeOfQuestion) {\r\n            dispatch(setErrorMessage(\"Loại câu hỏi không được để trống!\"));\r\n            return;\r\n        }\r\n        if (!newQuestion.class) {\r\n            dispatch(setErrorMessage(\"Lớp không được để trống!\"));\r\n            return;\r\n        }\r\n        if (newQuestion.typeOfQuestion !== 'TLN' && newQuestion.statements.filter(statement => statement.content.trim() !== \"\").length < 4) {\r\n            dispatch(setErrorMessage(\"Câu hỏi TN phải có ít nhất 4 đáp án!\"));\r\n            return;\r\n        }\r\n        if (newQuestion.typeOfQuestion === 'TLN' && !newQuestion.correctAnswer.trim()) {\r\n            dispatch(setErrorMessage(\"Đáp án không được để trống!\"));\r\n            return;\r\n        }\r\n        dispatch(addQuestion());\r\n        setView('questionDetail');\r\n    };\r\n\r\n\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_138px)] overflow-y-auto\">\r\n            {/* Compact Preview Header */}\r\n            <div className=\"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <h2 className=\"text-xs font-semibold text-gray-900\">Chi tiết câu hỏi</h2>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                    {\r\n                        view === 'questionDetail' ? (\r\n                            <button\r\n                                onClick={() => setView('addQuestion')}\r\n                                className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-sky-600 hover:bg-sky-700 text-white`}\r\n                            >\r\n                                Thêm câu hỏi\r\n                            </button>\r\n                        ) : (\r\n                            <>\r\n                                <button\r\n                                    onClick={handleAddQuestion}\r\n                                    className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-emerald-600 hover:bg-emerald-700 text-white`}\r\n                                >\r\n                                    Lưu\r\n                                </button>\r\n                                <button\r\n                                    onClick={() => setView('questionDetail')}\r\n                                    className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-red-600 hover:bg-red-700 text-white`}\r\n                                >\r\n                                    Hủy\r\n                                </button>\r\n                            </>\r\n                        )\r\n                    }\r\n                </div>\r\n            </div>\r\n            {view === 'questionDetail' && <DetailQuestionView />}\r\n            {view === 'addQuestion' && <AddQuestionView />}\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nexport default LeftContent;"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,QAAQ,MAAM,+BAA+B;AACpD,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,OAAOC,cAAc,MAAM,2CAA2C;AACtE,SAASC,WAAW,QAAQ,+CAA+C;AAC3E,SAASC,WAAW,EAAEC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,cAAc;AACjE,SAASC,YAAY,EAAEC,cAAc,EAAEC,WAAW,QAAQ,+CAA+C;AACzG,SAASC,YAAY,QAAQ,+CAA+C;AAC5E,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,aAAa,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7C,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC,aAAa;IAAEC,UAAU;IAAEC;EAAK,CAAC,GAAG3B,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EACvF,MAAM;IAAEI;EAAM,CAAC,GAAG7B,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACC,KAAK,CAAC;EACrD,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM8B,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoC,QAAQ,EAAE5B,WAAW,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EAE9CD,SAAS,CAAC,MAAM;IACZ;;IAEA,IAAI4B,UAAU,IAAI,CAAAD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEW,MAAM,IAAG,CAAC,EAAE;MACzC,MAAMC,gBAAgB,GAAGZ,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEa,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKd,UAAU,CAAC;MACtEnB,WAAW,CAAC8B,gBAAgB,CAAC;IACjC;EACJ,CAAC,EAAE,CAACX,UAAU,EAAED,aAAa,CAAC,CAAC;EAE/B,MAAMgB,oBAAoB,GAAGA,CAACC,CAAC,EAAEC,KAAK,KAAK;IACvC,MAAMC,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAE,CAACQ,KAAK,GAAGD,CAAC,CAACG,MAAM,CAACC;IAAM,CAAC;IAChEhB,QAAQ,CAAClB,YAAY,CAACgC,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMG,qBAAqB,GAAGA,CAACC,KAAK,EAAEF,KAAK,EAAEH,KAAK,KAAK;IACnD,MAAMM,iBAAiB,GAAG,CAAC,GAAGd,QAAQ,CAACe,UAAU,CAAC;IAClDD,iBAAiB,CAACD,KAAK,CAAC,GAAG;MAAE,GAAGC,iBAAiB,CAACD,KAAK,CAAC;MAAE,CAACL,KAAK,GAAGG;IAAM,CAAC;IAC1E,MAAMF,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAEe,UAAU,EAAED;IAAkB,CAAC;IACtEnB,QAAQ,CAAClB,YAAY,CAACgC,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMO,4BAA4B,GAAIL,KAAK,IAAK;IAC5C,MAAMF,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAEiB,QAAQ,EAAEN;IAAM,CAAC;IACxDhB,QAAQ,CAAClB,YAAY,CAACgC,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMS,kBAAkB,GAAGA,CAAA,KAAM;IAC7BvB,QAAQ,CAACf,YAAY,CAAC,CAAC,CAAC;EAC5B,CAAC;EAEDjB,SAAS,CAAC,MAAM;IACZ,IAAIwD,KAAK,CAACC,OAAO,CAAC1B,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MACjC,IAAIM,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEqB,KAAK,IAAI,CAAArB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqB,KAAK,CAACC,IAAI,CAAC,CAAC,MAAK,EAAE,EAAE;QAClDvB,gBAAgB,CACZL,KAAK,CAAC,SAAS,CAAC,CAAC6B,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,UAAU,CAACzB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqB,KAAK,CAAC,IAAIG,IAAI,CAACA,IAAI,CAACvB,MAAM,KAAK,CAAC,CACrG,CAAC;MACL,CAAC,MAAM;QACHF,gBAAgB,CAACL,KAAK,CAAC,SAAS,CAAC,CAAC6B,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACvB,MAAM,KAAK,CAAC,CAAC,CAAC;MAC/E;IACJ,CAAC,MAAM;MACHF,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAACL,KAAK,EAAEM,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqB,KAAK,CAAC,CAAC;EAE5B,oBACIpC,OAAA;IAAKyC,SAAS,EAAC,sBAAsB;IAAAC,QAAA,EAChC3B,QAAQ,iBACLf,OAAA;MAAKyC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7B1C,OAAA;QAAKyC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChC1C,OAAA;UAAIyC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrE9C,OAAA;UAAKyC,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAChC1C,OAAA,CAAClB,gBAAgB;YACbiE,cAAc,EAAEhC,QAAQ,CAACqB,KAAM;YAC/BY,QAAQ,EAAGC,MAAM,IAAK5B,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEuB;cAAO;YAAE,CAAC,EAAE,OAAO,CAAE;YACnFC,OAAO,EAAEhB,KAAK,CAACC,OAAO,CAAC1B,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;YAC7DgC,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACF9C,OAAA,CAACjB,oBAAoB;YACjBgE,cAAc,EAAEhC,QAAQ,CAACoC,OAAQ;YACjCH,QAAQ,EAAGC,MAAM,IAAK5B,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEuB;cAAO;YAAE,CAAC,EAAE,SAAS,CAAE;YACrFC,OAAO,EAAErC,aAAc;YACvB4B,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACF9C,OAAA,CAAClB,gBAAgB;YACbiE,cAAc,EAAEhC,QAAQ,CAACqC,UAAW;YACpCJ,QAAQ,EAAGC,MAAM,IAAK5B,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEuB;cAAO;YAAE,CAAC,EAAE,YAAY,CAAE;YACxFC,OAAO,EAAEhB,KAAK,CAACC,OAAO,CAAC1B,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;YACvEgC,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN9C,OAAA;QAAIyC,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClC9C,OAAA;QAAKyC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChC1C,OAAA;UAAIyC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E9C,OAAA;UAAKyC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtB1C,OAAA,CAAChB,QAAQ;YACL0C,KAAK,EAAEX,QAAQ,CAACsC,OAAQ;YACxBL,QAAQ,EAAG1B,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,SAAS,CAAE;YACpDgC,WAAW,EAAC,yCAAuB;YACnCC,KAAK,EAAC;UAAS;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,EACD,CAACvC,IAAI,KAAK,OAAO,IAAIQ,QAAQ,CAACyC,QAAQ,kBACnCxD,OAAA,CAACf,aAAa;YACVuE,QAAQ,EAAEzC,QAAQ,CAACyC,QAAS;YAC5BC,WAAW,EAAGC,KAAK,IAAKrC,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEgC;cAAM;YAAE,CAAC,EAAE,UAAU,CAAE;YACvFC,aAAa,EAAEA,CAAA,KAAMtC,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAE;cAAG;YAAE,CAAC,EAAE,UAAU;UAAE;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CACJ,EACA/B,QAAQ,CAAC6C,cAAc,KAAK,KAAK,iBAC9B5D,OAAA;YAAKyC,SAAS,EAAC,WAAW;YAAAC,QAAA,GACrB3B,QAAQ,CAACe,UAAU,CAACd,MAAM,GAAG,CAAC,iBAC3BhB,OAAA;cACI6D,OAAO,EAAEA,CAAA,KAAM5B,kBAAkB,CAAC,CAAE;cACpCQ,SAAS,EAAC,+FAA+F;cAAAC,QAAA,gBAEzG1C,OAAA,CAACX,IAAI;gBAACoD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mCAChC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACX,EACA/B,QAAQ,CAACe,UAAU,CAACgC,GAAG,CAAC,CAACC,SAAS,EAAEnC,KAAK,kBACtC5B,OAAA;cAAiByC,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBAChE1C,OAAA;gBAAKyC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBACpD1C,OAAA;kBAAGyC,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAC7C3B,QAAQ,CAAC6C,cAAc,KAAK,IAAI,GAAGjD,QAAQ,CAACiB,KAAK,CAAC,GAAGhB,QAAQ,CAACgB,KAAK;gBAAC;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACJ9C,OAAA,CAAChB,QAAQ;kBACL0C,KAAK,EAAEqC,SAAS,CAACV,OAAQ;kBACzBL,QAAQ,EAAG1B,CAAC,IAAKK,qBAAqB,CAACC,KAAK,EAAEN,CAAC,CAACG,MAAM,CAACC,KAAK,EAAE,SAAS,CAAE;kBACzE4B,WAAW,EAAC;gBAAuB;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,EACD/B,QAAQ,CAAC6C,cAAc,KAAK,KAAK,iBAC9B5D,OAAA;kBAAKyC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACpC1C,OAAA;oBAAOyC,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD1C,OAAA;sBACIgE,IAAI,EAAC,OAAO;sBACZC,IAAI,eAAAC,MAAA,CAAetC,KAAK,CAAG;sBAC3BuC,OAAO,EAAEJ,SAAS,CAACK,SAAU;sBAC7BpB,QAAQ,EAAEA,CAAA,KAAMrB,qBAAqB,CAACC,KAAK,EAAE,IAAI,EAAE,WAAW,CAAE;sBAChEa,SAAS,EAAC;oBAA6C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACF9C,OAAA;sBAAMyC,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACR9C,OAAA;oBAAOyC,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD1C,OAAA;sBACIgE,IAAI,EAAC,OAAO;sBACZC,IAAI,eAAAC,MAAA,CAAetC,KAAK,CAAG;sBAC3BuC,OAAO,EAAE,CAACJ,SAAS,CAACK,SAAU;sBAC9BpB,QAAQ,EAAEA,CAAA,KAAMrB,qBAAqB,CAACC,KAAK,EAAE,KAAK,EAAE,WAAW,CAAE;sBACjEa,SAAS,EAAC;oBAAyC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACF9C,OAAA;sBAAMyC,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,EACL,CAACvC,IAAI,KAAK,OAAO,IAAIwD,SAAS,CAACP,QAAQ,kBACpCxD,OAAA,CAACf,aAAa;gBACVuE,QAAQ,EAAEO,SAAS,CAACP,QAAS;gBAC7BC,WAAW,EAAGC,KAAK,IAAK/B,qBAAqB,CAACC,KAAK,EAAE8B,KAAK,EAAE,UAAU,CAAE;gBACxEC,aAAa,EAAEA,CAAA,KAAMhC,qBAAqB,CAACC,KAAK,EAAE,EAAE,EAAE,UAAU;cAAE;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CACJ;YAAA,GAzCKlB,KAAK;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2CV,CACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EACA/B,QAAQ,CAAC6C,cAAc,KAAK,KAAK,iBAC9B5D,OAAA;YAAKyC,SAAS,EAAC,WAAW;YAAAC,QAAA,eACtB1C,OAAA,CAAChB,QAAQ;cACL0C,KAAK,EAAEX,QAAQ,CAACsD,aAAc;cAC9BrB,QAAQ,EAAG1B,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,eAAe,CAAE;cAC1DgC,WAAW,EAAC,6BAAa;cACzBC,KAAK,EAAC,mBAAQ;cACde,IAAI,EAAElF;YAAY;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,eAQD9C,OAAA,CAAChB,QAAQ;YACL0C,KAAK,EAAEX,QAAQ,CAACwD,WAAW,IAAI,EAAG;YAClCvB,QAAQ,EAAG1B,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,aAAa,CAAE;YACxDgC,WAAW,EAAC,2CAA4B;YACxCC,KAAK,EAAC,yBAAe;YACrBe,IAAI,EAAEhF;UAAM;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EACD/B,QAAQ,CAACwD,WAAW,iBACjBvE,OAAA,CAACF,aAAa;YAAC0E,GAAG,EAAEzD,QAAQ,CAACwD;UAAY;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC9C,eACD9C,OAAA,CAACd,cAAc;YACX8C,QAAQ,EAAEjB,QAAQ,CAACiB,QAAS;YAC5ByC,gBAAgB,EAAE1C,4BAA6B;YAC/C2C,OAAO,EAAE;UAAM;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAA1C,EAAA,CAnMKD,kBAAkB;EAAA,QACwBvB,WAAW,EACrCA,WAAW,EACZC,WAAW;AAAA;AAAA8F,EAAA,GAH1BxE,kBAAkB;AAqMxB,MAAMyE,eAAe,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC1B,MAAMnE,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiG;EAAY,CAAC,GAAGlG,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EACnE,MAAM;IAAEI;EAAM,CAAC,GAAG7B,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACC,KAAK,CAAC;EACrD,MAAME,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAMoG,uBAAuB,GAAGA,CAACzD,CAAC,EAAEC,KAAK,KAAK;IAC1C,MAAMC,eAAe,GAAG;MAAE,GAAGsD,WAAW;MAAE,CAACvD,KAAK,GAAGD,CAAC,CAACG,MAAM,CAACC;IAAM,CAAC;IACnEhB,QAAQ,CAACjB,cAAc,CAAC+B,eAAe,CAAC,CAAC;EAC7C,CAAC;EAED9C,SAAS,CAAC,MAAM;IACZ,IAAIwD,KAAK,CAACC,OAAO,CAAC1B,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MACjC,IAAIqE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAE1C,KAAK,IAAI,CAAA0C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE1C,KAAK,CAACC,IAAI,CAAC,CAAC,MAAK,EAAE,EAAE;QACxDvB,gBAAgB,CACZL,KAAK,CAAC,SAAS,CAAC,CAAC6B,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,UAAU,CAACsC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE1C,KAAK,CAAC,IAAIG,IAAI,CAACA,IAAI,CAACvB,MAAM,KAAK,CAAC,CACxG,CAAC;MACL,CAAC,MAAM;QACHF,gBAAgB,CAACL,KAAK,CAAC,SAAS,CAAC,CAAC6B,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACvB,MAAM,KAAK,CAAC,CAAC,CAAC;MAC/E;IACJ,CAAC,MAAM;MACHF,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAACL,KAAK,EAAEqE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE1C,KAAK,CAAC,CAAC;EAE/B,MAAM4C,wBAAwB,GAAGA,CAACpD,KAAK,EAAEF,KAAK,EAAEH,KAAK,KAAK;IACtD,MAAMM,iBAAiB,GAAG,CAAC,GAAGiD,WAAW,CAAChD,UAAU,CAAC;IACrDD,iBAAiB,CAACD,KAAK,CAAC,GAAG;MAAE,GAAGC,iBAAiB,CAACD,KAAK,CAAC;MAAE,CAACL,KAAK,GAAGG;IAAM,CAAC;IAC1E,MAAMF,eAAe,GAAG;MAAE,GAAGsD,WAAW;MAAEhD,UAAU,EAAED;IAAkB,CAAC;IACzEnB,QAAQ,CAACjB,cAAc,CAAC+B,eAAe,CAAC,CAAC;EAC7C,CAAC;EAED,MAAMyD,+BAA+B,GAAIvD,KAAK,IAAK;IAC/C,MAAMF,eAAe,GAAG;MAAE,GAAGsD,WAAW;MAAE9C,QAAQ,EAAEN;IAAM,CAAC;IAC3DhB,QAAQ,CAACjB,cAAc,CAAC+B,eAAe,CAAC,CAAC;EAC7C,CAAC;EAED,oBACIxB,OAAA;IAAKyC,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eACjC1C,OAAA;MAAKyC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAChC1C,OAAA;QAAIyC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrE9C,OAAA;QAAKyC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChC1C,OAAA,CAAClB,gBAAgB;UACbiE,cAAc,EAAE+B,WAAW,CAAClB,cAAe;UAC3CZ,QAAQ,EAAGC,MAAM,IAAK8B,uBAAuB,CAAC;YAAEtD,MAAM,EAAE;cAAEC,KAAK,EAAEuB;YAAO;UAAE,CAAC,EAAE,gBAAgB,CAAE;UAC/FC,OAAO,EAAE,CAAC;YAAEX,IAAI,EAAE,IAAI;YAAE2C,WAAW,EAAE;UAAc,CAAC,EAAE;YAAE3C,IAAI,EAAE,IAAI;YAAE2C,WAAW,EAAE;UAAW,CAAC,EAAE;YAAE3C,IAAI,EAAE,KAAK;YAAE2C,WAAW,EAAE;UAAe,CAAC,CAAE;UAC7IzC,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACF9C,OAAA,CAAClB,gBAAgB;UACbiE,cAAc,EAAE+B,WAAW,CAAC1C,KAAM;UAClCY,QAAQ,EAAGC,MAAM,IAAK8B,uBAAuB,CAAC;YAAEtD,MAAM,EAAE;cAAEC,KAAK,EAAEuB;YAAO;UAAE,CAAC,EAAE,OAAO,CAAE;UACtFC,OAAO,EAAEhB,KAAK,CAACC,OAAO,CAAC1B,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;UAC7DgC,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACF9C,OAAA,CAACjB,oBAAoB;UACjBgE,cAAc,EAAE+B,WAAW,CAAC3B,OAAQ;UACpCH,QAAQ,EAAGC,MAAM,IAAK8B,uBAAuB,CAAC;YAAEtD,MAAM,EAAE;cAAEC,KAAK,EAAEuB;YAAO;UAAE,CAAC,EAAE,SAAS,CAAE;UACxFC,OAAO,EAAErC,aAAc;UACvB4B,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACF9C,OAAA,CAAClB,gBAAgB;UACbiE,cAAc,EAAE+B,WAAW,CAAC1B,UAAW;UACvCJ,QAAQ,EAAGC,MAAM,IAAK8B,uBAAuB,CAAC;YAAEtD,MAAM,EAAE;cAAEC,KAAK,EAAEuB;YAAO;UAAE,CAAC,EAAE,YAAY,CAAE;UAC3FC,OAAO,EAAEhB,KAAK,CAACC,OAAO,CAAC1B,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;UACvEgC,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN9C,OAAA;QAAIyC,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClC9C,OAAA;QAAKyC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChC1C,OAAA;UAAIyC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E9C,OAAA;UAAKyC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtB1C,OAAA,CAAChB,QAAQ;YACL0C,KAAK,EAAEoD,WAAW,CAACzB,OAAQ;YAC3BL,QAAQ,EAAG1B,CAAC,IAAKyD,uBAAuB,CAACzD,CAAC,EAAE,SAAS,CAAE;YACvDgC,WAAW,EAAC,yCAAuB;YACnCC,KAAK,EAAC;UAAS;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACF9C,OAAA,CAACf,aAAa;YACVuE,QAAQ,EAAEsB,WAAW,CAACtB,QAAS;YAC/BC,WAAW,EAAGC,KAAK,IAAKqB,uBAAuB,CAAC;cAAEtD,MAAM,EAAE;gBAAEC,KAAK,EAAEgC;cAAM;YAAE,CAAC,EAAE,UAAU,CAAE;YAC1FC,aAAa,EAAEA,CAAA,KAAMoB,uBAAuB,CAAC;cAAEtD,MAAM,EAAE;gBAAEC,KAAK,EAAE;cAAG;YAAE,CAAC,EAAE,UAAU;UAAE;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,EACDgC,WAAW,CAAClB,cAAc,KAAK,KAAK,iBACjC5D,OAAA;YAAKyC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACrBoC,WAAW,CAAChD,UAAU,CAACgC,GAAG,CAAC,CAACC,SAAS,EAAEnC,KAAK,kBACzC5B,OAAA;cAAiByC,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBAChE1C,OAAA;gBAAKyC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBACpD1C,OAAA;kBAAGyC,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAC7CoC,WAAW,CAAClB,cAAc,KAAK,IAAI,GAAGjD,QAAQ,CAACiB,KAAK,CAAC,GAAGhB,QAAQ,CAACgB,KAAK;gBAAC;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC,eACJ9C,OAAA,CAAChB,QAAQ;kBACL0C,KAAK,EAAEqC,SAAS,CAACV,OAAQ;kBACzBL,QAAQ,EAAG1B,CAAC,IAAK0D,wBAAwB,CAACpD,KAAK,EAAEN,CAAC,CAACG,MAAM,CAACC,KAAK,EAAE,SAAS,CAAE;kBAC5E4B,WAAW,EAAC;gBAAuB;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACF9C,OAAA;kBAAKyC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACpC1C,OAAA;oBAAOyC,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD1C,OAAA;sBACIgE,IAAI,EAAC,OAAO;sBACZC,IAAI,eAAAC,MAAA,CAAetC,KAAK,CAAG;sBAC3BuC,OAAO,EAAEJ,SAAS,CAACK,SAAU;sBAC7BpB,QAAQ,EAAEA,CAAA,KAAMgC,wBAAwB,CAACpD,KAAK,EAAE,IAAI,EAAE,WAAW,CAAE;sBACnEa,SAAS,EAAC;oBAA6C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACF9C,OAAA;sBAAMyC,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACR9C,OAAA;oBAAOyC,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD1C,OAAA;sBACIgE,IAAI,EAAC,OAAO;sBACZC,IAAI,eAAAC,MAAA,CAAetC,KAAK,CAAG;sBAC3BuC,OAAO,EAAE,CAACJ,SAAS,CAACK,SAAU;sBAC9BpB,QAAQ,EAAEA,CAAA,KAAMgC,wBAAwB,CAACpD,KAAK,EAAE,KAAK,EAAE,WAAW,CAAE;sBACpEa,SAAS,EAAC;oBAAyC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACF9C,OAAA;sBAAMyC,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN9C,OAAA,CAACf,aAAa;gBACVuE,QAAQ,EAAEO,SAAS,CAACP,QAAS;gBAC7BC,WAAW,EAAGC,KAAK,IAAKsB,wBAAwB,CAACpD,KAAK,EAAE8B,KAAK,EAAE,UAAU,CAAE;gBAC3EC,aAAa,EAAEA,CAAA,KAAMqB,wBAAwB,CAACpD,KAAK,EAAE,EAAE,EAAE,UAAU;cAAE;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC;YAAA,GArCIlB,KAAK;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsCV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EACAgC,WAAW,CAAClB,cAAc,KAAK,KAAK,iBACjC5D,OAAA;YAAKyC,SAAS,EAAC,WAAW;YAAAC,QAAA,eACtB1C,OAAA,CAAChB,QAAQ;cACL0C,KAAK,EAAEoD,WAAW,CAACT,aAAc;cACjCrB,QAAQ,EAAG1B,CAAC,IAAKyD,uBAAuB,CAACzD,CAAC,EAAE,eAAe,CAAE;cAC7DgC,WAAW,EAAC,6BAAa;cACzBC,KAAK,EAAC,mBAAQ;cACde,IAAI,EAAElF;YAAY;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN9C,OAAA;QAAKyC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtB1C,OAAA,CAAChB,QAAQ;UACL0C,KAAK,EAAEoD,WAAW,CAACP,WAAY;UAC/BvB,QAAQ,EAAG1B,CAAC,IAAKyD,uBAAuB,CAACzD,CAAC,EAAE,aAAa,CAAE;UAC3DgC,WAAW,EAAC,2CAA4B;UACxCC,KAAK,EAAC,yBAAe;UACrBe,IAAI,EAAEhF;QAAM;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,EACDgC,WAAW,CAACP,WAAW,iBACpBvE,OAAA,CAACF,aAAa;UAAC0E,GAAG,EAAEM,WAAW,CAACP;QAAY;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACjD,eACD9C,OAAA,CAACd,cAAc;UACX8C,QAAQ,EAAE8C,WAAW,CAAC9C,QAAS;UAC/ByC,gBAAgB,EAAEQ;QAAgC;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAA+B,GAAA,CAhKKD,eAAe;EAAA,QACA/F,WAAW,EACJD,WAAW,EACjBA,WAAW;AAAA;AAAAuG,GAAA,GAH3BP,eAAe;AAmKrB,MAAMQ,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtB,MAAM,CAAC9E,IAAI,EAAE+E,OAAO,CAAC,GAAG3G,QAAQ,CAAC,gBAAgB,CAAC;EAClD,MAAM;IAAEmG;EAAY,CAAC,GAAGlG,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EACnE,MAAMK,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAE9B,MAAM0G,iBAAiB,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACT,WAAW,CAACzB,OAAO,CAAChB,IAAI,CAAC,CAAC,EAAE;MAC7B3B,QAAQ,CAACb,eAAe,CAAC,uCAAuC,CAAC,CAAC;MAClE;IACJ;IACA,IAAI,CAACiF,WAAW,CAAClB,cAAc,EAAE;MAC7BlD,QAAQ,CAACb,eAAe,CAAC,mCAAmC,CAAC,CAAC;MAC9D;IACJ;IACA,IAAI,CAACiF,WAAW,CAAC1C,KAAK,EAAE;MACpB1B,QAAQ,CAACb,eAAe,CAAC,0BAA0B,CAAC,CAAC;MACrD;IACJ;IACA,IAAIiF,WAAW,CAAClB,cAAc,KAAK,KAAK,IAAIkB,WAAW,CAAChD,UAAU,CAACQ,MAAM,CAACyB,SAAS,IAAIA,SAAS,CAACV,OAAO,CAAChB,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAACrB,MAAM,GAAG,CAAC,EAAE;MAChIN,QAAQ,CAACb,eAAe,CAAC,sCAAsC,CAAC,CAAC;MACjE;IACJ;IACA,IAAIiF,WAAW,CAAClB,cAAc,KAAK,KAAK,IAAI,CAACkB,WAAW,CAACT,aAAa,CAAChC,IAAI,CAAC,CAAC,EAAE;MAC3E3B,QAAQ,CAACb,eAAe,CAAC,6BAA6B,CAAC,CAAC;MACxD;IACJ;IACAa,QAAQ,CAAChB,WAAW,CAAC,CAAC,CAAC;IACvB4F,OAAO,CAAC,gBAAgB,CAAC;EAC7B,CAAC;EAID,oBACItF,OAAA;IAAKyC,SAAS,EAAC,uDAAuD;IAAAC,QAAA,gBAElE1C,OAAA;MAAKyC,SAAS,EAAC,oFAAoF;MAAAC,QAAA,gBAC/F1C,OAAA;QAAKyC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpC1C,OAAA;UAAIyC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACN9C,OAAA;QAAKyC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAEhCnC,IAAI,KAAK,gBAAgB,gBACrBP,OAAA;UACI6D,OAAO,EAAEA,CAAA,KAAMyB,OAAO,CAAC,aAAa,CAAE;UACtC7C,SAAS,+FAAgG;UAAAC,QAAA,EAC5G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAET9C,OAAA,CAAAE,SAAA;UAAAwC,QAAA,gBACI1C,OAAA;YACI6D,OAAO,EAAE0B,iBAAkB;YAC3B9C,SAAS,uGAAwG;YAAAC,QAAA,EACpH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9C,OAAA;YACI6D,OAAO,EAAEA,CAAA,KAAMyB,OAAO,CAAC,gBAAgB,CAAE;YACzC7C,SAAS,+FAAgG;YAAAC,QAAA,EAC5G;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACX;MACL;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACLvC,IAAI,KAAK,gBAAgB,iBAAIP,OAAA,CAACG,kBAAkB;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACnDvC,IAAI,KAAK,aAAa,iBAAIP,OAAA,CAAC4E,eAAe;MAAAjC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7C,CAAC;AAEd,CAAC;AAAAuC,GAAA,CAvEKD,WAAW;EAAA,QAEWxG,WAAW,EAClBC,WAAW;AAAA;AAAA2G,GAAA,GAH1BJ,WAAW;AA0EjB,eAAeA,WAAW;AAAC,IAAAT,EAAA,EAAAQ,GAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAd,EAAA;AAAAc,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}