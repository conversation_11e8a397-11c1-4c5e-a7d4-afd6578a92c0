import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest = {
    /**
     * The ID of the model to unarchive.
     */
    modelId: string;
};
/** @internal */
export declare const JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$inboundSchema: z.ZodType<JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest, z.ZodTypeDef, unknown>;
/** @internal */
export type JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$Outbound = {
    model_id: string;
};
/** @internal */
export declare const JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$outboundSchema: z.ZodType<JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$Outbound, z.ZodTypeDef, JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$ {
    /** @deprecated use `JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$inboundSchema` instead. */
    const inboundSchema: z.ZodType<JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest, z.ZodTypeDef, unknown>;
    /** @deprecated use `JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$outboundSchema` instead. */
    const outboundSchema: z.ZodType<JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$Outbound, z.ZodTypeDef, JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest>;
    /** @deprecated use `JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$Outbound` instead. */
    type Outbound = JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$Outbound;
}
export declare function jobsApiRoutesFineTuningUnarchiveFineTunedModelRequestToJSON(jobsApiRoutesFineTuningUnarchiveFineTunedModelRequest: JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest): string;
export declare function jobsApiRoutesFineTuningUnarchiveFineTunedModelRequestFromJSON(jsonString: string): SafeParseResult<JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest, SDKValidationError>;
//# sourceMappingURL=jobsapiroutesfinetuningunarchivefinetunedmodel.d.ts.map