{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nimport * as aiApi from \"../../services/aiApi\";\nexport const askQuestionWithAI = createAsyncThunk(\"ai/askQuestionWithAI\", async (_ref, _ref2) => {\n  let {\n    questionId,\n    messageId\n  } = _ref;\n  let {\n    dispatch\n  } = _ref2;\n  return await apiHandler(dispatch, aiApi.askQuestionWithAI, {\n    questionId,\n    messageId\n  }, () => {}, false, false);\n});\nexport const classifyQuestions = createAsyncThunk(\"ai/classifyQuestions\", async (questions, _ref3) => {\n  let {\n    dispatch\n  } = _ref3;\n  return await apiHandler(dispatch, aiApi.classifyQuestions, questions, () => {}, false, false);\n});\nexport const fixTextAndLatex = createAsyncThunk(\"ai/fixTextAndLatex\", async (text, _ref4) => {\n  let {\n    dispatch\n  } = _ref4;\n  return await apiHandler(dispatch, aiApi.fixTextAndLatex, text, () => {}, false, false);\n});\nconst aiSlice = createSlice({\n  name: \"ai\",\n  initialState: {\n    aiResponse: null,\n    loading: false,\n    fixTextResult: null,\n    userQuestion: {\n      1: 'Hãy giải bài toán này chi tiết từng bước.',\n      2: 'Giải thích đáp án trong câu hỏi này.',\n      3: 'Đáp án câu hỏi này đang bị sai, hãy sửa lại.',\n      4: 'Giải thích lời giải trong câu hỏi này.',\n      5: 'Lời giải câu hỏi này đang bị sai, hãy sửa lại.'\n    }\n  },\n  reducers: {\n    setAiResponse: (state, action) => {\n      state.aiResponse = action.payload;\n    },\n    resetAiResponse: state => {\n      state.aiResponse = null;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(askQuestionWithAI.pending, state => {\n      state.loading = true;\n    }).addCase(askQuestionWithAI.fulfilled, (state, action) => {\n      state.aiResponse = action.payload.data.aiResponse;\n      state.loading = false;\n    }).addCase(askQuestionWithAI.rejected, state => {\n      state.loading = false;\n    }).addCase(classifyQuestions.pending, state => {\n      state.loading = true;\n    }).addCase(classifyQuestions.fulfilled, (state, action) => {\n      state.classifyResult = action.payload.data;\n      state.loading = false;\n    }).addCase(classifyQuestions.rejected, state => {\n      state.loading = false;\n    }).addCase(fixTextAndLatex.pending, state => {\n      state.loading = true;\n    }).addCase(fixTextAndLatex.fulfilled, (state, action) => {\n      state.fixTextResult = action.payload.data;\n      state.loading = false;\n    }).addCase(fixTextAndLatex.rejected, state => {\n      state.loading = false;\n    });\n  }\n});\nexport const {\n  setAiResponse,\n  resetAiResponse\n} = aiSlice.actions;\nexport default aiSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aiApi", "askQuestionWithAI", "_ref", "_ref2", "questionId", "messageId", "dispatch", "classifyQuestions", "questions", "_ref3", "fixTextAndLatex", "text", "_ref4", "aiSlice", "name", "initialState", "aiResponse", "loading", "fixTextResult", "userQuestion", "reducers", "setAiResponse", "state", "action", "payload", "resetAiResponse", "extraReducers", "builder", "addCase", "pending", "fulfilled", "data", "rejected", "classifyResult", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/ai/aiSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\nimport * as aiApi from \"../../services/aiApi\";\r\n\r\nexport const askQuestionWithAI = createAsyncThunk(\r\n    \"ai/askQuestionWithAI\",\r\n    async ({ questionId, messageId }, { dispatch }) => {\r\n        return await apiHandler(dispatch, aiApi.askQuestionWithAI, { questionId, messageId }, () => { }, false, false);\r\n    }\r\n);\r\n\r\nexport const classifyQuestions = createAsyncThunk(\r\n    \"ai/classifyQuestions\",\r\n    async (questions, { dispatch }) => {\r\n        return await apiHandler(dispatch, aiApi.classifyQuestions, questions, () => { }, false, false);\r\n    }\r\n);\r\n\r\nexport const fixTextAndLatex = createAsyncThunk(\r\n    \"ai/fixTextAndLatex\",\r\n    async (text, { dispatch }) => {\r\n        return await apiHandler(dispatch, aiApi.fixTextAndLatex, text, () => { }, false, false);\r\n    }\r\n);\r\n\r\n\r\nconst aiSlice = createSlice({\r\n    name: \"ai\",\r\n    initialState: {\r\n        aiResponse: null,\r\n        loading: false,\r\n        fixTextResult: null,\r\n        userQuestion: {\r\n            1: 'Hãy giải bài toán này chi tiết từng bước.',\r\n            2: 'Giải thích đáp án trong câu hỏi này.',\r\n            3: 'Đáp án câu hỏi này đang bị sai, hãy sửa lại.',\r\n            4: 'Giải thích lời giải trong câu hỏi này.',\r\n            5: 'Lời giải câu hỏi này đang bị sai, hãy sửa lại.'\r\n        }\r\n    },\r\n    reducers: {\r\n        setAiResponse: (state, action) => {\r\n            state.aiResponse = action.payload;\r\n        },\r\n        resetAiResponse: (state) => {\r\n            state.aiResponse = null;\r\n        },\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(askQuestionWithAI.pending, (state) => {\r\n                state.loading = true;\r\n            })\r\n            .addCase(askQuestionWithAI.fulfilled, (state, action) => {\r\n                state.aiResponse = action.payload.data.aiResponse;\r\n                state.loading = false;\r\n            })\r\n            .addCase(askQuestionWithAI.rejected, (state) => {\r\n                state.loading = false;\r\n            })\r\n            .addCase(classifyQuestions.pending, (state) => {\r\n                state.loading = true;\r\n            })\r\n            .addCase(classifyQuestions.fulfilled, (state, action) => {\r\n                state.classifyResult = action.payload.data;\r\n                state.loading = false;\r\n            })\r\n            .addCase(classifyQuestions.rejected, (state) => {\r\n                state.loading = false;\r\n            })\r\n            .addCase(fixTextAndLatex.pending, (state) => {\r\n                state.loading = true;\r\n            })\r\n            .addCase(fixTextAndLatex.fulfilled, (state, action) => {\r\n                state.fixTextResult = action.payload.data;\r\n                state.loading = false;\r\n            })\r\n            .addCase(fixTextAndLatex.rejected, (state) => {\r\n                state.loading = false;\r\n            })\r\n    },\r\n});\r\n\r\nexport const { setAiResponse, resetAiResponse } = aiSlice.actions;\r\nexport default aiSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAO,KAAKC,KAAK,MAAM,sBAAsB;AAE7C,OAAO,MAAMC,iBAAiB,GAAGH,gBAAgB,CAC7C,sBAAsB,EACtB,OAAAI,IAAA,EAAAC,KAAA,KAAmD;EAAA,IAA5C;IAAEC,UAAU;IAAEC;EAAU,CAAC,GAAAH,IAAA;EAAA,IAAE;IAAEI;EAAS,CAAC,GAAAH,KAAA;EAC1C,OAAO,MAAMJ,UAAU,CAACO,QAAQ,EAAEN,KAAK,CAACC,iBAAiB,EAAE;IAAEG,UAAU;IAAEC;EAAU,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAClH,CACJ,CAAC;AAED,OAAO,MAAME,iBAAiB,GAAGT,gBAAgB,CAC7C,sBAAsB,EACtB,OAAOU,SAAS,EAAAC,KAAA,KAAmB;EAAA,IAAjB;IAAEH;EAAS,CAAC,GAAAG,KAAA;EAC1B,OAAO,MAAMV,UAAU,CAACO,QAAQ,EAAEN,KAAK,CAACO,iBAAiB,EAAEC,SAAS,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAClG,CACJ,CAAC;AAED,OAAO,MAAME,eAAe,GAAGZ,gBAAgB,CAC3C,oBAAoB,EACpB,OAAOa,IAAI,EAAAC,KAAA,KAAmB;EAAA,IAAjB;IAAEN;EAAS,CAAC,GAAAM,KAAA;EACrB,OAAO,MAAMb,UAAU,CAACO,QAAQ,EAAEN,KAAK,CAACU,eAAe,EAAEC,IAAI,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC3F,CACJ,CAAC;AAGD,MAAME,OAAO,GAAGhB,WAAW,CAAC;EACxBiB,IAAI,EAAE,IAAI;EACVC,YAAY,EAAE;IACVC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE,KAAK;IACdC,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE;MACV,CAAC,EAAE,2CAA2C;MAC9C,CAAC,EAAE,sCAAsC;MACzC,CAAC,EAAE,8CAA8C;MACjD,CAAC,EAAE,wCAAwC;MAC3C,CAAC,EAAE;IACP;EACJ,CAAC;EACDC,QAAQ,EAAE;IACNC,aAAa,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC9BD,KAAK,CAACN,UAAU,GAAGO,MAAM,CAACC,OAAO;IACrC,CAAC;IACDC,eAAe,EAAGH,KAAK,IAAK;MACxBA,KAAK,CAACN,UAAU,GAAG,IAAI;IAC3B;EACJ,CAAC;EACDU,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAAC3B,iBAAiB,CAAC4B,OAAO,EAAGP,KAAK,IAAK;MAC3CA,KAAK,CAACL,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACDW,OAAO,CAAC3B,iBAAiB,CAAC6B,SAAS,EAAE,CAACR,KAAK,EAAEC,MAAM,KAAK;MACrDD,KAAK,CAACN,UAAU,GAAGO,MAAM,CAACC,OAAO,CAACO,IAAI,CAACf,UAAU;MACjDM,KAAK,CAACL,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDW,OAAO,CAAC3B,iBAAiB,CAAC+B,QAAQ,EAAGV,KAAK,IAAK;MAC5CA,KAAK,CAACL,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDW,OAAO,CAACrB,iBAAiB,CAACsB,OAAO,EAAGP,KAAK,IAAK;MAC3CA,KAAK,CAACL,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACDW,OAAO,CAACrB,iBAAiB,CAACuB,SAAS,EAAE,CAACR,KAAK,EAAEC,MAAM,KAAK;MACrDD,KAAK,CAACW,cAAc,GAAGV,MAAM,CAACC,OAAO,CAACO,IAAI;MAC1CT,KAAK,CAACL,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDW,OAAO,CAACrB,iBAAiB,CAACyB,QAAQ,EAAGV,KAAK,IAAK;MAC5CA,KAAK,CAACL,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDW,OAAO,CAAClB,eAAe,CAACmB,OAAO,EAAGP,KAAK,IAAK;MACzCA,KAAK,CAACL,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACDW,OAAO,CAAClB,eAAe,CAACoB,SAAS,EAAE,CAACR,KAAK,EAAEC,MAAM,KAAK;MACnDD,KAAK,CAACJ,aAAa,GAAGK,MAAM,CAACC,OAAO,CAACO,IAAI;MACzCT,KAAK,CAACL,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDW,OAAO,CAAClB,eAAe,CAACsB,QAAQ,EAAGV,KAAK,IAAK;MAC1CA,KAAK,CAACL,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEI,aAAa;EAAEI;AAAgB,CAAC,GAAGZ,OAAO,CAACqB,OAAO;AACjE,eAAerB,OAAO,CAACsB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}