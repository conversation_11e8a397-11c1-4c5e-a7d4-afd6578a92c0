import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { apiHandler } from "../../utils/apiHandler";
import * as aiApi from "../../services/aiApi";

export const askQuestionWithAI = createAsyncThunk(
    "ai/askQuestionWithAI",
    async ({ questionId, messageId }, { dispatch }) => {
        return await apiHandler(dispatch, aiApi.askQuestionWithAI, { questionId, messageId }, () => { }, false, false);
    }
);

export const classifyQuestions = createAsyncThunk(
    "ai/classifyQuestions",
    async (questions, { dispatch }) => {
        return await apiHandler(dispatch, aiApi.classifyQuestions, questions, () => { }, false, false);
    }
);

export const fixTextAndLatex = createAsyncThunk(
    "ai/fixTextAndLatex",
    async (text, { dispatch }) => {
        return await apiHandler(dispatch, aiApi.fixTextAndLatex, text, () => { }, false, false);
    }
);


const aiSlice = createSlice({
    name: "ai",
    initialState: {
        aiResponse: null,
        loading: false,
        fixTextResult: null,
        userQuestion: {
            1: 'Hãy giải bài toán này chi tiết từng bước.',
            2: 'Giải thích đáp án trong câu hỏi này.',
            3: 'Đáp án câu hỏi này đang bị sai, hãy sửa lại.',
            4: 'Giải thích lời giải trong câu hỏi này.',
            5: 'Lời giải câu hỏi này đang bị sai, hãy sửa lại.'
        }
    },
    reducers: {
        setAiResponse: (state, action) => {
            state.aiResponse = action.payload;
        },
        resetAiResponse: (state) => {
            state.aiResponse = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(askQuestionWithAI.pending, (state) => {
                state.loading = true;
            })
            .addCase(askQuestionWithAI.fulfilled, (state, action) => {
                state.aiResponse = action.payload.data.aiResponse;
                state.loading = false;
            })
            .addCase(askQuestionWithAI.rejected, (state) => {
                state.loading = false;
            })
            .addCase(classifyQuestions.pending, (state) => {
                state.loading = true;
            })
            .addCase(classifyQuestions.fulfilled, (state, action) => {
                state.classifyResult = action.payload.data;
                state.loading = false;
            })
            .addCase(classifyQuestions.rejected, (state) => {
                state.loading = false;
            })
            .addCase(fixTextAndLatex.pending, (state) => {
            })
            .addCase(fixTextAndLatex.fulfilled, (state, action) => {
                state.fixTextResult = action.payload.data;
            })
            .addCase(fixTextAndLatex.rejected, (state) => {
            })
    },
});

export const { setAiResponse, resetAiResponse } = aiSlice.actions;
export default aiSlice.reducer;