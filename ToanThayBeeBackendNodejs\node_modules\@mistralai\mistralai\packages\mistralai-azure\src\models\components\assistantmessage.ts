/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  ContentChunk,
  ContentChunk$inboundSchema,
  ContentChunk$Outbound,
  ContentChunk$outboundSchema,
} from "./contentchunk.js";
import {
  ToolCall,
  ToolCall$inboundSchema,
  ToolCall$Outbound,
  ToolCall$outboundSchema,
} from "./toolcall.js";

export type AssistantMessageContent = string | Array<ContentChunk>;

export const AssistantMessageRole = {
  Assistant: "assistant",
} as const;
export type AssistantMessageRole = ClosedEnum<typeof AssistantMessageRole>;

export type AssistantMessage = {
  content?: string | Array<ContentChunk> | null | undefined;
  toolCalls?: Array<ToolCall> | null | undefined;
  /**
   * Set this to `true` when adding an assistant message as prefix to condition the model response. The role of the prefix message is to force the model to start its answer by the content of the message.
   */
  prefix?: boolean | undefined;
  role?: AssistantMessageRole | undefined;
};

/** @internal */
export const AssistantMessageContent$inboundSchema: z.ZodType<
  AssistantMessageContent,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.array(ContentChunk$inboundSchema)]);

/** @internal */
export type AssistantMessageContent$Outbound =
  | string
  | Array<ContentChunk$Outbound>;

/** @internal */
export const AssistantMessageContent$outboundSchema: z.ZodType<
  AssistantMessageContent$Outbound,
  z.ZodTypeDef,
  AssistantMessageContent
> = z.union([z.string(), z.array(ContentChunk$outboundSchema)]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace AssistantMessageContent$ {
  /** @deprecated use `AssistantMessageContent$inboundSchema` instead. */
  export const inboundSchema = AssistantMessageContent$inboundSchema;
  /** @deprecated use `AssistantMessageContent$outboundSchema` instead. */
  export const outboundSchema = AssistantMessageContent$outboundSchema;
  /** @deprecated use `AssistantMessageContent$Outbound` instead. */
  export type Outbound = AssistantMessageContent$Outbound;
}

export function assistantMessageContentToJSON(
  assistantMessageContent: AssistantMessageContent,
): string {
  return JSON.stringify(
    AssistantMessageContent$outboundSchema.parse(assistantMessageContent),
  );
}

export function assistantMessageContentFromJSON(
  jsonString: string,
): SafeParseResult<AssistantMessageContent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => AssistantMessageContent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'AssistantMessageContent' from JSON`,
  );
}

/** @internal */
export const AssistantMessageRole$inboundSchema: z.ZodNativeEnum<
  typeof AssistantMessageRole
> = z.nativeEnum(AssistantMessageRole);

/** @internal */
export const AssistantMessageRole$outboundSchema: z.ZodNativeEnum<
  typeof AssistantMessageRole
> = AssistantMessageRole$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace AssistantMessageRole$ {
  /** @deprecated use `AssistantMessageRole$inboundSchema` instead. */
  export const inboundSchema = AssistantMessageRole$inboundSchema;
  /** @deprecated use `AssistantMessageRole$outboundSchema` instead. */
  export const outboundSchema = AssistantMessageRole$outboundSchema;
}

/** @internal */
export const AssistantMessage$inboundSchema: z.ZodType<
  AssistantMessage,
  z.ZodTypeDef,
  unknown
> = z.object({
  content: z.nullable(
    z.union([z.string(), z.array(ContentChunk$inboundSchema)]),
  ).optional(),
  tool_calls: z.nullable(z.array(ToolCall$inboundSchema)).optional(),
  prefix: z.boolean().default(false),
  role: AssistantMessageRole$inboundSchema.default("assistant"),
}).transform((v) => {
  return remap$(v, {
    "tool_calls": "toolCalls",
  });
});

/** @internal */
export type AssistantMessage$Outbound = {
  content?: string | Array<ContentChunk$Outbound> | null | undefined;
  tool_calls?: Array<ToolCall$Outbound> | null | undefined;
  prefix: boolean;
  role: string;
};

/** @internal */
export const AssistantMessage$outboundSchema: z.ZodType<
  AssistantMessage$Outbound,
  z.ZodTypeDef,
  AssistantMessage
> = z.object({
  content: z.nullable(
    z.union([z.string(), z.array(ContentChunk$outboundSchema)]),
  ).optional(),
  toolCalls: z.nullable(z.array(ToolCall$outboundSchema)).optional(),
  prefix: z.boolean().default(false),
  role: AssistantMessageRole$outboundSchema.default("assistant"),
}).transform((v) => {
  return remap$(v, {
    toolCalls: "tool_calls",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace AssistantMessage$ {
  /** @deprecated use `AssistantMessage$inboundSchema` instead. */
  export const inboundSchema = AssistantMessage$inboundSchema;
  /** @deprecated use `AssistantMessage$outboundSchema` instead. */
  export const outboundSchema = AssistantMessage$outboundSchema;
  /** @deprecated use `AssistantMessage$Outbound` instead. */
  export type Outbound = AssistantMessage$Outbound;
}

export function assistantMessageToJSON(
  assistantMessage: AssistantMessage,
): string {
  return JSON.stringify(
    AssistantMessage$outboundSchema.parse(assistantMessage),
  );
}

export function assistantMessageFromJSON(
  jsonString: string,
): SafeParseResult<AssistantMessage, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => AssistantMessage$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'AssistantMessage' from JSON`,
  );
}
