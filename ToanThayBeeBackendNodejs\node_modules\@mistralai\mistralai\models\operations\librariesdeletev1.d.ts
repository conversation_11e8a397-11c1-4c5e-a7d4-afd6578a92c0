import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type LibrariesDeleteV1Request = {
    libraryId: string;
};
/** @internal */
export declare const LibrariesDeleteV1Request$inboundSchema: z.ZodType<LibrariesDeleteV1Request, z.ZodTypeDef, unknown>;
/** @internal */
export type LibrariesDeleteV1Request$Outbound = {
    library_id: string;
};
/** @internal */
export declare const LibrariesDeleteV1Request$outboundSchema: z.ZodType<LibrariesDeleteV1Request$Outbound, z.ZodTypeDef, LibrariesDeleteV1Request>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace LibrariesDeleteV1Request$ {
    /** @deprecated use `LibrariesDeleteV1Request$inboundSchema` instead. */
    const inboundSchema: z.ZodType<LibrariesDeleteV1Request, z.ZodTypeDef, unknown>;
    /** @deprecated use `LibrariesDeleteV1Request$outboundSchema` instead. */
    const outboundSchema: z.ZodType<LibrariesDeleteV1Request$Outbound, z.ZodTypeDef, LibrariesDeleteV1Request>;
    /** @deprecated use `LibrariesDeleteV1Request$Outbound` instead. */
    type Outbound = LibrariesDeleteV1Request$Outbound;
}
export declare function librariesDeleteV1RequestToJSON(librariesDeleteV1Request: LibrariesDeleteV1Request): string;
export declare function librariesDeleteV1RequestFromJSON(jsonString: string): SafeParseResult<LibrariesDeleteV1Request, SDKValidationError>;
//# sourceMappingURL=librariesdeletev1.d.ts.map