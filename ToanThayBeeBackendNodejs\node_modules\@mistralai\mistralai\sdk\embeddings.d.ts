import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
export declare class Embeddings extends ClientSDK {
    /**
     * Embeddings
     *
     * @remarks
     * Embeddings
     */
    create(request: components.EmbeddingRequest, options?: RequestOptions): Promise<components.EmbeddingResponse>;
}
//# sourceMappingURL=embeddings.d.ts.map