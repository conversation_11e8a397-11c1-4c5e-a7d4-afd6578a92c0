/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  ContentChunk,
  ContentChunk$inboundSchema,
  ContentChunk$Outbound,
  ContentChunk$outboundSchema,
} from "./contentchunk.js";
import {
  ToolCall,
  ToolCall$inboundSchema,
  ToolCall$Outbound,
  ToolCall$outboundSchema,
} from "./toolcall.js";

export type Content = string | Array<ContentChunk>;

export type DeltaMessage = {
  role?: string | null | undefined;
  content?: string | Array<ContentChunk> | null | undefined;
  toolCalls?: Array<ToolCall> | null | undefined;
};

/** @internal */
export const Content$inboundSchema: z.ZodType<Content, z.ZodTypeDef, unknown> =
  z.union([z.string(), z.array(ContentChunk$inboundSchema)]);

/** @internal */
export type Content$Outbound = string | Array<ContentChunk$Outbound>;

/** @internal */
export const Content$outboundSchema: z.ZodType<
  Content$Outbound,
  z.ZodTypeDef,
  Content
> = z.union([z.string(), z.array(ContentChunk$outboundSchema)]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Content$ {
  /** @deprecated use `Content$inboundSchema` instead. */
  export const inboundSchema = Content$inboundSchema;
  /** @deprecated use `Content$outboundSchema` instead. */
  export const outboundSchema = Content$outboundSchema;
  /** @deprecated use `Content$Outbound` instead. */
  export type Outbound = Content$Outbound;
}

export function contentToJSON(content: Content): string {
  return JSON.stringify(Content$outboundSchema.parse(content));
}

export function contentFromJSON(
  jsonString: string,
): SafeParseResult<Content, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Content$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Content' from JSON`,
  );
}

/** @internal */
export const DeltaMessage$inboundSchema: z.ZodType<
  DeltaMessage,
  z.ZodTypeDef,
  unknown
> = z.object({
  role: z.nullable(z.string()).optional(),
  content: z.nullable(
    z.union([z.string(), z.array(ContentChunk$inboundSchema)]),
  ).optional(),
  tool_calls: z.nullable(z.array(ToolCall$inboundSchema)).optional(),
}).transform((v) => {
  return remap$(v, {
    "tool_calls": "toolCalls",
  });
});

/** @internal */
export type DeltaMessage$Outbound = {
  role?: string | null | undefined;
  content?: string | Array<ContentChunk$Outbound> | null | undefined;
  tool_calls?: Array<ToolCall$Outbound> | null | undefined;
};

/** @internal */
export const DeltaMessage$outboundSchema: z.ZodType<
  DeltaMessage$Outbound,
  z.ZodTypeDef,
  DeltaMessage
> = z.object({
  role: z.nullable(z.string()).optional(),
  content: z.nullable(
    z.union([z.string(), z.array(ContentChunk$outboundSchema)]),
  ).optional(),
  toolCalls: z.nullable(z.array(ToolCall$outboundSchema)).optional(),
}).transform((v) => {
  return remap$(v, {
    toolCalls: "tool_calls",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DeltaMessage$ {
  /** @deprecated use `DeltaMessage$inboundSchema` instead. */
  export const inboundSchema = DeltaMessage$inboundSchema;
  /** @deprecated use `DeltaMessage$outboundSchema` instead. */
  export const outboundSchema = DeltaMessage$outboundSchema;
  /** @deprecated use `DeltaMessage$Outbound` instead. */
  export type Outbound = DeltaMessage$Outbound;
}

export function deltaMessageToJSON(deltaMessage: DeltaMessage): string {
  return JSON.stringify(DeltaMessage$outboundSchema.parse(deltaMessage));
}

export function deltaMessageFromJSON(
  jsonString: string,
): SafeParseResult<DeltaMessage, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => DeltaMessage$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DeltaMessage' from JSON`,
  );
}
