import axios from 'axios';
import path from 'path';

import { uploadImage } from '../utils/imageUpload.js'; // hoặc đường dẫn thực tế của bạn
import { Buffer } from 'buffer';

/**
 * L<PERSON>y ảnh từ URL và chuyển thành base64 string để gửi GPT-4o
 * @param {string} imageUrl - Đường dẫn ảnh từ Firebase hoặc nơi khác
 * @returns {Promise<string>} - Chuỗi base64 có header: data:image/png;base64,...
 */
export const getImageBase64FromUrl = async (imageUrl) => {
    try {
        const response = await axios.get(imageUrl, {
            responseType: 'arraybuffer',
            timeout: 5000, // tránh treo
        });

        const contentType = response.headers['content-type'] || 'image/png';
        const base64 = Buffer.from(response.data, 'binary').toString('base64');

        return `data:${contentType};base64,${base64}`;
    } catch (error) {
        console.error('Lỗi khi tải ảnh:', error.message);
        throw new Error('<PERSON>hông thể tải ảnh từ URL');
    }
};

export const base64ToImage = async (base64) => {
    const matches = base64.match(/^data:(image\/\w+);base64,(.+)$/);
    const base64Data = matches ? matches[2] : base64;
    const buffer = Buffer.from(base64Data, 'base64');

    return buffer;
}


export async function uploadBase64Image(base64, folder) {
    const matches = base64.match(/^data:(image\/\w+);base64,(.+)$/);
    const base64Data = matches ? matches[2] : base64;
    const buffer = Buffer.from(base64Data, 'base64');

    const imageUrl = await uploadImage(buffer, folder);
    if (!imageUrl) {
        throw new Error('Upload failed');
    }

    return imageUrl;
}

/**
 * Upload nhiều ảnh base64 lên Firebase
 * @param {string[]} images - Mảng base64
 * @param {string} folder - Tên folder lưu ảnh
 * @returns {Promise<string[]>} - Mảng URL đã upload
 */
export async function uploadBase64Images(images, folder) {
    if (!Array.isArray(images) || images.length === 0) {
        throw new Error("Missing images array");
    }

    if (!folder) {
        throw new Error("Missing folder");
    }

    const uploadedUrls = [];

    for (let i = 0; i < images.length; i++) {
        const base64 = images[i];
        const imageUrl = await uploadBase64Image(base64, folder); // Sử dụng hàm uploadBase64Image đã định nghĩa ở trên
        if (!imageUrl) {
            throw new Error(`Upload failed for image ${i + 1}`);
        }

        uploadedUrls.push(imageUrl);
    }

    return uploadedUrls;
}