import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type FilesApiRoutesDeleteFileRequest = {
    fileId: string;
};
/** @internal */
export declare const FilesApiRoutesDeleteFileRequest$inboundSchema: z.ZodType<FilesApiRoutesDeleteFileRequest, z.ZodTypeDef, unknown>;
/** @internal */
export type FilesApiRoutesDeleteFileRequest$Outbound = {
    file_id: string;
};
/** @internal */
export declare const FilesApiRoutesDeleteFileRequest$outboundSchema: z.ZodType<FilesApiRoutesDeleteFileRequest$Outbound, z.ZodTypeDef, FilesApiRoutesDeleteFileRequest>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace FilesApiRoutesDeleteFileRequest$ {
    /** @deprecated use `FilesApiRoutesDeleteFileRequest$inboundSchema` instead. */
    const inboundSchema: z.ZodType<FilesApiRoutesDeleteFileRequest, z.ZodTypeDef, unknown>;
    /** @deprecated use `FilesApiRoutesDeleteFileRequest$outboundSchema` instead. */
    const outboundSchema: z.ZodType<FilesApiRoutesDeleteFileRequest$Outbound, z.ZodTypeDef, FilesApiRoutesDeleteFileRequest>;
    /** @deprecated use `FilesApiRoutesDeleteFileRequest$Outbound` instead. */
    type Outbound = FilesApiRoutesDeleteFileRequest$Outbound;
}
export declare function filesApiRoutesDeleteFileRequestToJSON(filesApiRoutesDeleteFileRequest: FilesApiRoutesDeleteFileRequest): string;
export declare function filesApiRoutesDeleteFileRequestFromJSON(jsonString: string): SafeParseResult<FilesApiRoutesDeleteFileRequest, SDKValidationError>;
//# sourceMappingURL=filesapiroutesdeletefile.d.ts.map