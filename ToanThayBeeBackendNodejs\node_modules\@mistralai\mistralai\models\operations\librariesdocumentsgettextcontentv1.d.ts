import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type LibrariesDocumentsGetTextContentV1Request = {
    libraryId: string;
    documentId: string;
};
/** @internal */
export declare const LibrariesDocumentsGetTextContentV1Request$inboundSchema: z.ZodType<LibrariesDocumentsGetTextContentV1Request, z.ZodTypeDef, unknown>;
/** @internal */
export type LibrariesDocumentsGetTextContentV1Request$Outbound = {
    library_id: string;
    document_id: string;
};
/** @internal */
export declare const LibrariesDocumentsGetTextContentV1Request$outboundSchema: z.ZodType<LibrariesDocumentsGetTextContentV1Request$Outbound, z.ZodTypeDef, LibrariesDocumentsGetTextContentV1Request>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace LibrariesDocumentsGetTextContentV1Request$ {
    /** @deprecated use `LibrariesDocumentsGetTextContentV1Request$inboundSchema` instead. */
    const inboundSchema: z.ZodType<LibrariesDocumentsGetTextContentV1Request, z.ZodTypeDef, unknown>;
    /** @deprecated use `LibrariesDocumentsGetTextContentV1Request$outboundSchema` instead. */
    const outboundSchema: z.ZodType<LibrariesDocumentsGetTextContentV1Request$Outbound, z.ZodTypeDef, LibrariesDocumentsGetTextContentV1Request>;
    /** @deprecated use `LibrariesDocumentsGetTextContentV1Request$Outbound` instead. */
    type Outbound = LibrariesDocumentsGetTextContentV1Request$Outbound;
}
export declare function librariesDocumentsGetTextContentV1RequestToJSON(librariesDocumentsGetTextContentV1Request: LibrariesDocumentsGetTextContentV1Request): string;
export declare function librariesDocumentsGetTextContentV1RequestFromJSON(jsonString: string): SafeParseResult<LibrariesDocumentsGetTextContentV1Request, SDKValidationError>;
//# sourceMappingURL=librariesdocumentsgettextcontentv1.d.ts.map