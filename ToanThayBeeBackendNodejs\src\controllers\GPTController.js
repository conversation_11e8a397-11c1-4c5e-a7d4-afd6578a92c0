import { callGPT, askQuestionWithAI } from '../services/gpt.service.js';

const messages = [
    { role: 'system', content: 'Bạn là trợ lý AI hữu ích.' },
    { role: 'user', content: '<PERSON><PERSON><PERSON> giải thích GPT hoạt động thế nào?' }
];

export const callGPTController = async (req, res) => {
    const response = await callGPT(messages);
    res.status(200).json({
        message: 'GPT response',
        data: response
    });
}

// Controller cho tính năng hỏi đáp AI với câu hỏi từ database
export const askQuestionController = async (req, res) => {
    try {
        const { questionId } = req.params;
        const { messageId } = req.body;

        // Validate questionId
        if (!questionId || isNaN(questionId)) {
            return res.status(400).json({
                message: 'ID câu hỏi không hợp lệ',
                error: 'questionId phải là một số'
            });
        }

        // Validate messageId
        if (!messageId || isNaN(messageId)) {
            return res.status(400).json({
                message: 'ID tin nhắn không hợp lệ',
                error: 'messageId phải là một số'
            });
        }

        // Gọi service để xử lý
        const result = await askQuestionWithAI(parseInt(questionId), parseInt(messageId));

        return res.status(200).json({
            message: 'Đã xử lý câu hỏi thành công',
            data: result
        });

    } catch (error) {
        console.error('Error in askQuestionController:', error);

        if (error.message === 'Không tìm thấy câu hỏi') {
            return res.status(404).json({
                message: 'Không tìm thấy câu hỏi',
                error: error.message
            });
        }

        return res.status(500).json({
            message: 'Lỗi server khi xử lý câu hỏi',
            error: error.message
        });
    }
}



