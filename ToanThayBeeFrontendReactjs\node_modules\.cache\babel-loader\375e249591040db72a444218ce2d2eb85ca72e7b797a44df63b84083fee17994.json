{"ast": null, "code": "import api from \"./api\";\nexport const getImagesAPI = async _ref => {\n  let {\n    folder\n  } = _ref;\n  return await api.get(\"/v1/images/\".concat(folder));\n};\nexport const getPdfsAPI = async _ref2 => {\n  let {\n    folder\n  } = _ref2;\n  return await api.get(\"/v1/pdfs/\".concat(folder));\n};\nexport const getImagesFoldersAPI = async _ref3 => {\n  let {\n    folders\n  } = _ref3;\n  return await api.post(\"/v1/images/folders\", {\n    folders\n  });\n};\nexport const postImageAPI = async _ref4 => {\n  let {\n    image,\n    folder\n  } = _ref4;\n  const formData = new FormData();\n  formData.append(\"image\", image);\n  formData.append(\"folder\", folder);\n  const response = await api.post(\"/v1/images/google/upload-single\", formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\"\n    }\n  });\n\n  // Add folder to the response data for use in the reducer\n  if (response.data) {\n    response.data.folder = folder;\n  }\n  return response;\n};\nexport const uploadMultipleImagesAPI = async _ref5 => {\n  let {\n    files,\n    folder\n  } = _ref5;\n  const formData = new FormData();\n\n  // Append multiple files\n  files.forEach((file, index) => {\n    formData.append('images', file);\n  });\n  formData.append(\"folder\", folder);\n  const response = await api.post(\"/v1/images/google/upload-multiple\", formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n  return response;\n};\nexport const deleteImageAPI = async _ref6 => {\n  let {\n    imageUrl\n  } = _ref6;\n  // Extract folder from imageUrl if possible\n  let folder = null;\n  try {\n    // Assuming imageUrl format contains folder name in the path\n    // Example: https://storage.googleapis.com/toanthaybee/article/image.jpg\n    const urlParts = imageUrl.split('/');\n    // Get the second-to-last part which should be the folder name\n    if (urlParts.length > 1) {\n      folder = urlParts[urlParts.length - 2];\n    }\n  } catch (error) {\n    console.error('Error extracting folder from URL:', error);\n  }\n  const response = await api.delete(\"/v1/images/delete\", {\n    data: {\n      imageUrl\n    }\n  });\n\n  // Add folder to the response data for use in the reducer\n  if (response.data) {\n    response.data.folder = folder;\n  }\n  return response;\n};\nexport const uploadBase64ImagesAPI = async _ref7 => {\n  let {\n    images,\n    folder\n  } = _ref7;\n  const response = await api.post(\"/v1/images/google/upload-base64\", {\n    images,\n    folder\n  }, {\n    headers: {\n      'Content-Type': 'application/json'\n    }\n  });\n  return response;\n};", "map": {"version": 3, "names": ["api", "getImagesAPI", "_ref", "folder", "get", "concat", "getPdfsAPI", "_ref2", "getImagesFoldersAPI", "_ref3", "folders", "post", "postImageAPI", "_ref4", "image", "formData", "FormData", "append", "response", "headers", "data", "uploadMultipleImagesAPI", "_ref5", "files", "for<PERSON>ach", "file", "index", "deleteImageAPI", "_ref6", "imageUrl", "urlParts", "split", "length", "error", "console", "delete", "uploadBase64ImagesAPI", "_ref7", "images"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/services/imageApi.js"], "sourcesContent": ["import api from \"./api\";\r\n\r\nexport const getImagesAPI = async ({ folder }) => {\r\n    return await api.get(`/v1/images/${folder}`);\r\n}\r\n\r\nexport const getPdfsAPI = async ({ folder }) => {\r\n    return await api.get(`/v1/pdfs/${folder}`);\r\n}\r\n\r\nexport const getImagesFoldersAPI = async ({ folders }) => {\r\n    return await api.post(`/v1/images/folders`, { folders });\r\n}\r\n\r\nexport const postImageAPI = async ({ image, folder }) => {\r\n    const formData = new FormData();\r\n    formData.append(\"image\", image);\r\n    formData.append(\"folder\", folder);\r\n    const response = await api.post(\"/v1/images/google/upload-single\", formData, {\r\n        headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n    });\r\n\r\n    // Add folder to the response data for use in the reducer\r\n    if (response.data) {\r\n        response.data.folder = folder;\r\n    }\r\n\r\n    return response;\r\n}\r\n\r\nexport const uploadMultipleImagesAPI = async ({ files, folder }) => {\r\n    const formData = new FormData();\r\n\r\n    // Append multiple files\r\n    files.forEach((file, index) => {\r\n        formData.append('images', file);\r\n    });\r\n\r\n    formData.append(\"folder\", folder);\r\n\r\n    const response = await api.post(\r\n        \"/v1/images/google/upload-multiple\",\r\n        formData,\r\n        {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        }\r\n    );\r\n\r\n    return response;\r\n}\r\n\r\nexport const deleteImageAPI = async ({ imageUrl }) => {\r\n    // Extract folder from imageUrl if possible\r\n    let folder = null;\r\n    try {\r\n        // Assuming imageUrl format contains folder name in the path\r\n        // Example: https://storage.googleapis.com/toanthaybee/article/image.jpg\r\n        const urlParts = imageUrl.split('/');\r\n        // Get the second-to-last part which should be the folder name\r\n        if (urlParts.length > 1) {\r\n            folder = urlParts[urlParts.length - 2];\r\n        }\r\n    } catch (error) {\r\n        console.error('Error extracting folder from URL:', error);\r\n    }\r\n\r\n    const response = await api.delete(`/v1/images/delete`, {\r\n        data: { imageUrl },\r\n    });\r\n\r\n    // Add folder to the response data for use in the reducer\r\n    if (response.data) {\r\n        response.data.folder = folder;\r\n    }\r\n\r\n    return response;\r\n};\r\n\r\nexport const uploadBase64ImagesAPI = async ({ images, folder }) => {\r\n    const response = await api.post(\r\n        \"/v1/images/google/upload-base64\",\r\n        { images, folder },\r\n        {\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n            },\r\n        }\r\n    );\r\n\r\n    return response;\r\n}\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,OAAO,MAAMC,YAAY,GAAG,MAAAC,IAAA,IAAsB;EAAA,IAAf;IAAEC;EAAO,CAAC,GAAAD,IAAA;EACzC,OAAO,MAAMF,GAAG,CAACI,GAAG,eAAAC,MAAA,CAAeF,MAAM,CAAE,CAAC;AAChD,CAAC;AAED,OAAO,MAAMG,UAAU,GAAG,MAAAC,KAAA,IAAsB;EAAA,IAAf;IAAEJ;EAAO,CAAC,GAAAI,KAAA;EACvC,OAAO,MAAMP,GAAG,CAACI,GAAG,aAAAC,MAAA,CAAaF,MAAM,CAAE,CAAC;AAC9C,CAAC;AAED,OAAO,MAAMK,mBAAmB,GAAG,MAAAC,KAAA,IAAuB;EAAA,IAAhB;IAAEC;EAAQ,CAAC,GAAAD,KAAA;EACjD,OAAO,MAAMT,GAAG,CAACW,IAAI,uBAAuB;IAAED;EAAQ,CAAC,CAAC;AAC5D,CAAC;AAED,OAAO,MAAME,YAAY,GAAG,MAAAC,KAAA,IAA6B;EAAA,IAAtB;IAAEC,KAAK;IAAEX;EAAO,CAAC,GAAAU,KAAA;EAChD,MAAME,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,KAAK,CAAC;EAC/BC,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEd,MAAM,CAAC;EACjC,MAAMe,QAAQ,GAAG,MAAMlB,GAAG,CAACW,IAAI,CAAC,iCAAiC,EAAEI,QAAQ,EAAE;IACzEI,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;;EAEF;EACA,IAAID,QAAQ,CAACE,IAAI,EAAE;IACfF,QAAQ,CAACE,IAAI,CAACjB,MAAM,GAAGA,MAAM;EACjC;EAEA,OAAOe,QAAQ;AACnB,CAAC;AAED,OAAO,MAAMG,uBAAuB,GAAG,MAAAC,KAAA,IAA6B;EAAA,IAAtB;IAAEC,KAAK;IAAEpB;EAAO,CAAC,GAAAmB,KAAA;EAC3D,MAAMP,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;EAE/B;EACAO,KAAK,CAACC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;IAC3BX,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEQ,IAAI,CAAC;EACnC,CAAC,CAAC;EAEFV,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEd,MAAM,CAAC;EAEjC,MAAMe,QAAQ,GAAG,MAAMlB,GAAG,CAACW,IAAI,CAC3B,mCAAmC,EACnCI,QAAQ,EACR;IACII,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CACJ,CAAC;EAED,OAAOD,QAAQ;AACnB,CAAC;AAED,OAAO,MAAMS,cAAc,GAAG,MAAAC,KAAA,IAAwB;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAD,KAAA;EAC7C;EACA,IAAIzB,MAAM,GAAG,IAAI;EACjB,IAAI;IACA;IACA;IACA,MAAM2B,QAAQ,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;IACpC;IACA,IAAID,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;MACrB7B,MAAM,GAAG2B,QAAQ,CAACA,QAAQ,CAACE,MAAM,GAAG,CAAC,CAAC;IAC1C;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;EAC7D;EAEA,MAAMf,QAAQ,GAAG,MAAMlB,GAAG,CAACmC,MAAM,sBAAsB;IACnDf,IAAI,EAAE;MAAES;IAAS;EACrB,CAAC,CAAC;;EAEF;EACA,IAAIX,QAAQ,CAACE,IAAI,EAAE;IACfF,QAAQ,CAACE,IAAI,CAACjB,MAAM,GAAGA,MAAM;EACjC;EAEA,OAAOe,QAAQ;AACnB,CAAC;AAED,OAAO,MAAMkB,qBAAqB,GAAG,MAAAC,KAAA,IAA8B;EAAA,IAAvB;IAAEC,MAAM;IAAEnC;EAAO,CAAC,GAAAkC,KAAA;EAC1D,MAAMnB,QAAQ,GAAG,MAAMlB,GAAG,CAACW,IAAI,CAC3B,iCAAiC,EACjC;IAAE2B,MAAM;IAAEnC;EAAO,CAAC,EAClB;IACIgB,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CACJ,CAAC;EAED,OAAOD,QAAQ;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}