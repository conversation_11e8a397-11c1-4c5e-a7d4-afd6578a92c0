{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\input\\\\TextArea.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from \"react\";\nimport { Info } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TextArea = _ref => {\n  _s();\n  let {\n    value,\n    onChange,\n    placeholder,\n    label,\n    Icon = null,\n    hint = \"\",\n    // nội dung tooltip\n    buttonFilterText = false\n  } = _ref;\n  const textAreaRef = useRef(null);\n  const [showTooltip, setShowTooltip] = useState(false);\n  useEffect(() => {\n    if (textAreaRef.current) {\n      textAreaRef.current.style.height = \"auto\";\n      textAreaRef.current.style.height = textAreaRef.current.scrollHeight + \"px\";\n    }\n  }, [value]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col gap-2 relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-row justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"text-xs font-medium text-gray-700 flex items-center gap-1 relative\",\n        children: [Icon && /*#__PURE__*/_jsxDEV(Icon, {\n          className: \"w-3 h-3 inline\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 30\n        }, this), label, hint && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative flex items-center\",\n          onMouseEnter: () => setShowTooltip(true),\n          onMouseLeave: () => setShowTooltip(false),\n          children: [/*#__PURE__*/_jsxDEV(Info, {\n            className: \"w-3 h-3 text-gray-400 cursor-pointer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 29\n          }, this), showTooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-6 left-0 z-50 w-64 p-2 text-xs text-white bg-gray-800 rounded shadow-md\",\n            children: hint\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 17\n      }, this), buttonFilterText && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: buttonFilterText.onClick,\n        className: \"text-xs font-medium text-white hover:underline px-2 py-1 bg-blue-500 rounded\",\n        children: buttonFilterText.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n      ref: textAreaRef,\n      value: value,\n      onChange: onChange,\n      className: \"w-full px-2 py-4 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none overflow-hidden\",\n      placeholder: placeholder,\n      rows: 1\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 9\n  }, this);\n};\n_s(TextArea, \"0g+Ncv8PWsfOL5BPme6Qcw+cefo=\");\n_c = TextArea;\nexport default TextArea;\nvar _c;\n$RefreshReg$(_c, \"TextArea\");", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "Info", "jsxDEV", "_jsxDEV", "TextArea", "_ref", "_s", "value", "onChange", "placeholder", "label", "Icon", "hint", "buttonFilterText", "textAreaRef", "showTooltip", "setShowTooltip", "current", "style", "height", "scrollHeight", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onMouseEnter", "onMouseLeave", "onClick", "text", "ref", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/input/TextArea.jsx"], "sourcesContent": ["import { useEffect, useRef, useState } from \"react\";\r\nimport { Info } from \"lucide-react\";\r\n\r\nconst TextArea = ({\r\n    value,\r\n    onChange,\r\n    placeholder,\r\n    label,\r\n    Icon = null,\r\n    hint = \"\", // nội dung tooltip\r\n    buttonFilterText = false\r\n}) => {\r\n    const textAreaRef = useRef(null);\r\n    const [showTooltip, setShowTooltip] = useState(false);\r\n\r\n    useEffect(() => {\r\n        if (textAreaRef.current) {\r\n            textAreaRef.current.style.height = \"auto\";\r\n            textAreaRef.current.style.height = textAreaRef.current.scrollHeight + \"px\";\r\n        }\r\n    }, [value]);\r\n\r\n    return (\r\n        <div className=\"flex flex-col gap-2 relative\">\r\n            <div className=\"flex flex-row justify-between items-center\">\r\n                <label className=\"text-xs font-medium text-gray-700 flex items-center gap-1 relative\">\r\n                    {Icon && <Icon className=\"w-3 h-3 inline\" />}\r\n                    {label}\r\n                    {hint && (\r\n                        <div\r\n                            className=\"relative flex items-center\"\r\n                            onMouseEnter={() => setShowTooltip(true)}\r\n                            onMouseLeave={() => setShowTooltip(false)}\r\n                        >\r\n                            <Info className=\"w-3 h-3 text-gray-400 cursor-pointer\" />\r\n                            {showTooltip && (\r\n                                <div className=\"absolute top-6 left-0 z-50 w-64 p-2 text-xs text-white bg-gray-800 rounded shadow-md\">\r\n                                    {hint}\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n                    )}\r\n                </label>\r\n                {buttonFilterText && (\r\n                    <button\r\n                        onClick={buttonFilterText.onClick}\r\n                        className=\"text-xs font-medium text-white hover:underline px-2 py-1 bg-blue-500 rounded\"\r\n                    >\r\n                        {buttonFilterText.text}\r\n                    </button>\r\n                )}\r\n            </div>\r\n            <textarea\r\n                ref={textAreaRef}\r\n                value={value}\r\n                onChange={onChange}\r\n                className=\"w-full px-2 py-4 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none overflow-hidden\"\r\n                placeholder={placeholder}\r\n                rows={1}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default TextArea;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,QAAQ,GAAGC,IAAA,IAQX;EAAAC,EAAA;EAAA,IARY;IACdC,KAAK;IACLC,QAAQ;IACRC,WAAW;IACXC,KAAK;IACLC,IAAI,GAAG,IAAI;IACXC,IAAI,GAAG,EAAE;IAAE;IACXC,gBAAgB,GAAG;EACvB,CAAC,GAAAR,IAAA;EACG,MAAMS,WAAW,GAAGf,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAErDF,SAAS,CAAC,MAAM;IACZ,IAAIgB,WAAW,CAACG,OAAO,EAAE;MACrBH,WAAW,CAACG,OAAO,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;MACzCL,WAAW,CAACG,OAAO,CAACC,KAAK,CAACC,MAAM,GAAGL,WAAW,CAACG,OAAO,CAACG,YAAY,GAAG,IAAI;IAC9E;EACJ,CAAC,EAAE,CAACb,KAAK,CAAC,CAAC;EAEX,oBACIJ,OAAA;IAAKkB,SAAS,EAAC,8BAA8B;IAAAC,QAAA,gBACzCnB,OAAA;MAAKkB,SAAS,EAAC,4CAA4C;MAAAC,QAAA,gBACvDnB,OAAA;QAAOkB,SAAS,EAAC,oEAAoE;QAAAC,QAAA,GAChFX,IAAI,iBAAIR,OAAA,CAACQ,IAAI;UAACU,SAAS,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC3ChB,KAAK,EACLE,IAAI,iBACDT,OAAA;UACIkB,SAAS,EAAC,4BAA4B;UACtCM,YAAY,EAAEA,CAAA,KAAMX,cAAc,CAAC,IAAI,CAAE;UACzCY,YAAY,EAAEA,CAAA,KAAMZ,cAAc,CAAC,KAAK,CAAE;UAAAM,QAAA,gBAE1CnB,OAAA,CAACF,IAAI;YAACoB,SAAS,EAAC;UAAsC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxDX,WAAW,iBACRZ,OAAA;YAAKkB,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EAChGV;UAAI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EACPb,gBAAgB,iBACbV,OAAA;QACI0B,OAAO,EAAEhB,gBAAgB,CAACgB,OAAQ;QAClCR,SAAS,EAAC,8EAA8E;QAAAC,QAAA,EAEvFT,gBAAgB,CAACiB;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACX;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACNvB,OAAA;MACI4B,GAAG,EAAEjB,WAAY;MACjBP,KAAK,EAAEA,KAAM;MACbC,QAAQ,EAAEA,QAAS;MACnBa,SAAS,EAAC,4IAA4I;MACtJZ,WAAW,EAAEA,WAAY;MACzBuB,IAAI,EAAE;IAAE;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACpB,EAAA,CA3DIF,QAAQ;AAAA6B,EAAA,GAAR7B,QAAQ;AA6Dd,eAAeA,QAAQ;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}