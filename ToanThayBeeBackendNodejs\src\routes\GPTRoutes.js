import express from 'express'
import * as GPTController from '../controllers/GPTController.js'
import asyncHandler from '../middlewares/asyncHandler.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import Roles from '../constants/Roles.js'
const router = express.Router()

// Route gốc cho GPT
router.post('/v1/gpt',
    requireRoles(Roles.JustStudent),
    async<PERSON><PERSON><PERSON>(GPTController.callGPTController)
)

// Route để hỏi đáp AI với câu hỏi từ database
router.post('/v1/ai/ask-question/:questionId',
    requireRoles(Roles.JustStudent),
    asyncHandler(GPTController.askQuestionController)
)

// Route để phân loại câu hỏi bằng GPT
router.post('/v1/ai/classify-questions',
    requireRoles(Roles.JustStudent),
    async<PERSON>and<PERSON>(GPTController.classifyQuestionsController)
)

// Route để sửa chính tả và LaTeX bằng GPT
router.post('/v1/ai/fix-text-latex',
    requireRoles(Roles.JustStudent),
    async<PERSON>andler(GPTController.fixTextAndLatexController)
)

export default router