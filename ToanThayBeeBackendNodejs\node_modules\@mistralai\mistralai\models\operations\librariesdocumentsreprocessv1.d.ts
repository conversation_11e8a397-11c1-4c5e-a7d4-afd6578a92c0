import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type LibrariesDocumentsReprocessV1Request = {
    libraryId: string;
    documentId: string;
};
/** @internal */
export declare const LibrariesDocumentsReprocessV1Request$inboundSchema: z.ZodType<LibrariesDocumentsReprocessV1Request, z.ZodTypeDef, unknown>;
/** @internal */
export type LibrariesDocumentsReprocessV1Request$Outbound = {
    library_id: string;
    document_id: string;
};
/** @internal */
export declare const LibrariesDocumentsReprocessV1Request$outboundSchema: z.ZodType<LibrariesDocumentsReprocessV1Request$Outbound, z.ZodTypeDef, LibrariesDocumentsReprocessV1Request>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace LibrariesDocumentsReprocessV1Request$ {
    /** @deprecated use `LibrariesDocumentsReprocessV1Request$inboundSchema` instead. */
    const inboundSchema: z.ZodType<LibrariesDocumentsReprocessV1Request, z.ZodTypeDef, unknown>;
    /** @deprecated use `LibrariesDocumentsReprocessV1Request$outboundSchema` instead. */
    const outboundSchema: z.ZodType<LibrariesDocumentsReprocessV1Request$Outbound, z.ZodTypeDef, LibrariesDocumentsReprocessV1Request>;
    /** @deprecated use `LibrariesDocumentsReprocessV1Request$Outbound` instead. */
    type Outbound = LibrariesDocumentsReprocessV1Request$Outbound;
}
export declare function librariesDocumentsReprocessV1RequestToJSON(librariesDocumentsReprocessV1Request: LibrariesDocumentsReprocessV1Request): string;
export declare function librariesDocumentsReprocessV1RequestFromJSON(jsonString: string): SafeParseResult<LibrariesDocumentsReprocessV1Request, SDKValidationError>;
//# sourceMappingURL=librariesdocumentsreprocessv1.d.ts.map