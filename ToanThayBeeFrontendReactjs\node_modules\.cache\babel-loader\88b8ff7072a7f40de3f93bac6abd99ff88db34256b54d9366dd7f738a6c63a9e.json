{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport TextArea from \"src/components/input/TextArea\";\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\nimport SolutionEditor from \"src/components/PageAddExam/SolutionEditor\";\nimport { setQuestion } from \"src/features/questionsExam/questionsExamSlice\";\nimport { CheckCircle, Plus, Video, Sparkles } from \"lucide-react\";\nimport { setQuestions, setNewQuestion, addQuestion } from \"src/features/questionsExam/questionsExamSlice\";\nimport { addStatement } from \"src/features/questionsExam/questionsExamSlice\";\nimport { fixTextAndLatex } from \"src/features/ai/aiSlice\";\nimport { setErrorMessage } from \"src/features/state/stateApiSlice\";\nimport YouTubePlayer from \"../YouTubePlayer\";\n\n// Component TextArea với nút AI\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TextAreaWithAI = _ref => {\n  _s();\n  let {\n    value,\n    onChange,\n    placeholder,\n    label,\n    Icon\n  } = _ref;\n  const dispatch = useDispatch();\n  const [loading, setLoading] = useState(false);\n  const handleAIFix = async () => {\n    if (!value || !value.trim()) {\n      alert('Vui lòng nhập nội dung trước khi sử dụng AI sửa lỗi');\n      return;\n    }\n    try {\n      setLoading(true);\n      const result = await dispatch(fixTextAndLatex(value)).unwrap();\n      setLoading(false);\n      if (result.data.hasChanges) {\n        onChange({\n          target: {\n            value: result.data.fixedText\n          }\n        });\n      } else {\n        alert('Không tìm thấy lỗi nào cần sửa');\n      }\n    } catch (error) {\n      setLoading(false);\n      console.error('Error fixing text:', error);\n      alert('Có lỗi xảy ra khi sử dụng AI sửa lỗi');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-2\",\n    children: [label && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"flex items-center gap-1 text-sm font-medium text-gray-700\",\n        children: [Icon && /*#__PURE__*/_jsxDEV(Icon, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 34\n        }, this), label]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAIFix,\n        disabled: loading || !(value !== null && value !== void 0 && value.trim()),\n        className: \"flex items-center gap-1 px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n        title: \"S\\u1EED d\\u1EE5ng AI \\u0111\\u1EC3 s\\u1EEDa ch\\xEDnh t\\u1EA3 v\\xE0 k\\xFD hi\\u1EC7u LaTeX\",\n        children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n          className: \"w-3 h-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 25\n        }, this), loading ? 'Đang sửa...' : 'AI Fix']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n      value: value,\n      onChange: onChange,\n      placeholder: placeholder\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 9\n  }, this);\n};\n_s(TextAreaWithAI, \"G3V0PFzKUm/QgxbjQ7d4LdD51Z8=\", false, function () {\n  return [useDispatch];\n});\n_c = TextAreaWithAI;\nconst DetailQuestionView = () => {\n  _s2();\n  const {\n    questionsExam,\n    selectedId,\n    view\n  } = useSelector(state => state.questionsExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const dispatch = useDispatch();\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const [optionChapter, setOptionChapter] = useState([]);\n  const [question, setQuestion] = useState(null);\n  useEffect(() => {\n    // console.log(selectedId);\n\n    if (selectedId && (questionsExam === null || questionsExam === void 0 ? void 0 : questionsExam.length) > 0) {\n      const selectedQuestion = questionsExam === null || questionsExam === void 0 ? void 0 : questionsExam.find(q => q.id === selectedId);\n      setQuestion(selectedQuestion);\n    }\n  }, [selectedId, questionsExam]);\n  const handleQuestionChange = (e, field) => {\n    const updatedQuestion = {\n      ...question,\n      [field]: e.target.value\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleStatementChange = (index, value, field) => {\n    const updatedStatements = [...question.statements];\n    updatedStatements[index] = {\n      ...updatedStatements[index],\n      [field]: value\n    };\n    const updatedQuestion = {\n      ...question,\n      statements: updatedStatements\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleSolutionQuestionChange = value => {\n    const updatedQuestion = {\n      ...question,\n      solution: value\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleAddStatement = () => {\n    dispatch(addStatement());\n  };\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      if (question !== null && question !== void 0 && question.class && (question === null || question === void 0 ? void 0 : question.class.trim()) !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.startsWith(question === null || question === void 0 ? void 0 : question.class) && code.code.length === 5));\n      } else {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, question === null || question === void 0 ? void 0 : question.class]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3 w-full\",\n    children: question && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Ph\\xE2n lo\\u1EA1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-row gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: question.class,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'class'),\n            options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n            selectedOption: question.chapter,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'chapter'),\n            options: optionChapter,\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: question.difficulty,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'difficulty'),\n            options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \" bg-gray-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Th\\xF4ng tin c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(TextAreaWithAI, {\n            value: question.content,\n            onChange: e => handleQuestionChange(e, 'content'),\n            placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n            label: \"C\\xE2u h\\u1ECFi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 29\n          }, this), (view === 'image' || question.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n            imageUrl: question.imageUrl,\n            onImageDrop: image => handleQuestionChange({\n              target: {\n                value: image\n              }\n            }, 'imageUrl'),\n            onImageRemove: () => handleQuestionChange({\n              target: {\n                value: ''\n              }\n            }, 'imageUrl')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 33\n          }, this), question.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [question.statements.length < 4 && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleAddStatement(),\n              className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-blue-600 hover:bg-blue-700 text-white\",\n              children: [/*#__PURE__*/_jsxDEV(Plus, {\n                className: \"w-3 h-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 45\n              }, this), \" Th\\xEAm m\\u1EC7nh \\u0111\\u1EC1\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 41\n            }, this), question.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-2 items-center w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row gap-2 items-center w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs font-bold whitespace-nowrap\",\n                  children: question.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(TextAreaWithAI, {\n                  value: statement.content,\n                  onChange: e => handleStatementChange(index, e.target.value, 'content'),\n                  placeholder: \"Nh\\u1EADp n\\u1ED9i dung m\\u1EC7nh \\u0111\\u1EC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 49\n                }, this), question.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"flex items-center gap-2 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"statement-\".concat(index),\n                      checked: statement.isCorrect,\n                      onChange: () => handleStatementChange(index, true, 'isCorrect'),\n                      className: \"w-4 h-4 text-green-600 focus:ring-green-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 61\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-green-600 font-medium\",\n                      children: \"\\u0110\\xFAng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 57\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"flex items-center gap-2 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"statement-\".concat(index),\n                      checked: !statement.isCorrect,\n                      onChange: () => handleStatementChange(index, false, 'isCorrect'),\n                      className: \"w-4 h-4 text-red-600 focus:ring-red-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 61\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-red-600 font-medium\",\n                      children: \"Sai\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 57\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 53\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 45\n              }, this), (view === 'image' || statement.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n                imageUrl: statement.imageUrl,\n                onImageDrop: image => handleStatementChange(index, image, 'imageUrl'),\n                onImageRemove: () => handleStatementChange(index, '', 'imageUrl')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 49\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 41\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 33\n          }, this), question.typeOfQuestion === 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              value: question.correctAnswer,\n              onChange: e => handleQuestionChange(e, 'correctAnswer'),\n              placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n              label: \"\\u0110\\xE1p \\xE1n\",\n              Icon: CheckCircle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n            value: question.solutionUrl || '',\n            onChange: e => handleQuestionChange(e, 'solutionUrl'),\n            placeholder: \"Nh\\u1EADp link l\\u1EDDi gi\\u1EA3i youtube\",\n            label: \"Link l\\u1EDDi gi\\u1EA3i\",\n            Icon: Video\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 29\n          }, this), question.solutionUrl && /*#__PURE__*/_jsxDEV(YouTubePlayer, {\n            url: question.solutionUrl\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(SolutionEditor, {\n            solution: question.solution,\n            onSolutionChange: handleSolutionQuestionChange,\n            preview: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 9\n  }, this);\n};\n_s2(DetailQuestionView, \"DXB6iz/PdnPpihu4Y8AFD12VQkc=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c2 = DetailQuestionView;\nconst AddQuestionView = () => {\n  _s3();\n  const dispatch = useDispatch();\n  const {\n    newQuestion\n  } = useSelector(state => state.questionsExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const [optionChapter, setOptionChapter] = useState([]);\n  const handleNewQuestionChange = (e, field) => {\n    const updatedQuestion = {\n      ...newQuestion,\n      [field]: e.target.value\n    };\n    dispatch(setNewQuestion(updatedQuestion));\n  };\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      if (newQuestion !== null && newQuestion !== void 0 && newQuestion.class && (newQuestion === null || newQuestion === void 0 ? void 0 : newQuestion.class.trim()) !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.startsWith(newQuestion === null || newQuestion === void 0 ? void 0 : newQuestion.class) && code.code.length === 5));\n      } else {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, newQuestion === null || newQuestion === void 0 ? void 0 : newQuestion.class]);\n  const handleNewStatementChange = (index, value, field) => {\n    const updatedStatements = [...newQuestion.statements];\n    updatedStatements[index] = {\n      ...updatedStatements[index],\n      [field]: value\n    };\n    const updatedQuestion = {\n      ...newQuestion,\n      statements: updatedStatements\n    };\n    dispatch(setNewQuestion(updatedQuestion));\n  };\n  const handleNewSolutionQuestionChange = value => {\n    const updatedQuestion = {\n      ...newQuestion,\n      solution: value\n    };\n    dispatch(setNewQuestion(updatedQuestion));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3 w-full\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-medium text-gray-900 mb-1\",\n        children: \"Ph\\xE2n lo\\u1EA1i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: newQuestion.typeOfQuestion,\n          onChange: option => handleNewQuestionChange({\n            target: {\n              value: option\n            }\n          }, 'typeOfQuestion'),\n          options: [{\n            code: \"TN\",\n            description: \"Trắc nghiệm\"\n          }, {\n            code: \"DS\",\n            description: \"Đúng sai\"\n          }, {\n            code: \"TLN\",\n            description: \"Trả lời ngắn\"\n          }],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: newQuestion.class,\n          onChange: option => handleNewQuestionChange({\n            target: {\n              value: option\n            }\n          }, 'class'),\n          options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n          selectedOption: newQuestion.chapter,\n          onChange: option => handleNewQuestionChange({\n            target: {\n              value: option\n            }\n          }, 'chapter'),\n          options: optionChapter,\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: newQuestion.difficulty,\n          onChange: option => handleNewQuestionChange({\n            target: {\n              value: option\n            }\n          }, 'difficulty'),\n          options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \" bg-gray-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Th\\xF4ng tin c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(TextArea, {\n            value: newQuestion.content,\n            onChange: e => handleNewQuestionChange(e, 'content'),\n            placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n            label: \"C\\xE2u h\\u1ECFi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(ImageDropZone, {\n            imageUrl: newQuestion.imageUrl,\n            onImageDrop: image => handleNewQuestionChange({\n              target: {\n                value: image\n              }\n            }, 'imageUrl'),\n            onImageRemove: () => handleNewQuestionChange({\n              target: {\n                value: ''\n              }\n            }, 'imageUrl')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 25\n          }, this), newQuestion.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: newQuestion.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-2 items-center w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row gap-2 items-center w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs font-bold whitespace-nowrap\",\n                  children: newQuestion.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n                  value: statement.content,\n                  onChange: e => handleNewStatementChange(index, e.target.value, 'content'),\n                  placeholder: \"Nh\\u1EADp n\\u1ED9i dung m\\u1EC7nh \\u0111\\u1EC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"flex items-center gap-2 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"statement-\".concat(index),\n                      checked: statement.isCorrect,\n                      onChange: () => handleNewStatementChange(index, true, 'isCorrect'),\n                      className: \"w-4 h-4 text-green-600 focus:ring-green-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 366,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-green-600 font-medium\",\n                      children: \"\\u0110\\xFAng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"flex items-center gap-2 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      name: \"statement-\".concat(index),\n                      checked: !statement.isCorrect,\n                      onChange: () => handleNewStatementChange(index, false, 'isCorrect'),\n                      className: \"w-4 h-4 text-red-600 focus:ring-red-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-red-600 font-medium\",\n                      children: \"Sai\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(ImageDropZone, {\n                imageUrl: statement.imageUrl,\n                onImageDrop: image => handleNewStatementChange(index, image, 'imageUrl'),\n                onImageRemove: () => handleNewStatementChange(index, '', 'imageUrl')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 41\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 29\n          }, this), newQuestion.typeOfQuestion === 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              value: newQuestion.correctAnswer,\n              onChange: e => handleNewQuestionChange(e, 'correctAnswer'),\n              placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n              label: \"\\u0110\\xE1p \\xE1n\",\n              Icon: CheckCircle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(TextArea, {\n          value: newQuestion.solutionUrl,\n          onChange: e => handleNewQuestionChange(e, 'solutionUrl'),\n          placeholder: \"Nh\\u1EADp link l\\u1EDDi gi\\u1EA3i youtube\",\n          label: \"Link l\\u1EDDi gi\\u1EA3i\",\n          Icon: Video\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 21\n        }, this), newQuestion.solutionUrl && /*#__PURE__*/_jsxDEV(YouTubePlayer, {\n          url: newQuestion.solutionUrl\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(SolutionEditor, {\n          solution: newQuestion.solution,\n          onSolutionChange: handleNewSolutionQuestionChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 307,\n    columnNumber: 9\n  }, this);\n};\n_s3(AddQuestionView, \"R7UojSO0cGUgJuTtpZmH3ejLoVQ=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c3 = AddQuestionView;\nconst LeftContent = () => {\n  _s4();\n  const [view, setView] = useState('questionDetail');\n  const {\n    newQuestion\n  } = useSelector(state => state.questionsExam);\n  const dispatch = useDispatch();\n  const handleAddQuestion = () => {\n    if (!newQuestion.content.trim()) {\n      dispatch(setErrorMessage(\"Nội dung câu hỏi không được để trống!\"));\n      return;\n    }\n    if (!newQuestion.typeOfQuestion) {\n      dispatch(setErrorMessage(\"Loại câu hỏi không được để trống!\"));\n      return;\n    }\n    if (!newQuestion.class) {\n      dispatch(setErrorMessage(\"Lớp không được để trống!\"));\n      return;\n    }\n    if (newQuestion.typeOfQuestion !== 'TLN' && newQuestion.statements.filter(statement => statement.content.trim() !== \"\").length < 4) {\n      dispatch(setErrorMessage(\"Câu hỏi TN phải có ít nhất 4 đáp án!\"));\n      return;\n    }\n    if (newQuestion.typeOfQuestion === 'TLN' && !newQuestion.correctAnswer.trim()) {\n      dispatch(setErrorMessage(\"Đáp án không được để trống!\"));\n      return;\n    }\n    dispatch(addQuestion());\n    setView('questionDetail');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_138px)] overflow-y-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xs font-semibold text-gray-900\",\n          children: \"Chi ti\\u1EBFt c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: view === 'questionDetail' ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setView('addQuestion'),\n          className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-sky-600 hover:bg-sky-700 text-white\",\n          children: \"Th\\xEAm c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddQuestion,\n            className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-emerald-600 hover:bg-emerald-700 text-white\",\n            children: \"L\\u01B0u\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setView('questionDetail'),\n            className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-red-600 hover:bg-red-700 text-white\",\n            children: \"H\\u1EE7y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 13\n    }, this), view === 'questionDetail' && /*#__PURE__*/_jsxDEV(DetailQuestionView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 43\n    }, this), view === 'addQuestion' && /*#__PURE__*/_jsxDEV(AddQuestionView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 40\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 464,\n    columnNumber: 9\n  }, this);\n};\n_s4(LeftContent, \"NcpmA1bQnPPdje5SWgr/AuDkEK4=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c4 = LeftContent;\nexport default LeftContent;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"TextAreaWithAI\");\n$RefreshReg$(_c2, \"DetailQuestionView\");\n$RefreshReg$(_c3, \"AddQuestionView\");\n$RefreshReg$(_c4, \"LeftContent\");", "map": {"version": 3, "names": ["useEffect", "useState", "useSelector", "useDispatch", "DropMenuBarAdmin", "SuggestInputBarAdmin", "TextArea", "ImageDropZone", "SolutionEditor", "setQuestion", "CheckCircle", "Plus", "Video", "<PERSON><PERSON><PERSON>", "setQuestions", "setNewQuestion", "addQuestion", "addStatement", "fixTextAndLatex", "setErrorMessage", "YouTubePlayer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TextAreaWithAI", "_ref", "_s", "value", "onChange", "placeholder", "label", "Icon", "dispatch", "loading", "setLoading", "handleAIFix", "trim", "alert", "result", "unwrap", "data", "has<PERSON><PERSON><PERSON>", "target", "fixedText", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "title", "_c", "DetailQuestionView", "_s2", "questionsExam", "selectedId", "view", "state", "codes", "prefixTN", "prefixDS", "optionChapter", "setOptionChapter", "question", "length", "selectedQuestion", "find", "q", "id", "handleQuestionChange", "e", "field", "updatedQuestion", "handleStatementChange", "index", "updatedStatements", "statements", "handleSolutionQuestionChange", "solution", "handleAddStatement", "Array", "isArray", "class", "filter", "code", "startsWith", "selectedOption", "option", "options", "chapter", "difficulty", "content", "imageUrl", "onImageDrop", "image", "onImageRemove", "typeOfQuestion", "map", "statement", "type", "name", "concat", "checked", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "solutionUrl", "url", "onSolutionChange", "preview", "_c2", "AddQuestionView", "_s3", "newQuestion", "handleNewQuestionChange", "handleNewStatementChange", "handleNewSolutionQuestionChange", "description", "_c3", "LeftContent", "_s4", "<PERSON><PERSON><PERSON><PERSON>", "handleAddQuestion", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/LeftContent.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport TextArea from \"src/components/input/TextArea\";\r\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\r\nimport SolutionEditor from \"src/components/PageAddExam/SolutionEditor\";\r\nimport { setQuestion } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport { CheckCircle, Plus, Video, Sparkles } from \"lucide-react\";\r\nimport { setQuestions, setNewQuestion, addQuestion } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport { addStatement } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport { fixTextAndLatex } from \"src/features/ai/aiSlice\";\r\nimport { setErrorMessage } from \"src/features/state/stateApiSlice\";\r\nimport YouTubePlayer from \"../YouTubePlayer\";\r\n\r\n// Component TextArea với nút AI\r\nconst TextAreaWithAI = ({ value, onChange, placeholder, label, Icon }) => {\r\n    const dispatch = useDispatch();\r\n    const [loading, setLoading] = useState(false);\r\n\r\n    const handleAIFix = async () => {\r\n        if (!value || !value.trim()) {\r\n            alert('Vui lòng nhập nội dung trước khi sử dụng AI sửa lỗi');\r\n            return;\r\n        }\r\n\r\n        try {\r\n            setLoading(true);\r\n            const result = await dispatch(fixTextAndLatex(value)).unwrap();\r\n            setLoading(false);\r\n            if (result.data.hasChanges) {\r\n                onChange({ target: { value: result.data.fixedText } });\r\n            } else {\r\n                alert('Không tìm thấy lỗi nào cần sửa');\r\n            }\r\n        } catch (error) {\r\n            setLoading(false);\r\n            console.error('Error fixing text:', error);\r\n            alert('Có lỗi xảy ra khi sử dụng AI sửa lỗi');\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"space-y-2\">\r\n            {label && (\r\n                <div className=\"flex items-center justify-between\">\r\n                    <label className=\"flex items-center gap-1 text-sm font-medium text-gray-700\">\r\n                        {Icon && <Icon className=\"w-4 h-4\" />}\r\n                        {label}\r\n                    </label>\r\n                    <button\r\n                        onClick={handleAIFix}\r\n                        disabled={loading || !value?.trim()}\r\n                        className=\"flex items-center gap-1 px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                        title=\"Sử dụng AI để sửa chính tả và ký hiệu LaTeX\"\r\n                    >\r\n                        <Sparkles className=\"w-3 h-3\" />\r\n                        {loading ? 'Đang sửa...' : 'AI Fix'}\r\n                    </button>\r\n                </div>\r\n            )}\r\n            <TextArea\r\n                value={value}\r\n                onChange={onChange}\r\n                placeholder={placeholder}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nconst DetailQuestionView = () => {\r\n    const { questionsExam, selectedId, view } = useSelector((state) => state.questionsExam);\r\n    const { codes } = useSelector((state) => state.codes);\r\n    const dispatch = useDispatch();\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n    const [question, setQuestion] = useState(null);\r\n\r\n    useEffect(() => {\r\n        // console.log(selectedId);\r\n\r\n        if (selectedId && questionsExam?.length > 0) {\r\n            const selectedQuestion = questionsExam?.find(q => q.id === selectedId);\r\n            setQuestion(selectedQuestion);\r\n        }\r\n    }, [selectedId, questionsExam]);\r\n\r\n    const handleQuestionChange = (e, field) => {\r\n        const updatedQuestion = { ...question, [field]: e.target.value };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleStatementChange = (index, value, field) => {\r\n        const updatedStatements = [...question.statements];\r\n        updatedStatements[index] = { ...updatedStatements[index], [field]: value };\r\n        const updatedQuestion = { ...question, statements: updatedStatements };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleSolutionQuestionChange = (value) => {\r\n        const updatedQuestion = { ...question, solution: value };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleAddStatement = () => {\r\n        dispatch(addStatement());\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (question?.class && question?.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.startsWith(question?.class) && code.code.length === 5)\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"].filter((code) => code.code.length === 5));\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, question?.class]);\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3 w-full\">\r\n            {question && (\r\n                <div className=\"space-y-3 w-full\">\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Phân loại</h3>\r\n                        <div className=\"flex flex-row gap-2\">\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={question.class}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'class')}\r\n                                options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                                className=\"text-xs\"\r\n                            />\r\n                            <SuggestInputBarAdmin\r\n                                selectedOption={question.chapter}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'chapter')}\r\n                                options={optionChapter}\r\n                                className=\"text-xs\"\r\n                            />\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={question.difficulty}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'difficulty')}\r\n                                options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                                className=\"text-xs\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <hr className=\" bg-gray-200\"></hr>\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thông tin câu hỏi</h3>\r\n                        <div className=\"space-y-2\">\r\n                            <TextAreaWithAI\r\n                                value={question.content}\r\n                                onChange={(e) => handleQuestionChange(e, 'content')}\r\n                                placeholder=\"Nhập nội dung câu hỏi\"\r\n                                label=\"Câu hỏi\"\r\n                            />\r\n                            {(view === 'image' || question.imageUrl) && (\r\n                                <ImageDropZone\r\n                                    imageUrl={question.imageUrl}\r\n                                    onImageDrop={(image) => handleQuestionChange({ target: { value: image } }, 'imageUrl')}\r\n                                    onImageRemove={() => handleQuestionChange({ target: { value: '' } }, 'imageUrl')}\r\n                                />\r\n                            )}\r\n                            {question.typeOfQuestion !== 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    {question.statements.length < 4 && (\r\n                                        <button\r\n                                            onClick={() => handleAddStatement()}\r\n                                            className=\"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-blue-600 hover:bg-blue-700 text-white\"\r\n                                        >\r\n                                            <Plus className=\"w-3 h-3\" /> Thêm mệnh đề\r\n                                        </button>\r\n                                    )}\r\n                                    {question.statements.map((statement, index) => (\r\n                                        <div key={index} className=\"flex flex-col gap-2 items-center w-full\">\r\n                                            <div className=\"flex flex-row gap-2 items-center w-full\">\r\n                                                <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                                    {question.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}\r\n                                                </p>\r\n                                                <TextAreaWithAI\r\n                                                    value={statement.content}\r\n                                                    onChange={(e) => handleStatementChange(index, e.target.value, 'content')}\r\n                                                    placeholder=\"Nhập nội dung mệnh đề\"\r\n                                                />\r\n                                                {question.typeOfQuestion !== 'TLN' && (\r\n                                                    <div className=\"flex items-center gap-2\">\r\n                                                        <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                                            <input\r\n                                                                type=\"radio\"\r\n                                                                name={`statement-${index}`}\r\n                                                                checked={statement.isCorrect}\r\n                                                                onChange={() => handleStatementChange(index, true, 'isCorrect')}\r\n                                                                className=\"w-4 h-4 text-green-600 focus:ring-green-500\"\r\n                                                            />\r\n                                                            <span className=\"text-sm text-green-600 font-medium\">Đúng</span>\r\n                                                        </label>\r\n                                                        <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                                            <input\r\n                                                                type=\"radio\"\r\n                                                                name={`statement-${index}`}\r\n                                                                checked={!statement.isCorrect}\r\n                                                                onChange={() => handleStatementChange(index, false, 'isCorrect')}\r\n                                                                className=\"w-4 h-4 text-red-600 focus:ring-red-500\"\r\n                                                            />\r\n                                                            <span className=\"text-sm text-red-600 font-medium\">Sai</span>\r\n                                                        </label>\r\n                                                    </div>\r\n                                                )}\r\n                                            </div>\r\n                                            {(view === 'image' || statement.imageUrl) && (\r\n                                                <ImageDropZone\r\n                                                    imageUrl={statement.imageUrl}\r\n                                                    onImageDrop={(image) => handleStatementChange(index, image, 'imageUrl')}\r\n                                                    onImageRemove={() => handleStatementChange(index, '', 'imageUrl')}\r\n                                                />\r\n                                            )}\r\n\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            )}\r\n                            {question.typeOfQuestion === 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    <TextArea\r\n                                        value={question.correctAnswer}\r\n                                        onChange={(e) => handleQuestionChange(e, 'correctAnswer')}\r\n                                        placeholder=\"Nhập đáp án\"\r\n                                        label=\"Đáp án\"\r\n                                        Icon={CheckCircle}\r\n                                    />\r\n                                </div>\r\n                            )}\r\n                            {/* <TextArea\r\n                                        value={question.solution}\r\n                                        onChange={(e) => handleQuestionChange(e, 'solution')}\r\n                                        placeholder=\"Nhập lời giải\"\r\n                                        label=\"Lời giải\"\r\n                                        Icon={CheckCircle}\r\n                                    /> */}\r\n                            <TextArea\r\n                                value={question.solutionUrl || ''}\r\n                                onChange={(e) => handleQuestionChange(e, 'solutionUrl')}\r\n                                placeholder=\"Nhập link lời giải youtube\"\r\n                                label=\"Link lời giải\"\r\n                                Icon={Video}\r\n                            />\r\n                            {question.solutionUrl && (\r\n                                <YouTubePlayer url={question.solutionUrl} />\r\n                            )}\r\n                            <SolutionEditor\r\n                                solution={question.solution}\r\n                                onSolutionChange={handleSolutionQuestionChange}\r\n                                preview={false}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst AddQuestionView = () => {\r\n    const dispatch = useDispatch();\r\n    const { newQuestion } = useSelector((state) => state.questionsExam);\r\n    const { codes } = useSelector((state) => state.codes);\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n    const handleNewQuestionChange = (e, field) => {\r\n        const updatedQuestion = { ...newQuestion, [field]: e.target.value };\r\n        dispatch(setNewQuestion(updatedQuestion));\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (newQuestion?.class && newQuestion?.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.startsWith(newQuestion?.class) && code.code.length === 5)\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"].filter((code) => code.code.length === 5));\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, newQuestion?.class]);\r\n\r\n    const handleNewStatementChange = (index, value, field) => {\r\n        const updatedStatements = [...newQuestion.statements];\r\n        updatedStatements[index] = { ...updatedStatements[index], [field]: value };\r\n        const updatedQuestion = { ...newQuestion, statements: updatedStatements };\r\n        dispatch(setNewQuestion(updatedQuestion));\r\n    };\r\n\r\n    const handleNewSolutionQuestionChange = (value) => {\r\n        const updatedQuestion = { ...newQuestion, solution: value };\r\n        dispatch(setNewQuestion(updatedQuestion));\r\n    };\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3 w-full\">\r\n            <div className=\"flex flex-col gap-2\">\r\n                <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Phân loại</h3>\r\n                <div className=\"flex flex-row gap-2\">\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={newQuestion.typeOfQuestion}\r\n                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'typeOfQuestion')}\r\n                        options={[{ code: \"TN\", description: \"Trắc nghiệm\" }, { code: \"DS\", description: \"Đúng sai\" }, { code: \"TLN\", description: \"Trả lời ngắn\" }]}\r\n                        className=\"text-xs\"\r\n                    />\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={newQuestion.class}\r\n                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'class')}\r\n                        options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                    <SuggestInputBarAdmin\r\n                        selectedOption={newQuestion.chapter}\r\n                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'chapter')}\r\n                        options={optionChapter}\r\n                        className=\"text-xs\"\r\n                    />\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={newQuestion.difficulty}\r\n                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'difficulty')}\r\n                        options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n                <hr className=\" bg-gray-200\"></hr>\r\n                <div className=\"flex flex-col gap-2\">\r\n                    <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thông tin câu hỏi</h3>\r\n                    <div className=\"space-y-2\">\r\n                        <TextArea\r\n                            value={newQuestion.content}\r\n                            onChange={(e) => handleNewQuestionChange(e, 'content')}\r\n                            placeholder=\"Nhập nội dung câu hỏi\"\r\n                            label=\"Câu hỏi\"\r\n                        />\r\n                        <ImageDropZone\r\n                            imageUrl={newQuestion.imageUrl}\r\n                            onImageDrop={(image) => handleNewQuestionChange({ target: { value: image } }, 'imageUrl')}\r\n                            onImageRemove={() => handleNewQuestionChange({ target: { value: '' } }, 'imageUrl')}\r\n                        />\r\n                        {newQuestion.typeOfQuestion !== 'TLN' && (\r\n                            <div className=\"space-y-2\">\r\n                                {newQuestion.statements.map((statement, index) => (\r\n                                    <div key={index} className=\"flex flex-col gap-2 items-center w-full\">\r\n                                        <div className=\"flex flex-row gap-2 items-center w-full\">\r\n                                            <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                                {newQuestion.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}\r\n                                            </p>\r\n                                            <TextArea\r\n                                                value={statement.content}\r\n                                                onChange={(e) => handleNewStatementChange(index, e.target.value, 'content')}\r\n                                                placeholder=\"Nhập nội dung mệnh đề\"\r\n                                            />\r\n                                            <div className=\"flex items-center gap-2\">\r\n                                                <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                                    <input\r\n                                                        type=\"radio\"\r\n                                                        name={`statement-${index}`}\r\n                                                        checked={statement.isCorrect}\r\n                                                        onChange={() => handleNewStatementChange(index, true, 'isCorrect')}\r\n                                                        className=\"w-4 h-4 text-green-600 focus:ring-green-500\"\r\n                                                    />\r\n                                                    <span className=\"text-sm text-green-600 font-medium\">Đúng</span>\r\n                                                </label>\r\n                                                <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                                    <input\r\n                                                        type=\"radio\"\r\n                                                        name={`statement-${index}`}\r\n                                                        checked={!statement.isCorrect}\r\n                                                        onChange={() => handleNewStatementChange(index, false, 'isCorrect')}\r\n                                                        className=\"w-4 h-4 text-red-600 focus:ring-red-500\"\r\n                                                    />\r\n                                                    <span className=\"text-sm text-red-600 font-medium\">Sai</span>\r\n                                                </label>\r\n                                            </div>\r\n                                        </div>\r\n                                        <ImageDropZone\r\n                                            imageUrl={statement.imageUrl}\r\n                                            onImageDrop={(image) => handleNewStatementChange(index, image, 'imageUrl')}\r\n                                            onImageRemove={() => handleNewStatementChange(index, '', 'imageUrl')}\r\n                                        />\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        )}\r\n                        {newQuestion.typeOfQuestion === 'TLN' && (\r\n                            <div className=\"space-y-2\">\r\n                                <TextArea\r\n                                    value={newQuestion.correctAnswer}\r\n                                    onChange={(e) => handleNewQuestionChange(e, 'correctAnswer')}\r\n                                    placeholder=\"Nhập đáp án\"\r\n                                    label=\"Đáp án\"\r\n                                    Icon={CheckCircle}\r\n                                />\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n                <div className=\"space-y-2\">\r\n                    <TextArea\r\n                        value={newQuestion.solutionUrl}\r\n                        onChange={(e) => handleNewQuestionChange(e, 'solutionUrl')}\r\n                        placeholder=\"Nhập link lời giải youtube\"\r\n                        label=\"Link lời giải\"\r\n                        Icon={Video}\r\n                    />\r\n                    {newQuestion.solutionUrl && (\r\n                        <YouTubePlayer url={newQuestion.solutionUrl} />\r\n                    )}\r\n                    <SolutionEditor\r\n                        solution={newQuestion.solution}\r\n                        onSolutionChange={handleNewSolutionQuestionChange}\r\n                    />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nconst LeftContent = () => {\r\n    const [view, setView] = useState('questionDetail');\r\n    const { newQuestion } = useSelector((state) => state.questionsExam);\r\n    const dispatch = useDispatch();\r\n\r\n    const handleAddQuestion = () => {\r\n        if (!newQuestion.content.trim()) {\r\n            dispatch(setErrorMessage(\"Nội dung câu hỏi không được để trống!\"));\r\n            return;\r\n        }\r\n        if (!newQuestion.typeOfQuestion) {\r\n            dispatch(setErrorMessage(\"Loại câu hỏi không được để trống!\"));\r\n            return;\r\n        }\r\n        if (!newQuestion.class) {\r\n            dispatch(setErrorMessage(\"Lớp không được để trống!\"));\r\n            return;\r\n        }\r\n        if (newQuestion.typeOfQuestion !== 'TLN' && newQuestion.statements.filter(statement => statement.content.trim() !== \"\").length < 4) {\r\n            dispatch(setErrorMessage(\"Câu hỏi TN phải có ít nhất 4 đáp án!\"));\r\n            return;\r\n        }\r\n        if (newQuestion.typeOfQuestion === 'TLN' && !newQuestion.correctAnswer.trim()) {\r\n            dispatch(setErrorMessage(\"Đáp án không được để trống!\"));\r\n            return;\r\n        }\r\n        dispatch(addQuestion());\r\n        setView('questionDetail');\r\n    };\r\n\r\n\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_138px)] overflow-y-auto\">\r\n            {/* Compact Preview Header */}\r\n            <div className=\"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <h2 className=\"text-xs font-semibold text-gray-900\">Chi tiết câu hỏi</h2>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                    {\r\n                        view === 'questionDetail' ? (\r\n                            <button\r\n                                onClick={() => setView('addQuestion')}\r\n                                className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-sky-600 hover:bg-sky-700 text-white`}\r\n                            >\r\n                                Thêm câu hỏi\r\n                            </button>\r\n                        ) : (\r\n                            <>\r\n                                <button\r\n                                    onClick={handleAddQuestion}\r\n                                    className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-emerald-600 hover:bg-emerald-700 text-white`}\r\n                                >\r\n                                    Lưu\r\n                                </button>\r\n                                <button\r\n                                    onClick={() => setView('questionDetail')}\r\n                                    className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-red-600 hover:bg-red-700 text-white`}\r\n                                >\r\n                                    Hủy\r\n                                </button>\r\n                            </>\r\n                        )\r\n                    }\r\n                </div>\r\n            </div>\r\n            {view === 'questionDetail' && <DetailQuestionView />}\r\n            {view === 'addQuestion' && <AddQuestionView />}\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nexport default LeftContent;"], "mappings": ";;;;;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,QAAQ,MAAM,+BAA+B;AACpD,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,OAAOC,cAAc,MAAM,2CAA2C;AACtE,SAASC,WAAW,QAAQ,+CAA+C;AAC3E,SAASC,WAAW,EAAEC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,cAAc;AACjE,SAASC,YAAY,EAAEC,cAAc,EAAEC,WAAW,QAAQ,+CAA+C;AACzG,SAASC,YAAY,QAAQ,+CAA+C;AAC5E,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,aAAa,MAAM,kBAAkB;;AAE5C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAGC,IAAA,IAAmD;EAAAC,EAAA;EAAA,IAAlD;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAAN,IAAA;EACjE,MAAMO,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMmC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAACR,KAAK,IAAI,CAACA,KAAK,CAACS,IAAI,CAAC,CAAC,EAAE;MACzBC,KAAK,CAAC,qDAAqD,CAAC;MAC5D;IACJ;IAEA,IAAI;MACAH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,MAAM,GAAG,MAAMN,QAAQ,CAACf,eAAe,CAACU,KAAK,CAAC,CAAC,CAACY,MAAM,CAAC,CAAC;MAC9DL,UAAU,CAAC,KAAK,CAAC;MACjB,IAAII,MAAM,CAACE,IAAI,CAACC,UAAU,EAAE;QACxBb,QAAQ,CAAC;UAAEc,MAAM,EAAE;YAAEf,KAAK,EAAEW,MAAM,CAACE,IAAI,CAACG;UAAU;QAAE,CAAC,CAAC;MAC1D,CAAC,MAAM;QACHN,KAAK,CAAC,gCAAgC,CAAC;MAC3C;IACJ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACZV,UAAU,CAAC,KAAK,CAAC;MACjBW,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CP,KAAK,CAAC,sCAAsC,CAAC;IACjD;EACJ,CAAC;EAED,oBACIhB,OAAA;IAAKyB,SAAS,EAAC,WAAW;IAAAC,QAAA,GACrBjB,KAAK,iBACFT,OAAA;MAAKyB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAC9C1B,OAAA;QAAOyB,SAAS,EAAC,2DAA2D;QAAAC,QAAA,GACvEhB,IAAI,iBAAIV,OAAA,CAACU,IAAI;UAACe,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACpCrB,KAAK;MAAA;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACR9B,OAAA;QACI+B,OAAO,EAAEjB,WAAY;QACrBkB,QAAQ,EAAEpB,OAAO,IAAI,EAACN,KAAK,aAALA,KAAK,eAALA,KAAK,CAAES,IAAI,CAAC,CAAC,CAAC;QACpCU,SAAS,EAAC,kKAAkK;QAC5KQ,KAAK,EAAC,yFAA6C;QAAAP,QAAA,gBAEnD1B,OAAA,CAACT,QAAQ;UAACkC,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC/BlB,OAAO,GAAG,aAAa,GAAG,QAAQ;MAAA;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR,eACD9B,OAAA,CAAChB,QAAQ;MACLsB,KAAK,EAAEA,KAAM;MACbC,QAAQ,EAAEA,QAAS;MACnBC,WAAW,EAAEA;IAAY;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACzB,EAAA,CApDIF,cAAc;EAAA,QACCtB,WAAW;AAAA;AAAAqD,EAAA,GAD1B/B,cAAc;AAsDpB,MAAMgC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7B,MAAM;IAAEC,aAAa;IAAEC,UAAU;IAAEC;EAAK,CAAC,GAAG3D,WAAW,CAAE4D,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EACvF,MAAM;IAAEI;EAAM,CAAC,GAAG7D,WAAW,CAAE4D,KAAK,IAAKA,KAAK,CAACC,KAAK,CAAC;EACrD,MAAM9B,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM6D,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmE,QAAQ,EAAE3D,WAAW,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EAE9CD,SAAS,CAAC,MAAM;IACZ;;IAEA,IAAI4D,UAAU,IAAI,CAAAD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEU,MAAM,IAAG,CAAC,EAAE;MACzC,MAAMC,gBAAgB,GAAGX,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEY,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKb,UAAU,CAAC;MACtEnD,WAAW,CAAC6D,gBAAgB,CAAC;IACjC;EACJ,CAAC,EAAE,CAACV,UAAU,EAAED,aAAa,CAAC,CAAC;EAE/B,MAAMe,oBAAoB,GAAGA,CAACC,CAAC,EAAEC,KAAK,KAAK;IACvC,MAAMC,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAE,CAACQ,KAAK,GAAGD,CAAC,CAAChC,MAAM,CAACf;IAAM,CAAC;IAChEK,QAAQ,CAACnB,YAAY,CAAC+D,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAACC,KAAK,EAAEnD,KAAK,EAAEgD,KAAK,KAAK;IACnD,MAAMI,iBAAiB,GAAG,CAAC,GAAGZ,QAAQ,CAACa,UAAU,CAAC;IAClDD,iBAAiB,CAACD,KAAK,CAAC,GAAG;MAAE,GAAGC,iBAAiB,CAACD,KAAK,CAAC;MAAE,CAACH,KAAK,GAAGhD;IAAM,CAAC;IAC1E,MAAMiD,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAEa,UAAU,EAAED;IAAkB,CAAC;IACtE/C,QAAQ,CAACnB,YAAY,CAAC+D,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMK,4BAA4B,GAAItD,KAAK,IAAK;IAC5C,MAAMiD,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAEe,QAAQ,EAAEvD;IAAM,CAAC;IACxDK,QAAQ,CAACnB,YAAY,CAAC+D,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMO,kBAAkB,GAAGA,CAAA,KAAM;IAC7BnD,QAAQ,CAAChB,YAAY,CAAC,CAAC,CAAC;EAC5B,CAAC;EAEDjB,SAAS,CAAC,MAAM;IACZ,IAAIqF,KAAK,CAACC,OAAO,CAACvB,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MACjC,IAAIK,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEmB,KAAK,IAAI,CAAAnB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmB,KAAK,CAAClD,IAAI,CAAC,CAAC,MAAK,EAAE,EAAE;QAClD8B,gBAAgB,CACZJ,KAAK,CAAC,SAAS,CAAC,CAACyB,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,UAAU,CAACtB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmB,KAAK,CAAC,IAAIE,IAAI,CAACA,IAAI,CAACpB,MAAM,KAAK,CAAC,CACrG,CAAC;MACL,CAAC,MAAM;QACHF,gBAAgB,CAACJ,KAAK,CAAC,SAAS,CAAC,CAACyB,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACpB,MAAM,KAAK,CAAC,CAAC,CAAC;MAC/E;IACJ,CAAC,MAAM;MACHF,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAACJ,KAAK,EAAEK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmB,KAAK,CAAC,CAAC;EAE5B,oBACIjE,OAAA;IAAKyB,SAAS,EAAC,sBAAsB;IAAAC,QAAA,EAChCoB,QAAQ,iBACL9C,OAAA;MAAKyB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7B1B,OAAA;QAAKyB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChC1B,OAAA;UAAIyB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrE9B,OAAA;UAAKyB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAChC1B,OAAA,CAAClB,gBAAgB;YACbuF,cAAc,EAAEvB,QAAQ,CAACmB,KAAM;YAC/B1D,QAAQ,EAAG+D,MAAM,IAAKlB,oBAAoB,CAAC;cAAE/B,MAAM,EAAE;gBAAEf,KAAK,EAAEgE;cAAO;YAAE,CAAC,EAAE,OAAO,CAAE;YACnFC,OAAO,EAAER,KAAK,CAACC,OAAO,CAACvB,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;YAC7DhB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACF9B,OAAA,CAACjB,oBAAoB;YACjBsF,cAAc,EAAEvB,QAAQ,CAAC0B,OAAQ;YACjCjE,QAAQ,EAAG+D,MAAM,IAAKlB,oBAAoB,CAAC;cAAE/B,MAAM,EAAE;gBAAEf,KAAK,EAAEgE;cAAO;YAAE,CAAC,EAAE,SAAS,CAAE;YACrFC,OAAO,EAAE3B,aAAc;YACvBnB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACF9B,OAAA,CAAClB,gBAAgB;YACbuF,cAAc,EAAEvB,QAAQ,CAAC2B,UAAW;YACpClE,QAAQ,EAAG+D,MAAM,IAAKlB,oBAAoB,CAAC;cAAE/B,MAAM,EAAE;gBAAEf,KAAK,EAAEgE;cAAO;YAAE,CAAC,EAAE,YAAY,CAAE;YACxFC,OAAO,EAAER,KAAK,CAACC,OAAO,CAACvB,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;YACvEhB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN9B,OAAA;QAAIyB,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClC9B,OAAA;QAAKyB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChC1B,OAAA;UAAIyB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E9B,OAAA;UAAKyB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtB1B,OAAA,CAACG,cAAc;YACXG,KAAK,EAAEwC,QAAQ,CAAC4B,OAAQ;YACxBnE,QAAQ,EAAG8C,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,SAAS,CAAE;YACpD7C,WAAW,EAAC,yCAAuB;YACnCC,KAAK,EAAC;UAAS;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,EACD,CAACS,IAAI,KAAK,OAAO,IAAIO,QAAQ,CAAC6B,QAAQ,kBACnC3E,OAAA,CAACf,aAAa;YACV0F,QAAQ,EAAE7B,QAAQ,CAAC6B,QAAS;YAC5BC,WAAW,EAAGC,KAAK,IAAKzB,oBAAoB,CAAC;cAAE/B,MAAM,EAAE;gBAAEf,KAAK,EAAEuE;cAAM;YAAE,CAAC,EAAE,UAAU,CAAE;YACvFC,aAAa,EAAEA,CAAA,KAAM1B,oBAAoB,CAAC;cAAE/B,MAAM,EAAE;gBAAEf,KAAK,EAAE;cAAG;YAAE,CAAC,EAAE,UAAU;UAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CACJ,EACAgB,QAAQ,CAACiC,cAAc,KAAK,KAAK,iBAC9B/E,OAAA;YAAKyB,SAAS,EAAC,WAAW;YAAAC,QAAA,GACrBoB,QAAQ,CAACa,UAAU,CAACZ,MAAM,GAAG,CAAC,iBAC3B/C,OAAA;cACI+B,OAAO,EAAEA,CAAA,KAAM+B,kBAAkB,CAAC,CAAE;cACpCrC,SAAS,EAAC,+FAA+F;cAAAC,QAAA,gBAEzG1B,OAAA,CAACX,IAAI;gBAACoC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mCAChC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACX,EACAgB,QAAQ,CAACa,UAAU,CAACqB,GAAG,CAAC,CAACC,SAAS,EAAExB,KAAK,kBACtCzD,OAAA;cAAiByB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBAChE1B,OAAA;gBAAKyB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBACpD1B,OAAA;kBAAGyB,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAC7CoB,QAAQ,CAACiC,cAAc,KAAK,IAAI,GAAGrC,QAAQ,CAACe,KAAK,CAAC,GAAGd,QAAQ,CAACc,KAAK;gBAAC;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACJ9B,OAAA,CAACG,cAAc;kBACXG,KAAK,EAAE2E,SAAS,CAACP,OAAQ;kBACzBnE,QAAQ,EAAG8C,CAAC,IAAKG,qBAAqB,CAACC,KAAK,EAAEJ,CAAC,CAAChC,MAAM,CAACf,KAAK,EAAE,SAAS,CAAE;kBACzEE,WAAW,EAAC;gBAAuB;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,EACDgB,QAAQ,CAACiC,cAAc,KAAK,KAAK,iBAC9B/E,OAAA;kBAAKyB,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACpC1B,OAAA;oBAAOyB,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD1B,OAAA;sBACIkF,IAAI,EAAC,OAAO;sBACZC,IAAI,eAAAC,MAAA,CAAe3B,KAAK,CAAG;sBAC3B4B,OAAO,EAAEJ,SAAS,CAACK,SAAU;sBAC7B/E,QAAQ,EAAEA,CAAA,KAAMiD,qBAAqB,CAACC,KAAK,EAAE,IAAI,EAAE,WAAW,CAAE;sBAChEhC,SAAS,EAAC;oBAA6C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACF9B,OAAA;sBAAMyB,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACR9B,OAAA;oBAAOyB,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD1B,OAAA;sBACIkF,IAAI,EAAC,OAAO;sBACZC,IAAI,eAAAC,MAAA,CAAe3B,KAAK,CAAG;sBAC3B4B,OAAO,EAAE,CAACJ,SAAS,CAACK,SAAU;sBAC9B/E,QAAQ,EAAEA,CAAA,KAAMiD,qBAAqB,CAACC,KAAK,EAAE,KAAK,EAAE,WAAW,CAAE;sBACjEhC,SAAS,EAAC;oBAAyC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACF9B,OAAA;sBAAMyB,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,EACL,CAACS,IAAI,KAAK,OAAO,IAAI0C,SAAS,CAACN,QAAQ,kBACpC3E,OAAA,CAACf,aAAa;gBACV0F,QAAQ,EAAEM,SAAS,CAACN,QAAS;gBAC7BC,WAAW,EAAGC,KAAK,IAAKrB,qBAAqB,CAACC,KAAK,EAAEoB,KAAK,EAAE,UAAU,CAAE;gBACxEC,aAAa,EAAEA,CAAA,KAAMtB,qBAAqB,CAACC,KAAK,EAAE,EAAE,EAAE,UAAU;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CACJ;YAAA,GAzCK2B,KAAK;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2CV,CACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EACAgB,QAAQ,CAACiC,cAAc,KAAK,KAAK,iBAC9B/E,OAAA;YAAKyB,SAAS,EAAC,WAAW;YAAAC,QAAA,eACtB1B,OAAA,CAAChB,QAAQ;cACLsB,KAAK,EAAEwC,QAAQ,CAACyC,aAAc;cAC9BhF,QAAQ,EAAG8C,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,eAAe,CAAE;cAC1D7C,WAAW,EAAC,6BAAa;cACzBC,KAAK,EAAC,mBAAQ;cACdC,IAAI,EAAEtB;YAAY;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,eAQD9B,OAAA,CAAChB,QAAQ;YACLsB,KAAK,EAAEwC,QAAQ,CAAC0C,WAAW,IAAI,EAAG;YAClCjF,QAAQ,EAAG8C,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,aAAa,CAAE;YACxD7C,WAAW,EAAC,2CAA4B;YACxCC,KAAK,EAAC,yBAAe;YACrBC,IAAI,EAAEpB;UAAM;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EACDgB,QAAQ,CAAC0C,WAAW,iBACjBxF,OAAA,CAACF,aAAa;YAAC2F,GAAG,EAAE3C,QAAQ,CAAC0C;UAAY;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC9C,eACD9B,OAAA,CAACd,cAAc;YACX2E,QAAQ,EAAEf,QAAQ,CAACe,QAAS;YAC5B6B,gBAAgB,EAAE9B,4BAA6B;YAC/C+B,OAAO,EAAE;UAAM;YAAAhE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAM,GAAA,CAnMKD,kBAAkB;EAAA,QACwBvD,WAAW,EACrCA,WAAW,EACZC,WAAW;AAAA;AAAA+G,GAAA,GAH1BzD,kBAAkB;AAqMxB,MAAM0D,eAAe,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC1B,MAAMnF,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkH;EAAY,CAAC,GAAGnH,WAAW,CAAE4D,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EACnE,MAAM;IAAEI;EAAM,CAAC,GAAG7D,WAAW,CAAE4D,KAAK,IAAKA,KAAK,CAACC,KAAK,CAAC;EACrD,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAMqH,uBAAuB,GAAGA,CAAC3C,CAAC,EAAEC,KAAK,KAAK;IAC1C,MAAMC,eAAe,GAAG;MAAE,GAAGwC,WAAW;MAAE,CAACzC,KAAK,GAAGD,CAAC,CAAChC,MAAM,CAACf;IAAM,CAAC;IACnEK,QAAQ,CAAClB,cAAc,CAAC8D,eAAe,CAAC,CAAC;EAC7C,CAAC;EAED7E,SAAS,CAAC,MAAM;IACZ,IAAIqF,KAAK,CAACC,OAAO,CAACvB,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MACjC,IAAIsD,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAE9B,KAAK,IAAI,CAAA8B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE9B,KAAK,CAAClD,IAAI,CAAC,CAAC,MAAK,EAAE,EAAE;QACxD8B,gBAAgB,CACZJ,KAAK,CAAC,SAAS,CAAC,CAACyB,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,UAAU,CAAC2B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE9B,KAAK,CAAC,IAAIE,IAAI,CAACA,IAAI,CAACpB,MAAM,KAAK,CAAC,CACxG,CAAC;MACL,CAAC,MAAM;QACHF,gBAAgB,CAACJ,KAAK,CAAC,SAAS,CAAC,CAACyB,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACpB,MAAM,KAAK,CAAC,CAAC,CAAC;MAC/E;IACJ,CAAC,MAAM;MACHF,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAACJ,KAAK,EAAEsD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE9B,KAAK,CAAC,CAAC;EAE/B,MAAMgC,wBAAwB,GAAGA,CAACxC,KAAK,EAAEnD,KAAK,EAAEgD,KAAK,KAAK;IACtD,MAAMI,iBAAiB,GAAG,CAAC,GAAGqC,WAAW,CAACpC,UAAU,CAAC;IACrDD,iBAAiB,CAACD,KAAK,CAAC,GAAG;MAAE,GAAGC,iBAAiB,CAACD,KAAK,CAAC;MAAE,CAACH,KAAK,GAAGhD;IAAM,CAAC;IAC1E,MAAMiD,eAAe,GAAG;MAAE,GAAGwC,WAAW;MAAEpC,UAAU,EAAED;IAAkB,CAAC;IACzE/C,QAAQ,CAAClB,cAAc,CAAC8D,eAAe,CAAC,CAAC;EAC7C,CAAC;EAED,MAAM2C,+BAA+B,GAAI5F,KAAK,IAAK;IAC/C,MAAMiD,eAAe,GAAG;MAAE,GAAGwC,WAAW;MAAElC,QAAQ,EAAEvD;IAAM,CAAC;IAC3DK,QAAQ,CAAClB,cAAc,CAAC8D,eAAe,CAAC,CAAC;EAC7C,CAAC;EAED,oBACIvD,OAAA;IAAKyB,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eACjC1B,OAAA;MAAKyB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAChC1B,OAAA;QAAIyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrE9B,OAAA;QAAKyB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChC1B,OAAA,CAAClB,gBAAgB;UACbuF,cAAc,EAAE0B,WAAW,CAAChB,cAAe;UAC3CxE,QAAQ,EAAG+D,MAAM,IAAK0B,uBAAuB,CAAC;YAAE3E,MAAM,EAAE;cAAEf,KAAK,EAAEgE;YAAO;UAAE,CAAC,EAAE,gBAAgB,CAAE;UAC/FC,OAAO,EAAE,CAAC;YAAEJ,IAAI,EAAE,IAAI;YAAEgC,WAAW,EAAE;UAAc,CAAC,EAAE;YAAEhC,IAAI,EAAE,IAAI;YAAEgC,WAAW,EAAE;UAAW,CAAC,EAAE;YAAEhC,IAAI,EAAE,KAAK;YAAEgC,WAAW,EAAE;UAAe,CAAC,CAAE;UAC7I1E,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACF9B,OAAA,CAAClB,gBAAgB;UACbuF,cAAc,EAAE0B,WAAW,CAAC9B,KAAM;UAClC1D,QAAQ,EAAG+D,MAAM,IAAK0B,uBAAuB,CAAC;YAAE3E,MAAM,EAAE;cAAEf,KAAK,EAAEgE;YAAO;UAAE,CAAC,EAAE,OAAO,CAAE;UACtFC,OAAO,EAAER,KAAK,CAACC,OAAO,CAACvB,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;UAC7DhB,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACF9B,OAAA,CAACjB,oBAAoB;UACjBsF,cAAc,EAAE0B,WAAW,CAACvB,OAAQ;UACpCjE,QAAQ,EAAG+D,MAAM,IAAK0B,uBAAuB,CAAC;YAAE3E,MAAM,EAAE;cAAEf,KAAK,EAAEgE;YAAO;UAAE,CAAC,EAAE,SAAS,CAAE;UACxFC,OAAO,EAAE3B,aAAc;UACvBnB,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACF9B,OAAA,CAAClB,gBAAgB;UACbuF,cAAc,EAAE0B,WAAW,CAACtB,UAAW;UACvClE,QAAQ,EAAG+D,MAAM,IAAK0B,uBAAuB,CAAC;YAAE3E,MAAM,EAAE;cAAEf,KAAK,EAAEgE;YAAO;UAAE,CAAC,EAAE,YAAY,CAAE;UAC3FC,OAAO,EAAER,KAAK,CAACC,OAAO,CAACvB,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;UACvEhB,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN9B,OAAA;QAAIyB,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClC9B,OAAA;QAAKyB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChC1B,OAAA;UAAIyB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E9B,OAAA;UAAKyB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtB1B,OAAA,CAAChB,QAAQ;YACLsB,KAAK,EAAEyF,WAAW,CAACrB,OAAQ;YAC3BnE,QAAQ,EAAG8C,CAAC,IAAK2C,uBAAuB,CAAC3C,CAAC,EAAE,SAAS,CAAE;YACvD7C,WAAW,EAAC,yCAAuB;YACnCC,KAAK,EAAC;UAAS;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACF9B,OAAA,CAACf,aAAa;YACV0F,QAAQ,EAAEoB,WAAW,CAACpB,QAAS;YAC/BC,WAAW,EAAGC,KAAK,IAAKmB,uBAAuB,CAAC;cAAE3E,MAAM,EAAE;gBAAEf,KAAK,EAAEuE;cAAM;YAAE,CAAC,EAAE,UAAU,CAAE;YAC1FC,aAAa,EAAEA,CAAA,KAAMkB,uBAAuB,CAAC;cAAE3E,MAAM,EAAE;gBAAEf,KAAK,EAAE;cAAG;YAAE,CAAC,EAAE,UAAU;UAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,EACDiE,WAAW,CAAChB,cAAc,KAAK,KAAK,iBACjC/E,OAAA;YAAKyB,SAAS,EAAC,WAAW;YAAAC,QAAA,EACrBqE,WAAW,CAACpC,UAAU,CAACqB,GAAG,CAAC,CAACC,SAAS,EAAExB,KAAK,kBACzCzD,OAAA;cAAiByB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBAChE1B,OAAA;gBAAKyB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBACpD1B,OAAA;kBAAGyB,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAC7CqE,WAAW,CAAChB,cAAc,KAAK,IAAI,GAAGrC,QAAQ,CAACe,KAAK,CAAC,GAAGd,QAAQ,CAACc,KAAK;gBAAC;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC,eACJ9B,OAAA,CAAChB,QAAQ;kBACLsB,KAAK,EAAE2E,SAAS,CAACP,OAAQ;kBACzBnE,QAAQ,EAAG8C,CAAC,IAAK4C,wBAAwB,CAACxC,KAAK,EAAEJ,CAAC,CAAChC,MAAM,CAACf,KAAK,EAAE,SAAS,CAAE;kBAC5EE,WAAW,EAAC;gBAAuB;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACF9B,OAAA;kBAAKyB,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACpC1B,OAAA;oBAAOyB,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD1B,OAAA;sBACIkF,IAAI,EAAC,OAAO;sBACZC,IAAI,eAAAC,MAAA,CAAe3B,KAAK,CAAG;sBAC3B4B,OAAO,EAAEJ,SAAS,CAACK,SAAU;sBAC7B/E,QAAQ,EAAEA,CAAA,KAAM0F,wBAAwB,CAACxC,KAAK,EAAE,IAAI,EAAE,WAAW,CAAE;sBACnEhC,SAAS,EAAC;oBAA6C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACF9B,OAAA;sBAAMyB,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACR9B,OAAA;oBAAOyB,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD1B,OAAA;sBACIkF,IAAI,EAAC,OAAO;sBACZC,IAAI,eAAAC,MAAA,CAAe3B,KAAK,CAAG;sBAC3B4B,OAAO,EAAE,CAACJ,SAAS,CAACK,SAAU;sBAC9B/E,QAAQ,EAAEA,CAAA,KAAM0F,wBAAwB,CAACxC,KAAK,EAAE,KAAK,EAAE,WAAW,CAAE;sBACpEhC,SAAS,EAAC;oBAAyC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACF9B,OAAA;sBAAMyB,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN9B,OAAA,CAACf,aAAa;gBACV0F,QAAQ,EAAEM,SAAS,CAACN,QAAS;gBAC7BC,WAAW,EAAGC,KAAK,IAAKoB,wBAAwB,CAACxC,KAAK,EAAEoB,KAAK,EAAE,UAAU,CAAE;gBAC3EC,aAAa,EAAEA,CAAA,KAAMmB,wBAAwB,CAACxC,KAAK,EAAE,EAAE,EAAE,UAAU;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC;YAAA,GArCI2B,KAAK;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsCV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EACAiE,WAAW,CAAChB,cAAc,KAAK,KAAK,iBACjC/E,OAAA;YAAKyB,SAAS,EAAC,WAAW;YAAAC,QAAA,eACtB1B,OAAA,CAAChB,QAAQ;cACLsB,KAAK,EAAEyF,WAAW,CAACR,aAAc;cACjChF,QAAQ,EAAG8C,CAAC,IAAK2C,uBAAuB,CAAC3C,CAAC,EAAE,eAAe,CAAE;cAC7D7C,WAAW,EAAC,6BAAa;cACzBC,KAAK,EAAC,mBAAQ;cACdC,IAAI,EAAEtB;YAAY;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN9B,OAAA;QAAKyB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtB1B,OAAA,CAAChB,QAAQ;UACLsB,KAAK,EAAEyF,WAAW,CAACP,WAAY;UAC/BjF,QAAQ,EAAG8C,CAAC,IAAK2C,uBAAuB,CAAC3C,CAAC,EAAE,aAAa,CAAE;UAC3D7C,WAAW,EAAC,2CAA4B;UACxCC,KAAK,EAAC,yBAAe;UACrBC,IAAI,EAAEpB;QAAM;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,EACDiE,WAAW,CAACP,WAAW,iBACpBxF,OAAA,CAACF,aAAa;UAAC2F,GAAG,EAAEM,WAAW,CAACP;QAAY;UAAA7D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACjD,eACD9B,OAAA,CAACd,cAAc;UACX2E,QAAQ,EAAEkC,WAAW,CAAClC,QAAS;UAC/B6B,gBAAgB,EAAEQ;QAAgC;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAgE,GAAA,CAhKKD,eAAe;EAAA,QACAhH,WAAW,EACJD,WAAW,EACjBA,WAAW;AAAA;AAAAwH,GAAA,GAH3BP,eAAe;AAmKrB,MAAMQ,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtB,MAAM,CAAC/D,IAAI,EAAEgE,OAAO,CAAC,GAAG5H,QAAQ,CAAC,gBAAgB,CAAC;EAClD,MAAM;IAAEoH;EAAY,CAAC,GAAGnH,WAAW,CAAE4D,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EACnE,MAAM1B,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAE9B,MAAM2H,iBAAiB,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACT,WAAW,CAACrB,OAAO,CAAC3D,IAAI,CAAC,CAAC,EAAE;MAC7BJ,QAAQ,CAACd,eAAe,CAAC,uCAAuC,CAAC,CAAC;MAClE;IACJ;IACA,IAAI,CAACkG,WAAW,CAAChB,cAAc,EAAE;MAC7BpE,QAAQ,CAACd,eAAe,CAAC,mCAAmC,CAAC,CAAC;MAC9D;IACJ;IACA,IAAI,CAACkG,WAAW,CAAC9B,KAAK,EAAE;MACpBtD,QAAQ,CAACd,eAAe,CAAC,0BAA0B,CAAC,CAAC;MACrD;IACJ;IACA,IAAIkG,WAAW,CAAChB,cAAc,KAAK,KAAK,IAAIgB,WAAW,CAACpC,UAAU,CAACO,MAAM,CAACe,SAAS,IAAIA,SAAS,CAACP,OAAO,CAAC3D,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAACgC,MAAM,GAAG,CAAC,EAAE;MAChIpC,QAAQ,CAACd,eAAe,CAAC,sCAAsC,CAAC,CAAC;MACjE;IACJ;IACA,IAAIkG,WAAW,CAAChB,cAAc,KAAK,KAAK,IAAI,CAACgB,WAAW,CAACR,aAAa,CAACxE,IAAI,CAAC,CAAC,EAAE;MAC3EJ,QAAQ,CAACd,eAAe,CAAC,6BAA6B,CAAC,CAAC;MACxD;IACJ;IACAc,QAAQ,CAACjB,WAAW,CAAC,CAAC,CAAC;IACvB6G,OAAO,CAAC,gBAAgB,CAAC;EAC7B,CAAC;EAID,oBACIvG,OAAA;IAAKyB,SAAS,EAAC,uDAAuD;IAAAC,QAAA,gBAElE1B,OAAA;MAAKyB,SAAS,EAAC,oFAAoF;MAAAC,QAAA,gBAC/F1B,OAAA;QAAKyB,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpC1B,OAAA;UAAIyB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACN9B,OAAA;QAAKyB,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAEhCa,IAAI,KAAK,gBAAgB,gBACrBvC,OAAA;UACI+B,OAAO,EAAEA,CAAA,KAAMwE,OAAO,CAAC,aAAa,CAAE;UACtC9E,SAAS,+FAAgG;UAAAC,QAAA,EAC5G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAET9B,OAAA,CAAAE,SAAA;UAAAwB,QAAA,gBACI1B,OAAA;YACI+B,OAAO,EAAEyE,iBAAkB;YAC3B/E,SAAS,uGAAwG;YAAAC,QAAA,EACpH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9B,OAAA;YACI+B,OAAO,EAAEA,CAAA,KAAMwE,OAAO,CAAC,gBAAgB,CAAE;YACzC9E,SAAS,+FAAgG;YAAAC,QAAA,EAC5G;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACX;MACL;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACLS,IAAI,KAAK,gBAAgB,iBAAIvC,OAAA,CAACmC,kBAAkB;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACnDS,IAAI,KAAK,aAAa,iBAAIvC,OAAA,CAAC6F,eAAe;MAAAlE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7C,CAAC;AAEd,CAAC;AAAAwE,GAAA,CAvEKD,WAAW;EAAA,QAEWzH,WAAW,EAClBC,WAAW;AAAA;AAAA4H,GAAA,GAH1BJ,WAAW;AA0EjB,eAAeA,WAAW;AAAC,IAAAnE,EAAA,EAAA0D,GAAA,EAAAQ,GAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAxE,EAAA;AAAAwE,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}