import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import AdminSidebar from "src/components/sidebar/AdminSidebar";
import { fetchCodesByType } from "src/features/code/codeSlice";
import { setShowAddImagesModal } from "src/features/addExam/addExamSlice";
import { setStep, setExamData, postExam, nextStep, prevStep, setQuestions, setCreatedExam } from "src/features/addExam/addExamSlice";
import DropMenuBarAdmin from "src/components/dropMenu/OptionBarAdmin";
import SuggestInputBarAdmin from "src/components/input/suggestInputBarAdmin";
import ImageUpload from "src/components/image/UploadImage";
import UploadPdf from "src/components/UploadPdf";
import LatexRenderer from "src/components/latex/RenderLatex";
import LoadingSpinner from "src/components/loading/LoadingSpinner";
import { fetchImages } from "src/features/image/imageSlice";
import {
    ArrowLeft,
    Save,
    Eye,
    FileText,
    Plus,
    Trash2,
    Edit,
    CheckCircle,
    ChevronRight,
    ChevronLeft,
    Clock,
    Users,
    BookOpen,
    Image as ImageIcon,
    Upload
} from "lucide-react";
import { resetData } from "src/features/addExam/addExamSlice";

import LeftContent from "src/components/PageAddExam/LeftContent";
import RightContent from "src/components/PageAddExam/RightContent";
import AddImagesModal from "src/components/modal/AddImagesModal";
import useDebouncedEffect from "src/hooks/useDebouncedEffect";
import * as questionUntil from "src/utils/question/questionUtils";
import { setQuestionTNContent, setQuestionDSContent, setQuestionTLNContent } from "src/features/addExam/addExamSlice";
// Main Component
export const AddExamAdmin = () => {
    const { closeSidebar } = useSelector((state) => state.sidebar);
    const navigate = useNavigate();
    const { step, markDownExam } = useSelector((state) => state.addExam);
    const { showAddImagesModal, folder } = useSelector((state) => state.addExam);
    const dispatch = useDispatch();


    useEffect(() => {
        dispatch(fetchImages(folder));
        dispatch(fetchCodesByType(["chapter", "difficulty", "grade", "exam type", "year"]));
    }, [dispatch]);

    const { questionTNContent, questionDSContent, questionTLNContent, correctAnswerTN, correctAnswerDS, correctAnswerTLN, questions, examData } = useSelector((state) => state.addExam);

    useDebouncedEffect(() => {
        if (markDownExam.trim() !== "") {
            const contents = questionUntil.splitMarkdownToParts(markDownExam, dispatch);
            if (!contents) return;
            const { TN, DS, TLN } = contents;
            dispatch(setQuestionTNContent(TN));
            dispatch(setQuestionDSContent(DS));
            dispatch(setQuestionTLNContent(TLN));
        }
    }, [markDownExam, dispatch], 500);

    useDebouncedEffect(() => {
        const questionTN = questionUntil.splitContentTN(questionTNContent, correctAnswerTN, dispatch);
        const questionDS = questionUntil.splitContentDS(questionDSContent, correctAnswerDS, dispatch);
        const questionTLN = questionUntil.splitContentTLN(questionTLNContent, correctAnswerTLN, dispatch);
        if (questionTN && questionDS && questionTLN) {
            const newQuestions = [...questionTN, ...questionDS, ...questionTLN]
                .map((question, questionIndex) => {
                    return {
                        ...question,
                        questionData: {
                            ...question.questionData,
                            class: examData.class,
                        },
                        order: questionIndex
                    }
                })
            dispatch(setQuestions(newQuestions));
        }
    }, [questionTNContent, correctAnswerTN, questionDSContent, correctAnswerDS, questionTLNContent, correctAnswerTLN, examData.class], 500)

    return (
        <div className="bg-gray-50 flex flex-col">
            <AdminSidebar />
            {showAddImagesModal && <AddImagesModal showAddImagesModal={showAddImagesModal} folder={folder} setShowAddImagesModal={(value) => dispatch(setShowAddImagesModal(value))} />}
            <div className={`bg-gray-50 flex flex-col ${closeSidebar ? "ml-[104px]" : "ml-64"}`}>
                {/* Compact Header */}
                <div className="fixed top-0 z-10 w-full bg-white border-b border-gray-200 flex justify-between items-center px-3 py-2">
                    <div className="flex items-center gap-2">
                        <button
                            onClick={() => navigate('/admin/exam-management')}
                            className="p-1 hover:bg-gray-100 rounded transition-colors"
                        >
                            <ArrowLeft className="w-4 h-4 text-gray-600" />
                        </button>
                        <div>
                            <h1 className="text-sm font-bold text-gray-900">Tạo đề thi mới</h1>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <div className="text-xs text-gray-600 bg-gray-100 px-2 py-0.5 rounded-full">
                            Bước {step}/3
                        </div>
                    </div>
                </div>

                {/* Main Content - 2 Column Layout */}
                <div className="flex flex-1">
                    {/* Left Panel - Form */}
                    <div className="w-1/2 h-fit sticky top-[41px]  mt-[41px] border-r border-gray-200 bg-white">
                        <LeftContent />
                    </div>

                    {/* Right Panel - Preview */}
                    <div className="w-1/2 mt-[41px]">
                        <RightContent />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AddExamAdmin;