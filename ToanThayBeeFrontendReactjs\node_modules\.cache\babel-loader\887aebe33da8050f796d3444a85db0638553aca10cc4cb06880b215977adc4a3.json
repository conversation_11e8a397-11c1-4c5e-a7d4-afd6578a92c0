{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\user\\\\practice\\\\ScorePage.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport UserLayout from \"../../../layouts/UserLayout\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useParams } from \"react-router-dom\";\nimport { use, useEffect, useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { fetchQuestionAndAnswersByAttempt } from \"../../../features/scorePage/scorePageSlice\";\nimport { reExamination } from 'src/features/scorePage/scorePageSlice';\nimport LatexRenderer from \"../../../components/latex/RenderLatex\";\nimport { useMemo } from \"react\";\nimport LoadingSpinner from \"../../../components/loading/LoadingSpinner\";\nimport PdfViewer from \"../../../components/ViewPdf\";\nimport ReportButton from \"../../../components/button/ReportButton\";\nimport { setErrorMessage } from \"src/features/state/stateApiSlice\";\nimport { CheckCircle, XCircle, AlertCircle, BookOpen, Check, X, Eye, EyeOff, ArrowLeft, BarChart3, Target, Hash } from \"lucide-react\";\nimport MarkdownPreviewWithMath from \"src/components/latex/MarkDownPreview\";\nimport YouTubePlayer from \"src/components/YouTubePlayer\";\nimport LoadingText from \"src/components/loading/LoadingText\";\nimport ExamOverviewHeader from \"src/components/header/ExamOverviewHeader\";\nimport { formatTime } from \"src/utils/formatters\";\nimport { fetchAttemptById } from \"src/features/scorePage/scorePageSlice\";\nimport { calculateDurationText } from \"src/utils/formatters\";\nimport { getTotalScoreExam } from \"src/features/exam/examDetailSlice\";\nimport { setQuestion } from \"src/features/question/questionSlice\";\nimport AIChatWidget from \"src/components/ai/AiChatWidget\";\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\nimport { getDescription } from \"src/utils/codeUtils\";\nimport ActionButton from \"src/components/button/ActionButton\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getQuestionStatus = (answer, question) => {\n  if (!answer || answer.answerContent === null || answer.answerContent === undefined || answer.answerContent === \"\") {\n    return \"unanswered\"; // Chưa trả lời - màu vàng\n  }\n  return answer.result ? \"correct\" : \"incorrect\";\n};\n\n// Component hiển thị nút câu hỏi với màu sắc theo trạng thái\nconst QuestionButton = _ref => {\n  let {\n    question,\n    answer,\n    index,\n    onClick,\n    isSelected\n  } = _ref;\n  const status = getQuestionStatus(answer, question);\n  const getButtonStyle = () => {\n    const baseStyle = \"w-8 h-8 rounded-md font-medium text-xs transition-all duration-200 border \";\n    if (isSelected) {\n      switch (status) {\n        case \"correct\":\n          return baseStyle + \"bg-green-600 border-green-600 text-white\";\n        case \"incorrect\":\n          return baseStyle + \"bg-red-600 border-red-600 text-white\";\n        case \"unanswered\":\n          return baseStyle + \"bg-yellow-600 border-yellow-600 text-white\";\n        default:\n          return baseStyle + \"bg-gray-600 border-gray-600 text-white\";\n      }\n    } else {\n      switch (status) {\n        case \"correct\":\n          return baseStyle + \"bg-green-50 border-green-200 text-green-700 hover:bg-green-100\";\n        case \"incorrect\":\n          return baseStyle + \"bg-red-50 border-red-200 text-red-700 hover:bg-red-100\";\n        case \"unanswered\":\n          return baseStyle + \"bg-yellow-50 border-yellow-200 text-yellow-700 hover:bg-yellow-100\";\n        default:\n          return baseStyle + \"bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100\";\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: () => onClick(question, answer, index),\n    className: getButtonStyle(),\n    children: index + 1\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 9\n  }, this);\n};\n_c = QuestionButton;\nconst AnswerIndicator = _ref2 => {\n  let {\n    label,\n    selected,\n    correctValue,\n    isCorrect\n  } = _ref2;\n  console.log(\"AnswerIndicator\", label, selected, correctValue, isCorrect);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-4 h-4 rounded-full border-2 flex items-center justify-center transition-all duration-200\\n          \".concat(correctValue ? 'bg-green-600 border-green-600' : selected ? 'bg-red-600 border-red-600' : 'border-gray-300', \"\\n        \"),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-2 h-2 bg-white rounded-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"text-sm font-medium transition-colors duration-200\\n          \".concat(correctValue ? 'text-green-600' : selected ? 'text-red-600' : 'text-gray-800', \"\\n        \"),\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 13\n    }, this), selected ? isCorrect ? /*#__PURE__*/_jsxDEV(Check, {\n      size: 16,\n      className: \"text-green-600\",\n      title: \"B\\u1EA1n ch\\u1ECDn \\u0111\\xFAng\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 21\n    }, this) : /*#__PURE__*/_jsxDEV(X, {\n      size: 16,\n      className: \"text-red-600\",\n      title: \"B\\u1EA1n ch\\u1ECDn sai\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 21\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-4 h-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 9\n  }, this);\n};\n\n// Component hiển thị chi tiết câu hỏi và câu trả lời\n_c2 = AnswerIndicator;\nconst QuestionDetail = _ref3 => {\n  _s();\n  let {\n    question,\n    answer,\n    index\n  } = _ref3;\n  const [showSolution, setShowSolution] = useState(false);\n  if (!question) return null;\n  const status = getQuestionStatus(answer, question);\n  const getStatusDisplay = () => {\n    switch (status) {\n      case \"correct\":\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2 px-2 py-1 bg-green-100 border border-green-300 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            className: \"text-green-600\",\n            size: 14\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-green-800 font-semibold\",\n            children: \"\\u0110\\xFAng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 21\n        }, this);\n      case \"incorrect\":\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2 px-2 py-1 bg-red-100 border border-red-300 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(XCircle, {\n            className: \"text-red-600\",\n            size: 14\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-red-800 font-semibold\",\n            children: \"Sai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 21\n        }, this);\n      case \"unanswered\":\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2 px-2 py-1 bg-yellow-100 border border-yellow-300 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n            className: \"text-yellow-600\",\n            size: 14\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \" text-sm text-yellow-800 font-semibold\",\n            children: \"Ch\\u01B0a tr\\u1EA3 l\\u1EDDi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 21\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-md border border-gray-200 shadow-sm\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-sky-50 to-blue-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-800\",\n          children: [\"C\\xE2u \", index + 1, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500\",\n            children: [\"(ID: \", question.id, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 89\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ReportButton, {\n          questionId: question.id\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 17\n      }, this), getStatusDisplay()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-4 border border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-sm font-semibold text-gray-700 mb-2\",\n          children: \"N\\u1ED9i dung c\\xE2u h\\u1ECFi:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-800 leading-relaxed\",\n          children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n            text: question.content,\n            className: \"text-gray-800\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 21\n        }, this), question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center justify-center w-full mt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: question.imageUrl,\n            alt: \"Question\",\n            className: \"object-contain max-w-full h-auto rounded-lg shadow-sm border border-gray-200\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 17\n      }, this), question.typeOfQuestion === \"TN\" && question.statements && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-sm font-semibold text-gray-700\",\n          children: \"C\\xE1c l\\u1EF1a ch\\u1ECDn:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-3\",\n          children: [...question.statements].sort((a, b) => (a.order || 0) - (b.order || 0)).map((statement, index) => {\n            const isUserSelected = answer && answer.answerContent == statement.id;\n            const isCorrect = statement.isCorrect;\n            let statementStyle = \"bg-gray-50 border-gray-200\";\n            let iconStyle = \"bg-gray-400 text-white\";\n            if (isCorrect) {\n              statementStyle = \"bg-green-100 border-green-300\";\n              iconStyle = \"bg-green-600 text-white\";\n            } else if (isUserSelected && !isCorrect) {\n              statementStyle = \"bg-red-100 border-red-300\";\n              iconStyle = \"bg-red-600 text-white\";\n            } else if (isUserSelected) {\n              statementStyle = \"bg-sky-100 border-sky-300\";\n              iconStyle = \"bg-sky-600 text-white\";\n            }\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 rounded-lg border transition-all \".concat(statementStyle),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium \".concat(iconStyle),\n                  children: isCorrect ? /*#__PURE__*/_jsxDEV(Check, {\n                    size: 12\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 57\n                  }, this) : isUserSelected && !isCorrect ? /*#__PURE__*/_jsxDEV(X, {\n                    size: 12\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 57\n                  }, this) : String.fromCharCode(65 + index)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-800 leading-relaxed\",\n                    children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n                      text: statement.content,\n                      className: \"text-gray-800\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 57\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 53\n                  }, this), statement.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col items-center justify-center w-full mt-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: statement.imageUrl,\n                      alt: \"Statement \".concat(String.fromCharCode(65 + index)),\n                      className: \"object-contain max-w-full h-auto rounded-lg shadow-sm border border-gray-200\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 61\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 57\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 45\n              }, this)\n            }, statement.id || index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 41\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 21\n      }, this), question.typeOfQuestion === \"DS\" && question.statements && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-sm font-semibold text-gray-700\",\n          children: \"\\u0110\\xE1nh gi\\xE1 t\\u1EEBng ph\\xE1t bi\\u1EC3u:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-3\",\n          children: [...question.statements].sort((a, b) => (a.order || 0) - (b.order || 0)).map((statement, index) => {\n            const userAnswers = answer ? JSON.parse(answer.answerContent || \"[]\") : [];\n            const userAnswer = userAnswers.find(st => statement.id === st.statementId);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 rounded-lg border transition-all \",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex-shrink-0 w-6 h-6 rounded-full bg-sky-600 text-white flex items-center justify-center text-xs font-medium\",\n                    children: String.fromCharCode(97 + index)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-800 leading-relaxed\",\n                      children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n                        text: statement.content,\n                        className: \"text-gray-800\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 276,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 57\n                    }, this), statement.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-col items-center justify-center w-full mt-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: statement.imageUrl,\n                        alt: \"Statement \".concat(String.fromCharCode(97 + index)),\n                        className: \"object-contain max-w-full h-auto rounded-lg shadow-sm border border-gray-200\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 280,\n                        columnNumber: 65\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 279,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4 ml-9\",\n                  children: [/*#__PURE__*/_jsxDEV(AnswerIndicator, {\n                    label: \"\\u0110\\xFAng\",\n                    selected: (userAnswer === null || userAnswer === void 0 ? void 0 : userAnswer.answer) === true,\n                    isCorrect: (userAnswer === null || userAnswer === void 0 ? void 0 : userAnswer.answer) === statement.isCorrect,\n                    correctValue: statement.isCorrect === true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(AnswerIndicator, {\n                    label: \"Sai\",\n                    selected: (userAnswer === null || userAnswer === void 0 ? void 0 : userAnswer.answer) === false,\n                    isCorrect: (userAnswer === null || userAnswer === void 0 ? void 0 : userAnswer.answer) === statement.isCorrect,\n                    correctValue: statement.isCorrect === false\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 45\n              }, this)\n            }, statement.id || index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 41\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 21\n      }, this), question.typeOfQuestion === \"TLN\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-50 rounded-lg p-4 border border-blue-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-semibold text-blue-800 mb-2\",\n            children: \"C\\xE2u tr\\u1EA3 l\\u1EDDi c\\u1EE7a b\\u1EA1n:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm\",\n            children: answer && answer.answerContent ? /*#__PURE__*/_jsxDEV(LatexRenderer, {\n              text: answer.answerContent,\n              className: \"text-gray-800 font-medium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 37\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-500 italic\",\n              children: \"Ch\\u01B0a tr\\u1EA3 l\\u1EDDi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-green-50 rounded-lg p-4 border border-green-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-semibold text-green-800 mb-2\",\n            children: \"\\u0110\\xE1p \\xE1n \\u0111\\xFAng:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm\",\n            children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n              text: question.correctAnswer,\n              className: \"text-green-800 font-medium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true), question.solution && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-sky-50 rounded-lg p-4 border border-sky-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-semibold text-sky-800\",\n            children: \"L\\u1EDDi gi\\u1EA3i:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowSolution(!showSolution),\n            className: \"flex items-center gap-2 px-3 py-1 text-xs bg-sky-100 hover:bg-sky-200 text-sky-800 rounded-lg transition-colors\",\n            children: [showSolution ? /*#__PURE__*/_jsxDEV(EyeOff, {\n              size: 14\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 49\n            }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n              size: 14\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 72\n            }, this), showSolution ? \"Ẩn lời giải\" : \"Xem lời giải\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 25\n        }, this), showSolution && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-800\",\n            children: /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n              content: question.solution\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 33\n          }, this), question.solutionImageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: question.solutionImageUrl,\n              alt: \"L\\u1EDDi gi\\u1EA3i\",\n              className: \"max-h-80 object-contain rounded-md border border-gray-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 37\n          }, this), question.solutionUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center\",\n            children: /*#__PURE__*/_jsxDEV(YouTubePlayer, {\n              url: question.solutionUrl,\n              sizeClass: \"w-2/3 flex justify-center aspect-video\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 163,\n    columnNumber: 9\n  }, this);\n};\n_s(QuestionDetail, \"QTSP/1DQfu/5ZCNf20U2y6q64nA=\");\n_c3 = QuestionDetail;\nconst QuestionListPanel = _ref4 => {\n  let {\n    questionSections,\n    answers,\n    handleQuestionClick,\n    selectedQuestion\n  } = _ref4;\n  const legendItems = [{\n    color: 'green',\n    label: 'Đúng'\n  }, {\n    color: 'red',\n    label: 'Sai'\n  }, {\n    color: 'yellow',\n    label: 'Chưa trả lời'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-md border border-gray-200 shadow-sm p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-lg font-semibold text-gray-800 mb-4\",\n      children: \"Danh s\\xE1ch c\\xE2u h\\u1ECFi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap gap-2 mb-4 text-xs\",\n      children: legendItems.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-3 h-3 bg-\".concat(item.color, \"-100 border border-\").concat(item.color, \"-300 rounded\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-600\",\n          children: item.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 25\n        }, this)]\n      }, item.label, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-4\",\n      children: questionSections.map((section, sectionIndex) => {\n        var _section$questions;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-semibold text-gray-700 mb-1\",\n            children: section.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2\",\n            children: (_section$questions = section.questions) === null || _section$questions === void 0 ? void 0 : _section$questions.map((question, index) => {\n              const answer = answers === null || answers === void 0 ? void 0 : answers.find(a => a.questionId === question.id);\n              return /*#__PURE__*/_jsxDEV(QuestionButton, {\n                question: question,\n                answer: answer,\n                index: index,\n                onClick: handleQuestionClick,\n                isSelected: (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.id) === question.id\n              }, question.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 37\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 25\n          }, this)]\n        }, sectionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 21\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 390,\n    columnNumber: 9\n  }, this);\n};\n_c4 = QuestionListPanel;\nconst InfoQuestion = _ref5 => {\n  _s2();\n  let {\n    question\n  } = _ref5;\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-md border border-gray-200 shadow-sm p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-lg font-semibold text-gray-800 mb-4\",\n      children: \"Th\\xF4ng tin c\\xE2u h\\u1ECFi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 13\n    }, this), question ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 gap-y-2 text-sm text-gray-700\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"font-medium\",\n        children: \"ID:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: question === null || question === void 0 ? void 0 : question.id\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"font-medium\",\n        children: \"\\u0110\\u1ED9 kh\\xF3:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: getDescription(\"difficulty\", question === null || question === void 0 ? void 0 : question.difficulty, codes)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"font-medium\",\n        children: \"Ch\\u01B0\\u01A1ng:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: getDescription(\"chapter\", question === null || question === void 0 ? void 0 : question.chapter, codes)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"font-medium\",\n        children: \"L\\u1EDBp:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: getDescription(\"grade\", question === null || question === void 0 ? void 0 : question.class, codes)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"font-medium\",\n        children: \"Lo\\u1EA1i c\\xE2u h\\u1ECFi:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: (question === null || question === void 0 ? void 0 : question.typeOfQuestion) === \"TN\" ? \"Trắc nghiệm\" : (question === null || question === void 0 ? void 0 : question.typeOfQuestion) === \"TLN\" ? \"Tự luận ngắn\" : (question === null || question === void 0 ? void 0 : question.typeOfQuestion) === \"DS\" ? \"Đúng sai\" : \"Không rõ\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 439,\n      columnNumber: 25\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-sm text-gray-500\",\n      children: \"Ch\\u1ECDn m\\u1ED9t c\\xE2u h\\u1ECFi \\u0111\\u1EC3 xem th\\xF4ng tin\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 436,\n    columnNumber: 9\n  }, this);\n};\n_s2(InfoQuestion, \"ZhxuZH3bo5T9ytXOlPbgsJDSQwM=\", false, function () {\n  return [useSelector];\n});\n_c5 = InfoQuestion;\nconst BackButton = _ref6 => {\n  let {\n    onClick\n  } = _ref6;\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: onClick,\n    className: \"hover:underline w-fit flex items-center gap-1 text-sm text-gray-500 hover:text-gray-700 transition-colors\",\n    children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n      className: \"w-4 h-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"Quay l\\u1EA1i\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 474,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 469,\n    columnNumber: 5\n  }, this);\n};\n\n// Helper function to calculate statistics by grouping criteria\n_c6 = BackButton;\nconst calculateStatistics = function (questions, answers, codes) {\n  let groupBy = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'difficulty';\n  const stats = {};\n  questions.forEach((question, index) => {\n    const answer = answers === null || answers === void 0 ? void 0 : answers.find(a => a.questionId === question.id);\n    const status = getQuestionStatus(answer, question);\n\n    // Get group key based on groupBy parameter\n    let groupKey;\n    if (groupBy === 'difficulty') {\n      groupKey = question.difficulty || 'Khác';\n    } else if (groupBy === 'chapter') {\n      var _codes$chapter;\n      const chapterCode = codes === null || codes === void 0 ? void 0 : (_codes$chapter = codes.chapter) === null || _codes$chapter === void 0 ? void 0 : _codes$chapter.find(c => c.code === question.chapter);\n      groupKey = (chapterCode === null || chapterCode === void 0 ? void 0 : chapterCode.description) || 'Khác';\n    }\n    if (!stats[groupKey]) {\n      stats[groupKey] = {\n        total: 0,\n        correct: 0,\n        incorrect: 0,\n        unanswered: 0,\n        questions: []\n      };\n    }\n    stats[groupKey].total++;\n    stats[groupKey].questions.push({\n      questionNumber: index + 1,\n      status: status,\n      question: question\n    });\n    if (status === 'correct') {\n      stats[groupKey].correct++;\n    } else if (status === 'incorrect') {\n      stats[groupKey].incorrect++;\n    } else {\n      stats[groupKey].unanswered++;\n    }\n  });\n\n  // Calculate accuracy percentage\n  Object.keys(stats).forEach(key => {\n    const answered = stats[key].correct + stats[key].incorrect;\n    stats[key].accuracy = answered > 0 ? Math.round(stats[key].correct / answered * 100) : 0;\n  });\n  return stats;\n};\n\n// Statistics Section Component with Table and Tabs\nconst StatisticsSection = _ref7 => {\n  _s3();\n  let {\n    questions,\n    answers,\n    codes\n  } = _ref7;\n  const [activeTab, setActiveTab] = useState('difficulty');\n  const difficultyStats = calculateStatistics(questions, answers, codes, 'difficulty');\n  const chapterStats = calculateStatistics(questions, answers, codes, 'chapter');\n  const currentStats = activeTab === 'difficulty' ? difficultyStats : chapterStats;\n  const renderTable = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"overflow-x-auto rounded-md\",\n    children: /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"w-full border-collapse\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          className: \"flex p-3 border-b border-gray-300 bg-[#f6f8fa]\",\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"border border-gray-200 px-4 py-3 text-left font-semibold text-gray-700\",\n            children: activeTab === 'difficulty' ? 'Độ khó' : 'Chương'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"border border-gray-200 px-4 py-3 text-center font-semibold text-gray-700\",\n            children: \"T\\u1ED5ng s\\u1ED1 c\\xE2u\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"border border-gray-200 px-4 py-3 text-center font-semibold text-green-700\",\n            children: \"\\u0110\\xFAng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"border border-gray-200 px-4 py-3 text-center font-semibold text-red-700\",\n            children: \"Sai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"border border-gray-200 px-4 py-3 text-center font-semibold text-yellow-700\",\n            children: \"Ch\\u01B0a tr\\u1EA3 l\\u1EDDi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"border border-gray-200 px-4 py-3 text-center font-semibold text-sky-700\",\n            children: \"\\u0110\\u1ED9 ch\\xEDnh x\\xE1c\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"border border-gray-200 px-4 py-3 text-center font-semibold text-gray-700\",\n            children: \"Danh s\\xE1ch c\\xE2u h\\u1ECFi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: Object.entries(currentStats).map((_ref8, index) => {\n          let [key, data] = _ref8;\n          return /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: index % 2 === 0 ? 'bg-white' : 'bg-gray-50',\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"border border-gray-200 px-4 py-3 font-medium text-gray-800\",\n              children: key\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"border border-gray-200 px-4 py-3 text-center text-gray-700\",\n              children: data.total\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"border border-gray-200 px-4 py-3 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center justify-center w-8 h-8 bg-green-100 text-green-700 rounded-full text-sm font-semibold\",\n                children: data.correct\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"border border-gray-200 px-4 py-3 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center justify-center w-8 h-8 bg-red-100 text-red-700 rounded-full text-sm font-semibold\",\n                children: data.incorrect\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"border border-gray-200 px-4 py-3 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center justify-center w-8 h-8 bg-yellow-100 text-yellow-700 rounded-full text-sm font-semibold\",\n                children: data.unanswered\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"border border-gray-200 px-4 py-3 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center justify-center px-3 py-1 bg-sky-100 text-sky-700 rounded-full text-sm font-semibold\",\n                children: [data.accuracy, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"border border-gray-200 px-4 py-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-1 max-w-xs\",\n                children: data.questions.map(q => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"\\n                                                inline-flex items-center justify-center w-6 h-6 rounded text-xs font-medium\\n                                                \".concat(q.status === 'correct' ? 'bg-green-100 text-green-700 border border-green-200' : q.status === 'incorrect' ? 'bg-red-100 text-red-700 border border-red-200' : 'bg-yellow-100 text-yellow-700 border border-yellow-200', \"\\n                                            \"),\n                  children: q.questionNumber\n                }, q.questionNumber, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 29\n            }, this)]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 25\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 541,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 540,\n    columnNumber: 9\n  }, this);\n  if (questions.length === 0) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mb-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2 bg-white rounded-md shadow-sm overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setActiveTab('difficulty'),\n          title: \"Thống kê theo độ khó\",\n          shortTitle: \"\\u0110\\u1ED9 kh\\xF3\",\n          isActive: activeTab === 'difficulty',\n          icon: Target\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setActiveTab('chapter'),\n          title: \"Thống kê theo chương\",\n          shortTitle: \"Ch\\u01B0\\u01A1ng\",\n          isActive: activeTab === 'chapter',\n          icon: Hash\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 17\n      }, this), renderTable()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 623,\n    columnNumber: 9\n  }, this);\n};\n_s3(StatisticsSection, \"P/wlU+MIymRqaX9/ZdWp8epS8ho=\");\n_c7 = StatisticsSection;\nconst ScorePage = () => {\n  _s4();\n  var _attempt$score;\n  const {\n    attemptId\n  } = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    questions,\n    answers,\n    exam,\n    attempt,\n    loading\n  } = useSelector(state => state.scorePage);\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\n  const [selectedAnswer, setSelectedAnswer] = useState(null);\n  const [selectedIndex, setSelectedIndex] = useState(null);\n  const {\n    totalScore,\n    loadingTotalScore\n  } = useSelector(state => state.examDetail);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const [questionsTN, setQuestionsTN] = useState([]);\n  const [questionsDS, setQuestionsDS] = useState([]);\n  const [questionsTLN, setQuestionsTLN] = useState([]);\n  useEffect(() => {\n    if (questions.length > 0) {\n      setQuestionsTN(questions.filter(question => question.typeOfQuestion === \"TN\"));\n      setQuestionsDS(questions.filter(question => question.typeOfQuestion === \"DS\"));\n      setQuestionsTLN(questions.filter(question => question.typeOfQuestion === \"TLN\"));\n    }\n  }, [questions]);\n\n  // Hàm xử lý chấm lại bài\n  const handleReExamination = () => {\n    dispatch(reExamination(attemptId)).unwrap().then(() => {\n      dispatch(fetchQuestionAndAnswersByAttempt({\n        attemptId\n      }));\n    });\n  };\n\n  // Hàm xử lý khi click vào nút câu hỏi\n  const handleQuestionClick = (question, answer, index) => {\n    setSelectedQuestion(question);\n    setSelectedAnswer(answer);\n    setSelectedIndex(index);\n    // Set question to Redux state for AI component\n    dispatch(setQuestion(question));\n  };\n  useEffect(() => {\n    dispatch(fetchAttemptById(attemptId));\n    dispatch(fetchQuestionAndAnswersByAttempt({\n      attemptId\n    }));\n  }, [dispatch, attemptId]);\n  useEffect(() => {\n    if (exam !== null && exam !== void 0 && exam.id) {\n      dispatch(getTotalScoreExam(exam.id));\n    }\n  }, [exam, dispatch]);\n  useEffect(() => {\n    dispatch(fetchCodesByType([\"chapter\", \"grade\", \"difficulty\", \"question type\"]));\n  }, [dispatch]);\n  useEffect(() => {\n    if (exam && !exam.seeCorrectAnswer) {\n      dispatch(setErrorMessage(\"Không thể xem kết quả bài thi này!\"));\n      navigate(\"/practice/exam/\".concat(exam.id));\n    }\n  }, [exam, dispatch, navigate]);\n  const [questionSections, setQuestionSections] = useState([]);\n  useEffect(() => {\n    const sections = [];\n    if (questionsTN.length > 0) {\n      sections.push({\n        title: 'Phần I - Trắc nghiệm',\n        questions: questionsTN\n      });\n    }\n    if (questionsDS.length > 0) {\n      sections.push({\n        title: 'Phần II - Đúng sai',\n        questions: questionsDS\n      });\n    }\n    if (questionsTLN.length > 0) {\n      sections.push({\n        title: 'Phần III - Trả lời ngắn',\n        questions: questionsTLN\n      });\n    }\n    setQuestionSections(sections);\n  }, [questionsTN, questionsDS, questionsTLN]);\n  return /*#__PURE__*/_jsxDEV(UserLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 flex flex-col mb-9\",\n      children: [/*#__PURE__*/_jsxDEV(ExamOverviewHeader, {\n        star: false,\n        examStatus: false,\n        title: \"Điểm\",\n        exam: exam\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 737,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 p-4 bg-sky-50 rounded-md border border-sky-100 shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"text-xs font-semibold text-sky-800 uppercase mb-1 tracking-wide\",\n            children: \"\\u0110i\\u1EC3m c\\u1EE7a b\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(LoadingText, {\n            loading: loading || loadingTotalScore,\n            w: \"w-20\",\n            h: \"h-[30px]\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-4xl font-bold text-sky-600\",\n              children: [(_attempt$score = attempt === null || attempt === void 0 ? void 0 : attempt.score) !== null && _attempt$score !== void 0 ? _attempt$score : '--', \" / \", totalScore !== null && totalScore !== void 0 ? totalScore : 10]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 p-4 bg-indigo-50 rounded-md border border-indigo-100 shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"text-xs font-semibold text-indigo-800 uppercase mb-1 tracking-wide\",\n            children: \"Th\\u1EDDi gian l\\xE0m b\\xE0i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 758,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(LoadingText, {\n            loading: loading,\n            w: \"w-20\",\n            h: \"h-[30px]\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl font-semibold text-indigo-700\",\n              children: calculateDurationText(attempt === null || attempt === void 0 ? void 0 : attempt.startTime, attempt === null || attempt === void 0 ? void 0 : attempt.endTime)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 756,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 740,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(StatisticsSection, {\n        questions: questions,\n        answers: answers,\n        codes: codes\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 771,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"xl:w-1/5 lg:w-1/4 md:1/3 flex flex-col gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(QuestionListPanel, {\n            questionSections: questionSections,\n            answers: answers,\n            handleQuestionClick: handleQuestionClick,\n            selectedQuestion: selectedQuestion\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 781,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InfoQuestion, {\n            question: selectedQuestion\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 780,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 space-y-6\",\n          children: selectedQuestion ? /*#__PURE__*/_jsxDEV(QuestionDetail, {\n            question: selectedQuestion,\n            answer: selectedAnswer,\n            index: selectedIndex\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 793,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-md border border-gray-200 shadow-sm p-8 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-400 mb-4\",\n              children: /*#__PURE__*/_jsxDEV(BookOpen, {\n                size: 48,\n                className: \"mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 800,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-600 mb-2\",\n              children: \"Ch\\u1ECDn m\\u1ED9t c\\xE2u h\\u1ECFi \\u0111\\u1EC3 xem chi ti\\u1EBFt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 803,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500\",\n              children: \"Click v\\xE0o c\\xE1c n\\xFAt c\\xE2u h\\u1ECFi b\\xEAn tr\\xE1i \\u0111\\u1EC3 xem n\\u1ED9i dung v\\xE0 \\u0111\\xE1p \\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 778,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(AIChatWidget, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 736,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 735,\n    columnNumber: 9\n  }, this);\n};\n_s4(ScorePage, \"h7qIkjj1RNsrXVLwQrXp66sUI40=\", false, function () {\n  return [useParams, useDispatch, useNavigate, useSelector, useSelector, useSelector];\n});\n_c8 = ScorePage;\nexport default ScorePage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"QuestionButton\");\n$RefreshReg$(_c2, \"AnswerIndicator\");\n$RefreshReg$(_c3, \"QuestionDetail\");\n$RefreshReg$(_c4, \"QuestionListPanel\");\n$RefreshReg$(_c5, \"InfoQuestion\");\n$RefreshReg$(_c6, \"BackButton\");\n$RefreshReg$(_c7, \"StatisticsSection\");\n$RefreshReg$(_c8, \"ScorePage\");", "map": {"version": 3, "names": ["UserLayout", "useSelector", "useDispatch", "useParams", "use", "useEffect", "useState", "useNavigate", "fetchQuestionAndAnswersByAttempt", "reExamination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useMemo", "LoadingSpinner", "PdfViewer", "ReportButton", "setErrorMessage", "CheckCircle", "XCircle", "AlertCircle", "BookOpen", "Check", "X", "Eye", "Eye<PERSON>ff", "ArrowLeft", "BarChart3", "Target", "Hash", "MarkdownPreviewWithMath", "YouTubePlayer", "LoadingText", "ExamOverviewHeader", "formatTime", "fetchAttemptById", "calculateDurationText", "getTotalScoreExam", "setQuestion", "AIChatWidget", "fetchCodesByType", "getDescription", "ActionButton", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getQuestionStatus", "answer", "question", "answerContent", "undefined", "result", "QuestionButton", "_ref", "index", "onClick", "isSelected", "status", "getButtonStyle", "baseStyle", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AnswerIndicator", "_ref2", "label", "selected", "correctValue", "isCorrect", "console", "log", "concat", "size", "title", "_c2", "QuestionDetail", "_ref3", "_s", "showSolution", "setShowSolution", "getStatusDisplay", "id", "questionId", "text", "content", "imageUrl", "src", "alt", "typeOfQuestion", "statements", "sort", "a", "b", "order", "map", "statement", "isUserSelected", "statementStyle", "iconStyle", "String", "fromCharCode", "userAnswers", "JSON", "parse", "userAnswer", "find", "st", "statementId", "<PERSON><PERSON><PERSON><PERSON>", "solution", "solutionImageUrl", "solutionUrl", "url", "sizeClass", "_c3", "QuestionListPanel", "_ref4", "questionSections", "answers", "handleQuestionClick", "selectedQuestion", "legendItems", "color", "item", "section", "sectionIndex", "_section$questions", "questions", "_c4", "InfoQuestion", "_ref5", "_s2", "codes", "state", "difficulty", "chapter", "class", "_c5", "BackButton", "_ref6", "_c6", "calculateStatistics", "groupBy", "arguments", "length", "stats", "for<PERSON>ach", "groupKey", "_codes$chapter", "chapterCode", "c", "code", "description", "total", "correct", "incorrect", "unanswered", "push", "questionNumber", "Object", "keys", "key", "answered", "accuracy", "Math", "round", "StatisticsSection", "_ref7", "_s3", "activeTab", "setActiveTab", "difficultyStats", "chapterStats", "currentStats", "renderTable", "entries", "_ref8", "data", "q", "shortTitle", "isActive", "icon", "_c7", "ScorePage", "_s4", "_attempt$score", "attemptId", "dispatch", "navigate", "exam", "attempt", "loading", "scorePage", "setSelectedQuestion", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedAnswer", "selectedIndex", "setSelectedIndex", "totalScore", "loadingTotalScore", "examDetail", "questionsTN", "setQuestionsTN", "questionsDS", "setQuestionsDS", "questionsTLN", "setQuestionsTLN", "filter", "handleReExamination", "unwrap", "then", "seeCorrectAnswer", "setQuestionSections", "sections", "star", "examStatus", "w", "h", "score", "startTime", "endTime", "_c8", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/user/practice/ScorePage.jsx"], "sourcesContent": ["import UserLayout from \"../../../layouts/UserLayout\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { use, useEffect, useState } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { fetchQuestionAndAnswersByAttempt } from \"../../../features/scorePage/scorePageSlice\";\r\nimport { reExamination } from 'src/features/scorePage/scorePageSlice';\r\nimport LatexRenderer from \"../../../components/latex/RenderLatex\";\r\nimport { useMemo } from \"react\";\r\nimport LoadingSpinner from \"../../../components/loading/LoadingSpinner\";\r\nimport PdfViewer from \"../../../components/ViewPdf\";\r\nimport ReportButton from \"../../../components/button/ReportButton\";\r\nimport { setErrorMessage } from \"src/features/state/stateApiSlice\";\r\nimport {\r\n    CheckCircle,\r\n    XCircle,\r\n    AlertCircle,\r\n    BookOpen,\r\n    Check,\r\n    X,\r\n    Eye,\r\n    EyeOff,\r\n    ArrowLeft,\r\n    BarChart3,\r\n    Target,\r\n    Hash,\r\n} from \"lucide-react\";\r\nimport MarkdownPreviewWithMath from \"src/components/latex/MarkDownPreview\";\r\nimport YouTubePlayer from \"src/components/YouTubePlayer\";\r\nimport LoadingText from \"src/components/loading/LoadingText\";\r\nimport ExamOverviewHeader from \"src/components/header/ExamOverviewHeader\";\r\nimport { formatTime } from \"src/utils/formatters\";\r\nimport { fetchAttemptById } from \"src/features/scorePage/scorePageSlice\";\r\nimport { calculateDurationText } from \"src/utils/formatters\";\r\nimport { getTotalScoreExam } from \"src/features/exam/examDetailSlice\";\r\nimport { setQuestion } from \"src/features/question/questionSlice\";\r\nimport AIChatWidget from \"src/components/ai/AiChatWidget\";\r\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\r\nimport { getDescription } from \"src/utils/codeUtils\";\r\nimport ActionButton from \"src/components/button/ActionButton\";\r\n\r\nconst getQuestionStatus = (answer, question) => {\r\n    if (!answer || answer.answerContent === null || answer.answerContent === undefined || answer.answerContent === \"\") {\r\n        return \"unanswered\"; // Chưa trả lời - màu vàng\r\n    }\r\n    return answer.result ? \"correct\" : \"incorrect\";\r\n};\r\n\r\n\r\n// Component hiển thị nút câu hỏi với màu sắc theo trạng thái\r\nconst QuestionButton = ({ question, answer, index, onClick, isSelected }) => {\r\n    const status = getQuestionStatus(answer, question);\r\n\r\n    const getButtonStyle = () => {\r\n        const baseStyle = \"w-8 h-8 rounded-md font-medium text-xs transition-all duration-200 border \";\r\n\r\n        if (isSelected) {\r\n            switch (status) {\r\n                case \"correct\":\r\n                    return baseStyle + \"bg-green-600 border-green-600 text-white\";\r\n                case \"incorrect\":\r\n                    return baseStyle + \"bg-red-600 border-red-600 text-white\";\r\n                case \"unanswered\":\r\n                    return baseStyle + \"bg-yellow-600 border-yellow-600 text-white\";\r\n                default:\r\n                    return baseStyle + \"bg-gray-600 border-gray-600 text-white\";\r\n            }\r\n        } else {\r\n            switch (status) {\r\n                case \"correct\":\r\n                    return baseStyle + \"bg-green-50 border-green-200 text-green-700 hover:bg-green-100\";\r\n                case \"incorrect\":\r\n                    return baseStyle + \"bg-red-50 border-red-200 text-red-700 hover:bg-red-100\";\r\n                case \"unanswered\":\r\n                    return baseStyle + \"bg-yellow-50 border-yellow-200 text-yellow-700 hover:bg-yellow-100\";\r\n                default:\r\n                    return baseStyle + \"bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100\";\r\n            }\r\n        }\r\n    };\r\n\r\n    return (\r\n        <button\r\n            onClick={() => onClick(question, answer, index)}\r\n            className={getButtonStyle()}\r\n        >\r\n            {index + 1}\r\n        </button>\r\n    );\r\n};\r\n\r\nconst AnswerIndicator = ({ label, selected, correctValue, isCorrect }) => {\r\n    console.log(\"AnswerIndicator\", label, selected, correctValue, isCorrect);\r\n    return (\r\n        <div className=\"flex items-center gap-2\">\r\n            <div\r\n                className={`w-4 h-4 rounded-full border-2 flex items-center justify-center transition-all duration-200\r\n          ${correctValue ? 'bg-green-600 border-green-600' : selected ? 'bg-red-600 border-red-600' : 'border-gray-300'}\r\n        `}\r\n            >\r\n                <div className=\"w-2 h-2 bg-white rounded-full\" />\r\n            </div>\r\n\r\n            <span\r\n                className={`text-sm font-medium transition-colors duration-200\r\n          ${correctValue ? 'text-green-600' : selected ? 'text-red-600' : 'text-gray-800'}\r\n        `}\r\n            >\r\n                {label}\r\n            </span>\r\n\r\n            {selected ? (\r\n                isCorrect ? (\r\n                    <Check size={16} className=\"text-green-600\" title=\"Bạn chọn đúng\" />\r\n                ) : (\r\n                    <X size={16} className=\"text-red-600\" title=\"Bạn chọn sai\" />\r\n                )\r\n            ) : (\r\n                <div className=\"w-4 h-4\" />\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\n// Component hiển thị chi tiết câu hỏi và câu trả lời\r\nconst QuestionDetail = ({ question, answer, index }) => {\r\n    const [showSolution, setShowSolution] = useState(false);\r\n\r\n    if (!question) return null;\r\n    const status = getQuestionStatus(answer, question);\r\n\r\n    const getStatusDisplay = () => {\r\n        switch (status) {\r\n            case \"correct\":\r\n                return (\r\n                    <div className=\"flex items-center gap-2 px-2 py-1 bg-green-100 border border-green-300 rounded-lg\">\r\n                        <CheckCircle className=\"text-green-600\" size={14} />\r\n                        <span className=\"text-sm text-green-800 font-semibold\">Đúng</span>\r\n                    </div>\r\n                );\r\n            case \"incorrect\":\r\n                return (\r\n                    <div className=\"flex items-center gap-2 px-2 py-1 bg-red-100 border border-red-300 rounded-lg\">\r\n                        <XCircle className=\"text-red-600\" size={14} />\r\n                        <span className=\"text-sm text-red-800 font-semibold\">Sai</span>\r\n                    </div>\r\n                );\r\n            case \"unanswered\":\r\n                return (\r\n                    <div className=\"flex items-center gap-2 px-2 py-1 bg-yellow-100 border border-yellow-300 rounded-lg\">\r\n                        <AlertCircle className=\"text-yellow-600\" size={14} />\r\n                        <span className=\" text-sm text-yellow-800 font-semibold\">Chưa trả lời</span>\r\n                    </div>\r\n                );\r\n            default:\r\n                return null;\r\n        }\r\n    };\r\n\r\n\r\n\r\n    return (\r\n        <div className=\"bg-white rounded-md border border-gray-200 shadow-sm\">\r\n            {/* Header */}\r\n            <div className=\"flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-sky-50 to-blue-50\">\r\n                <div className=\"flex flex-row gap-2\">\r\n                    <h3 className=\"text-lg font-semibold text-gray-800\">Câu {index + 1} <span className=\"text-sm text-gray-500\">(ID: {question.id})</span></h3>\r\n                    <ReportButton questionId={question.id} />\r\n                </div>\r\n                {getStatusDisplay()}\r\n            </div>\r\n\r\n            {/* Question Content */}\r\n            <div className=\"p-4 space-y-4\">\r\n                <div className=\"bg-gray-50 rounded-lg p-4 border border-gray-200\">\r\n                    <h4 className=\"text-sm font-semibold text-gray-700 mb-2\">Nội dung câu hỏi:</h4>\r\n                    <div className=\"text-sm text-gray-800 leading-relaxed\">\r\n                        <LatexRenderer text={question.content} className=\"text-gray-800\" />\r\n                    </div>\r\n                    {question.imageUrl && (\r\n                        <div className=\"flex flex-col items-center justify-center w-full mt-4\">\r\n                            <img\r\n                                src={question.imageUrl}\r\n                                alt=\"Question\"\r\n                                className=\"object-contain max-w-full h-auto rounded-lg shadow-sm border border-gray-200\"\r\n                            />\r\n                        </div>\r\n                    )}\r\n                </div>\r\n\r\n                {/* Statements for TN questions */}\r\n                {question.typeOfQuestion === \"TN\" && question.statements && (\r\n                    <div className=\"space-y-3\">\r\n                        <h4 className=\"text-sm font-semibold text-gray-700\">Các lựa chọn:</h4>\r\n                        <div className=\"flex flex-col gap-3\">\r\n                            {[...question.statements]\r\n                                .sort((a, b) => (a.order || 0) - (b.order || 0))\r\n                                .map((statement, index) => {\r\n                                    const isUserSelected = answer && answer.answerContent == statement.id;\r\n                                    const isCorrect = statement.isCorrect;\r\n\r\n                                    let statementStyle = \"bg-gray-50 border-gray-200\";\r\n                                    let iconStyle = \"bg-gray-400 text-white\";\r\n\r\n                                    if (isCorrect) {\r\n                                        statementStyle = \"bg-green-100 border-green-300\";\r\n                                        iconStyle = \"bg-green-600 text-white\";\r\n                                    } else if (isUserSelected && !isCorrect) {\r\n                                        statementStyle = \"bg-red-100 border-red-300\";\r\n                                        iconStyle = \"bg-red-600 text-white\";\r\n                                    } else if (isUserSelected) {\r\n                                        statementStyle = \"bg-sky-100 border-sky-300\";\r\n                                        iconStyle = \"bg-sky-600 text-white\";\r\n                                    }\r\n\r\n                                    return (\r\n                                        <div\r\n                                            key={statement.id || index}\r\n                                            className={`p-3 rounded-lg border transition-all ${statementStyle}`}\r\n                                        >\r\n                                            <div className=\"flex items-start gap-3\">\r\n                                                <span className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${iconStyle}`}>\r\n                                                    {isCorrect ? (\r\n                                                        <Check size={12} />\r\n                                                    ) : isUserSelected && !isCorrect ? (\r\n                                                        <X size={12} />\r\n                                                    ) : (\r\n                                                        String.fromCharCode(65 + index)\r\n                                                    )}\r\n                                                </span>\r\n                                                <div className=\"flex-1\">\r\n                                                    <div className=\"text-sm text-gray-800 leading-relaxed\">\r\n                                                        <LatexRenderer text={statement.content} className=\"text-gray-800\" />\r\n                                                    </div>\r\n                                                    {statement.imageUrl && (\r\n                                                        <div className=\"flex flex-col items-center justify-center w-full mt-2\">\r\n                                                            <img\r\n                                                                src={statement.imageUrl}\r\n                                                                alt={`Statement ${String.fromCharCode(65 + index)}`}\r\n                                                                className=\"object-contain max-w-full h-auto rounded-lg shadow-sm border border-gray-200\"\r\n                                                            />\r\n                                                        </div>\r\n                                                    )}\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    );\r\n                                })}\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                {/* Statements for DS questions */}\r\n                {question.typeOfQuestion === \"DS\" && question.statements && (\r\n                    <div className=\"space-y-3\">\r\n                        <h4 className=\"text-sm font-semibold text-gray-700\">Đánh giá từng phát biểu:</h4>\r\n                        <div className=\"flex flex-col gap-3\">\r\n                            {[...question.statements]\r\n                                .sort((a, b) => (a.order || 0) - (b.order || 0))\r\n                                .map((statement, index) => {\r\n                                    const userAnswers = answer ? JSON.parse(answer.answerContent || \"[]\") : [];\r\n                                    const userAnswer = userAnswers.find((st) => statement.id === st.statementId)\r\n\r\n                                    return (\r\n                                        <div\r\n                                            key={statement.id || index}\r\n                                            className={`p-3 rounded-lg border transition-all `}\r\n                                        >\r\n                                            <div className=\"flex flex-col gap-3\">\r\n                                                <div className=\"flex items-start gap-3\">\r\n                                                    <span className=\"flex-shrink-0 w-6 h-6 rounded-full bg-sky-600 text-white flex items-center justify-center text-xs font-medium\">\r\n                                                        {String.fromCharCode(97 + index)}\r\n                                                    </span>\r\n                                                    <div className=\"flex-1\">\r\n                                                        <div className=\"text-sm text-gray-800 leading-relaxed\">\r\n                                                            <LatexRenderer text={statement.content} className=\"text-gray-800\" />\r\n                                                        </div>\r\n                                                        {statement.imageUrl && (\r\n                                                            <div className=\"flex flex-col items-center justify-center w-full mt-2\">\r\n                                                                <img\r\n                                                                    src={statement.imageUrl}\r\n                                                                    alt={`Statement ${String.fromCharCode(97 + index)}`}\r\n                                                                    className=\"object-contain max-w-full h-auto rounded-lg shadow-sm border border-gray-200\"\r\n                                                                />\r\n                                                            </div>\r\n                                                        )}\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                {/* True/False options display */}\r\n                                                <div className=\"flex items-center gap-4 ml-9\">\r\n                                                    <AnswerIndicator\r\n                                                        label=\"Đúng\"\r\n                                                        selected={userAnswer?.answer === true}\r\n                                                        isCorrect={userAnswer?.answer === statement.isCorrect}\r\n                                                        correctValue={statement.isCorrect === true}\r\n                                                    />\r\n                                                    <AnswerIndicator\r\n                                                        label=\"Sai\"\r\n                                                        selected={userAnswer?.answer === false}\r\n                                                        isCorrect={userAnswer?.answer === statement.isCorrect}\r\n                                                        correctValue={statement.isCorrect === false}\r\n                                                    />\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    );\r\n                                })}\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                {/* User Answer for TLN questions only */}\r\n                {question.typeOfQuestion === \"TLN\" && (\r\n                    <>\r\n                        <div className=\"bg-blue-50 rounded-lg p-4 border border-blue-200\">\r\n                            <h4 className=\"text-sm font-semibold text-blue-800 mb-2\">Câu trả lời của bạn:</h4>\r\n                            <div className=\"text-sm\">\r\n                                {answer && answer.answerContent ? (\r\n                                    <LatexRenderer text={answer.answerContent} className=\"text-gray-800 font-medium\" />\r\n                                ) : (\r\n                                    <span className=\"text-gray-500 italic\">Chưa trả lời</span>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"bg-green-50 rounded-lg p-4 border border-green-200\">\r\n                            <h4 className=\"text-sm font-semibold text-green-800 mb-2\">Đáp án đúng:</h4>\r\n                            <div className=\"text-sm\">\r\n                                <LatexRenderer text={question.correctAnswer} className=\"text-green-800 font-medium\" />\r\n                            </div>\r\n                        </div>\r\n                    </>\r\n                )}\r\n\r\n                {/* Solution */}\r\n                {question.solution && (\r\n                    <div className=\"bg-sky-50 rounded-lg p-4 border border-sky-200\">\r\n                        <div className=\"flex items-center justify-between mb-3\">\r\n                            <h4 className=\"text-sm font-semibold text-sky-800\">Lời giải:</h4>\r\n                            <button\r\n                                onClick={() => setShowSolution(!showSolution)}\r\n                                className=\"flex items-center gap-2 px-3 py-1 text-xs bg-sky-100 hover:bg-sky-200 text-sky-800 rounded-lg transition-colors\"\r\n                            >\r\n                                {showSolution ? <EyeOff size={14} /> : <Eye size={14} />}\r\n                                {showSolution ? \"Ẩn lời giải\" : \"Xem lời giải\"}\r\n                            </button>\r\n                        </div>\r\n\r\n                        {showSolution && (\r\n                            <div className=\"space-y-3\">\r\n                                <div className=\"text-sm text-gray-800\">\r\n                                    <MarkdownPreviewWithMath content={question.solution} />\r\n                                </div>\r\n                                {question.solutionImageUrl && (\r\n                                    <div className=\"flex justify-center\">\r\n                                        <img\r\n                                            src={question.solutionImageUrl}\r\n                                            alt=\"Lời giải\"\r\n                                            className=\"max-h-80 object-contain rounded-md border border-gray-200\"\r\n                                        />\r\n                                    </div>\r\n                                )}\r\n                                {question.solutionUrl && (\r\n                                    <div className=\"flex justify-center items-center\">\r\n                                        <YouTubePlayer url={question.solutionUrl} sizeClass=\"w-2/3 flex justify-center aspect-video\" />\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                )}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nconst QuestionListPanel = ({\r\n    questionSections,\r\n    answers,\r\n    handleQuestionClick,\r\n    selectedQuestion,\r\n}) => {\r\n    const legendItems = [\r\n        { color: 'green', label: 'Đúng' },\r\n        { color: 'red', label: 'Sai' },\r\n        { color: 'yellow', label: 'Chưa trả lời' },\r\n    ];\r\n    return (\r\n        <div className=\"bg-white rounded-md border border-gray-200 shadow-sm p-4\">\r\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Danh sách câu hỏi</h3>\r\n\r\n            {/* Legend */}\r\n            <div className=\"flex flex-wrap gap-2 mb-4 text-xs\">\r\n                {legendItems.map((item) => (\r\n                    <div className=\"flex items-center gap-1\" key={item.label}>\r\n                        <div className={`w-3 h-3 bg-${item.color}-100 border border-${item.color}-300 rounded`}></div>\r\n                        <span className=\"text-gray-600\">{item.label}</span>\r\n                    </div>\r\n                ))}\r\n            </div>\r\n\r\n            {/* Question Buttons */}\r\n            <div className=\"flex flex-col gap-4\">\r\n                {questionSections.map((section, sectionIndex) => (\r\n                    <div key={sectionIndex}>\r\n                        <h4 className=\"text-sm font-semibold text-gray-700 mb-1\">{section.title}</h4>\r\n                        <div className=\"flex flex-wrap gap-2\">\r\n                            {section.questions?.map((question, index) => {\r\n                                const answer = answers?.find((a) => a.questionId === question.id);\r\n                                return (\r\n                                    <QuestionButton\r\n                                        key={question.id}\r\n                                        question={question}\r\n                                        answer={answer}\r\n                                        index={index}\r\n                                        onClick={handleQuestionClick}\r\n                                        isSelected={selectedQuestion?.id === question.id}\r\n                                    />\r\n                                );\r\n                            })}\r\n                        </div>\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nconst InfoQuestion = ({ question }) => {\r\n    const { codes } = useSelector((state) => state.codes);\r\n\r\n\r\n\r\n    return (\r\n        <div className=\"bg-white rounded-md border border-gray-200 shadow-sm p-4\">\r\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Thông tin câu hỏi</h3>\r\n\r\n            {question ? <div className=\"grid grid-cols-2 gap-y-2 text-sm text-gray-700\">\r\n                <div className=\"font-medium\">ID:</div>\r\n                <div>{question?.id}</div>\r\n\r\n                <div className=\"font-medium\">Độ khó:</div>\r\n                <div>{getDescription(\"difficulty\", question?.difficulty, codes)}</div>\r\n\r\n                <div className=\"font-medium\">Chương:</div>\r\n                <div>{getDescription(\"chapter\", question?.chapter, codes)}</div>\r\n\r\n                <div className=\"font-medium\">Lớp:</div>\r\n                <div>{getDescription(\"grade\", question?.class, codes)}</div>\r\n\r\n                <div className=\"font-medium\">Loại câu hỏi:</div>\r\n                <div>\r\n                    {question?.typeOfQuestion === \"TN\"\r\n                        ? \"Trắc nghiệm\"\r\n                        : question?.typeOfQuestion === \"TLN\"\r\n                            ? \"Tự luận ngắn\"\r\n                            : question?.typeOfQuestion === \"DS\"\r\n                                ? \"Đúng sai\"\r\n                                : \"Không rõ\"}\r\n                </div>\r\n            </div> : (\r\n                <div className=\"text-sm text-gray-500\">Chọn một câu hỏi để xem thông tin</div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\nconst BackButton = ({ onClick }) => (\r\n    <button\r\n        onClick={onClick}\r\n        className=\"hover:underline w-fit flex items-center gap-1 text-sm text-gray-500 hover:text-gray-700 transition-colors\"\r\n    >\r\n        <ArrowLeft className=\"w-4 h-4\" />\r\n        <span>Quay lại</span>\r\n    </button>\r\n);\r\n\r\n// Helper function to calculate statistics by grouping criteria\r\nconst calculateStatistics = (questions, answers, codes, groupBy = 'difficulty') => {\r\n    const stats = {};\r\n\r\n    questions.forEach((question, index) => {\r\n        const answer = answers?.find((a) => a.questionId === question.id);\r\n        const status = getQuestionStatus(answer, question);\r\n\r\n        // Get group key based on groupBy parameter\r\n        let groupKey;\r\n        if (groupBy === 'difficulty') {\r\n            groupKey = question.difficulty || 'Khác';\r\n        } else if (groupBy === 'chapter') {\r\n            const chapterCode = codes?.chapter?.find(c => c.code === question.chapter);\r\n            groupKey = chapterCode?.description || 'Khác';\r\n        }\r\n\r\n        if (!stats[groupKey]) {\r\n            stats[groupKey] = {\r\n                total: 0,\r\n                correct: 0,\r\n                incorrect: 0,\r\n                unanswered: 0,\r\n                questions: []\r\n            };\r\n        }\r\n\r\n        stats[groupKey].total++;\r\n        stats[groupKey].questions.push({\r\n            questionNumber: index + 1,\r\n            status: status,\r\n            question: question\r\n        });\r\n\r\n        if (status === 'correct') {\r\n            stats[groupKey].correct++;\r\n        } else if (status === 'incorrect') {\r\n            stats[groupKey].incorrect++;\r\n        } else {\r\n            stats[groupKey].unanswered++;\r\n        }\r\n    });\r\n\r\n    // Calculate accuracy percentage\r\n    Object.keys(stats).forEach(key => {\r\n        const answered = stats[key].correct + stats[key].incorrect;\r\n        stats[key].accuracy = answered > 0 ? Math.round((stats[key].correct / answered) * 100) : 0;\r\n    });\r\n\r\n    return stats;\r\n};\r\n\r\n// Statistics Section Component with Table and Tabs\r\nconst StatisticsSection = ({ questions, answers, codes }) => {\r\n    const [activeTab, setActiveTab] = useState('difficulty');\r\n\r\n    const difficultyStats = calculateStatistics(questions, answers, codes, 'difficulty');\r\n    const chapterStats = calculateStatistics(questions, answers, codes, 'chapter');\r\n\r\n    const currentStats = activeTab === 'difficulty' ? difficultyStats : chapterStats;\r\n\r\n    const renderTable = () => (\r\n        <div className=\"overflow-x-auto rounded-md\">\r\n            <table className=\"w-full border-collapse\">\r\n                <thead>\r\n                    <tr className=\"flex p-3 border-b border-gray-300 bg-[#f6f8fa]\">\r\n                        <th className=\"border border-gray-200 px-4 py-3 text-left font-semibold text-gray-700\">\r\n                            {activeTab === 'difficulty' ? 'Độ khó' : 'Chương'}\r\n                        </th>\r\n                        <th className=\"border border-gray-200 px-4 py-3 text-center font-semibold text-gray-700\">\r\n                            Tổng số câu\r\n                        </th>\r\n                        <th className=\"border border-gray-200 px-4 py-3 text-center font-semibold text-green-700\">\r\n                            Đúng\r\n                        </th>\r\n                        <th className=\"border border-gray-200 px-4 py-3 text-center font-semibold text-red-700\">\r\n                            Sai\r\n                        </th>\r\n                        <th className=\"border border-gray-200 px-4 py-3 text-center font-semibold text-yellow-700\">\r\n                            Chưa trả lời\r\n                        </th>\r\n                        <th className=\"border border-gray-200 px-4 py-3 text-center font-semibold text-sky-700\">\r\n                            Độ chính xác\r\n                        </th>\r\n                        <th className=\"border border-gray-200 px-4 py-3 text-center font-semibold text-gray-700\">\r\n                            Danh sách câu hỏi\r\n                        </th>\r\n                    </tr>\r\n                </thead>\r\n                <tbody>\r\n                    {Object.entries(currentStats).map(([key, data], index) => (\r\n                        <tr key={key} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>\r\n                            <td className=\"border border-gray-200 px-4 py-3 font-medium text-gray-800\">\r\n                                {key}\r\n                            </td>\r\n                            <td className=\"border border-gray-200 px-4 py-3 text-center text-gray-700\">\r\n                                {data.total}\r\n                            </td>\r\n                            <td className=\"border border-gray-200 px-4 py-3 text-center\">\r\n                                <span className=\"inline-flex items-center justify-center w-8 h-8 bg-green-100 text-green-700 rounded-full text-sm font-semibold\">\r\n                                    {data.correct}\r\n                                </span>\r\n                            </td>\r\n                            <td className=\"border border-gray-200 px-4 py-3 text-center\">\r\n                                <span className=\"inline-flex items-center justify-center w-8 h-8 bg-red-100 text-red-700 rounded-full text-sm font-semibold\">\r\n                                    {data.incorrect}\r\n                                </span>\r\n                            </td>\r\n                            <td className=\"border border-gray-200 px-4 py-3 text-center\">\r\n                                <span className=\"inline-flex items-center justify-center w-8 h-8 bg-yellow-100 text-yellow-700 rounded-full text-sm font-semibold\">\r\n                                    {data.unanswered}\r\n                                </span>\r\n                            </td>\r\n                            <td className=\"border border-gray-200 px-4 py-3 text-center\">\r\n                                <span className=\"inline-flex items-center justify-center px-3 py-1 bg-sky-100 text-sky-700 rounded-full text-sm font-semibold\">\r\n                                    {data.accuracy}%\r\n                                </span>\r\n                            </td>\r\n                            <td className=\"border border-gray-200 px-4 py-3\">\r\n                                <div className=\"flex flex-wrap gap-1 max-w-xs\">\r\n                                    {data.questions.map((q) => (\r\n                                        <span\r\n                                            key={q.questionNumber}\r\n                                            className={`\r\n                                                inline-flex items-center justify-center w-6 h-6 rounded text-xs font-medium\r\n                                                ${q.status === 'correct' ? 'bg-green-100 text-green-700 border border-green-200' :\r\n                                                    q.status === 'incorrect' ? 'bg-red-100 text-red-700 border border-red-200' :\r\n                                                        'bg-yellow-100 text-yellow-700 border border-yellow-200'}\r\n                                            `}\r\n                                        >\r\n                                            {q.questionNumber}\r\n                                        </span>\r\n                                    ))}\r\n                                </div>\r\n                            </td>\r\n                        </tr>\r\n                    ))}\r\n                </tbody>\r\n            </table>\r\n        </div>\r\n    );\r\n\r\n    if (questions.length === 0) return null;\r\n\r\n    return (\r\n        <div className=\"mb-4\">\r\n            {/* Tab Navigation */}\r\n            <div className=\"flex flex-col gap-2 bg-white rounded-md shadow-sm overflow-hidden\">\r\n                <div className=\"flex flex-row gap-2\">\r\n                    <ActionButton\r\n                        onClick={() => setActiveTab('difficulty')}\r\n                        title={\"Thống kê theo độ khó\"}\r\n                        shortTitle=\"Độ khó\"\r\n                        isActive={activeTab === 'difficulty'}\r\n                        icon={Target}\r\n                    />\r\n                    <ActionButton\r\n                        onClick={() => setActiveTab('chapter')}\r\n                        title={\"Thống kê theo chương\"}\r\n                        shortTitle=\"Chương\"\r\n                        isActive={activeTab === 'chapter'}\r\n                        icon={Hash}\r\n                    />\r\n                </div>\r\n\r\n                {/* Table Content */}\r\n                {renderTable()}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nconst ScorePage = () => {\r\n    const { attemptId } = useParams();\r\n    const dispatch = useDispatch();\r\n    const navigate = useNavigate();\r\n    const { questions, answers, exam, attempt, loading } = useSelector((state) => state.scorePage);\r\n    const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n    const [selectedAnswer, setSelectedAnswer] = useState(null);\r\n    const [selectedIndex, setSelectedIndex] = useState(null);\r\n    const { totalScore, loadingTotalScore } = useSelector((state) => state.examDetail);\r\n    const { codes } = useSelector((state) => state.codes);\r\n\r\n    const [questionsTN, setQuestionsTN] = useState([]);\r\n    const [questionsDS, setQuestionsDS] = useState([]);\r\n    const [questionsTLN, setQuestionsTLN] = useState([]);\r\n\r\n    useEffect(() => {\r\n        if (questions.length > 0) {\r\n            setQuestionsTN(questions.filter((question) => question.typeOfQuestion === \"TN\"));\r\n            setQuestionsDS(questions.filter((question) => question.typeOfQuestion === \"DS\"));\r\n            setQuestionsTLN(questions.filter((question) => question.typeOfQuestion === \"TLN\"));\r\n        }\r\n    }, [questions]);\r\n\r\n    // Hàm xử lý chấm lại bài\r\n    const handleReExamination = () => {\r\n        dispatch(reExamination(attemptId))\r\n            .unwrap()\r\n            .then(() => {\r\n                dispatch(fetchQuestionAndAnswersByAttempt({ attemptId }));\r\n            })\r\n    };\r\n\r\n    // Hàm xử lý khi click vào nút câu hỏi\r\n    const handleQuestionClick = (question, answer, index) => {\r\n        setSelectedQuestion(question);\r\n        setSelectedAnswer(answer);\r\n        setSelectedIndex(index);\r\n        // Set question to Redux state for AI component\r\n        dispatch(setQuestion(question));\r\n    };\r\n\r\n    useEffect(() => {\r\n        dispatch(fetchAttemptById(attemptId));\r\n        dispatch(fetchQuestionAndAnswersByAttempt({ attemptId }));\r\n    }, [dispatch, attemptId]);\r\n\r\n    useEffect(() => {\r\n        if (exam?.id) {\r\n            dispatch(getTotalScoreExam(exam.id));\r\n        }\r\n    }, [exam, dispatch]);\r\n\r\n    useEffect(() => {\r\n        dispatch(fetchCodesByType([\"chapter\", \"grade\", \"difficulty\", \"question type\"]));\r\n    }, [dispatch]);\r\n\r\n    useEffect(() => {\r\n        if (exam && !exam.seeCorrectAnswer) {\r\n            dispatch(setErrorMessage(\"Không thể xem kết quả bài thi này!\"));\r\n            navigate(`/practice/exam/${exam.id}`);\r\n        }\r\n    }, [exam, dispatch, navigate]);\r\n\r\n    const [questionSections, setQuestionSections] = useState([]);\r\n\r\n    useEffect(() => {\r\n        const sections = [];\r\n\r\n        if (questionsTN.length > 0) {\r\n            sections.push({ title: 'Phần I - Trắc nghiệm', questions: questionsTN });\r\n        }\r\n\r\n        if (questionsDS.length > 0) {\r\n            sections.push({ title: 'Phần II - Đúng sai', questions: questionsDS });\r\n        }\r\n\r\n        if (questionsTLN.length > 0) {\r\n            sections.push({ title: 'Phần III - Trả lời ngắn', questions: questionsTLN });\r\n        }\r\n\r\n        setQuestionSections(sections);\r\n    }, [questionsTN, questionsDS, questionsTLN]);\r\n\r\n\r\n    return (\r\n        <UserLayout>\r\n            <div className=\"p-4 flex flex-col mb-9\">\r\n                <ExamOverviewHeader star={false} examStatus={false} title={\"Điểm\"} exam={exam} />\r\n\r\n                {/* Statistics Cards */}\r\n                <div className=\"flex flex-col md:flex-row gap-4 mb-4\">\r\n                    {/* Box điểm số */}\r\n                    <div className=\"flex-1 p-4 bg-sky-50 rounded-md border border-sky-100 shadow-sm\">\r\n                        <h5 className=\"text-xs font-semibold text-sky-800 uppercase mb-1 tracking-wide\">\r\n                            Điểm của bạn\r\n                        </h5>\r\n                        <LoadingText\r\n                            loading={loading || loadingTotalScore} w=\"w-20\" h=\"h-[30px]\" >\r\n                            <p className=\"text-4xl font-bold text-sky-600\">\r\n                                {attempt?.score ?? '--'} / {totalScore ?? 10}\r\n                            </p>\r\n                        </LoadingText>\r\n                    </div>\r\n\r\n                    {/* Box thời gian làm bài */}\r\n\r\n                    <div className=\"flex-1 p-4 bg-indigo-50 rounded-md border border-indigo-100 shadow-sm\">\r\n\r\n                        <h5 className=\"text-xs font-semibold text-indigo-800 uppercase mb-1 tracking-wide\">\r\n                            Thời gian làm bài\r\n                        </h5>\r\n                        <LoadingText\r\n                            loading={loading} w=\"w-20\" h=\"h-[30px]\" >\r\n                            <p className=\"text-xl font-semibold text-indigo-700\">\r\n                                {calculateDurationText(attempt?.startTime, attempt?.endTime)}\r\n                            </p>\r\n                        </LoadingText>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Statistics Section */}\r\n                <StatisticsSection\r\n                    questions={questions}\r\n                    answers={answers}\r\n                    codes={codes}\r\n                />\r\n\r\n                {/* Main Content */}\r\n                <div className=\"flex flex-col lg:flex-row gap-4\">\r\n                    {/* Left Panel - Question Navigation */}\r\n                    <div className=\"xl:w-1/5 lg:w-1/4 md:1/3 flex flex-col gap-4\">\r\n                        <QuestionListPanel\r\n                            questionSections={questionSections}\r\n                            answers={answers}\r\n                            handleQuestionClick={handleQuestionClick}\r\n                            selectedQuestion={selectedQuestion}\r\n                        />\r\n                        <InfoQuestion question={selectedQuestion} />\r\n                    </div>\r\n\r\n                    {/* Right Panel - Question Detail and AI */}\r\n                    <div className=\"flex-1 space-y-6\">\r\n                        {selectedQuestion ? (\r\n                            <QuestionDetail\r\n                                question={selectedQuestion}\r\n                                answer={selectedAnswer}\r\n                                index={selectedIndex}\r\n                            />\r\n                        ) : (\r\n                            <div className=\"bg-white rounded-md border border-gray-200 shadow-sm p-8 text-center\">\r\n                                <div className=\"text-gray-400 mb-4\">\r\n                                    <BookOpen size={48} className=\"mx-auto\" />\r\n                                </div>\r\n                                <h3 className=\"text-lg font-semibold text-gray-600 mb-2\">Chọn một câu hỏi để xem chi tiết</h3>\r\n                                <p className=\"text-gray-500\">Click vào các nút câu hỏi bên trái để xem nội dung và đáp án</p>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n                {/* AI Chat Widget */}\r\n                <AIChatWidget />\r\n            </div>\r\n        </UserLayout >\r\n    );\r\n};\r\n\r\nexport default ScorePage;"], "mappings": ";;;;;AAAA,OAAOA,UAAU,MAAM,6BAA6B;AACpD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,gCAAgC,QAAQ,4CAA4C;AAC7F,SAASC,aAAa,QAAQ,uCAAuC;AACrE,OAAOC,aAAa,MAAM,uCAAuC;AACjE,SAASC,OAAO,QAAQ,OAAO;AAC/B,OAAOC,cAAc,MAAM,4CAA4C;AACvE,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,YAAY,MAAM,yCAAyC;AAClE,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SACIC,WAAW,EACXC,OAAO,EACPC,WAAW,EACXC,QAAQ,EACRC,KAAK,EACLC,CAAC,EACDC,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,SAAS,EACTC,MAAM,EACNC,IAAI,QACD,cAAc;AACrB,OAAOC,uBAAuB,MAAM,sCAAsC;AAC1E,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,OAAOC,kBAAkB,MAAM,0CAA0C;AACzE,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,qBAAqB,QAAQ,sBAAsB;AAC5D,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,SAASC,WAAW,QAAQ,qCAAqC;AACjE,OAAOC,YAAY,MAAM,gCAAgC;AACzD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,cAAc,QAAQ,qBAAqB;AACpD,OAAOC,YAAY,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9D,MAAMC,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,QAAQ,KAAK;EAC5C,IAAI,CAACD,MAAM,IAAIA,MAAM,CAACE,aAAa,KAAK,IAAI,IAAIF,MAAM,CAACE,aAAa,KAAKC,SAAS,IAAIH,MAAM,CAACE,aAAa,KAAK,EAAE,EAAE;IAC/G,OAAO,YAAY,CAAC,CAAC;EACzB;EACA,OAAOF,MAAM,CAACI,MAAM,GAAG,SAAS,GAAG,WAAW;AAClD,CAAC;;AAGD;AACA,MAAMC,cAAc,GAAGC,IAAA,IAAsD;EAAA,IAArD;IAAEL,QAAQ;IAAED,MAAM;IAAEO,KAAK;IAAEC,OAAO;IAAEC;EAAW,CAAC,GAAAH,IAAA;EACpE,MAAMI,MAAM,GAAGX,iBAAiB,CAACC,MAAM,EAAEC,QAAQ,CAAC;EAElD,MAAMU,cAAc,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,4EAA4E;IAE9F,IAAIH,UAAU,EAAE;MACZ,QAAQC,MAAM;QACV,KAAK,SAAS;UACV,OAAOE,SAAS,GAAG,0CAA0C;QACjE,KAAK,WAAW;UACZ,OAAOA,SAAS,GAAG,sCAAsC;QAC7D,KAAK,YAAY;UACb,OAAOA,SAAS,GAAG,4CAA4C;QACnE;UACI,OAAOA,SAAS,GAAG,wCAAwC;MACnE;IACJ,CAAC,MAAM;MACH,QAAQF,MAAM;QACV,KAAK,SAAS;UACV,OAAOE,SAAS,GAAG,gEAAgE;QACvF,KAAK,WAAW;UACZ,OAAOA,SAAS,GAAG,wDAAwD;QAC/E,KAAK,YAAY;UACb,OAAOA,SAAS,GAAG,oEAAoE;QAC3F;UACI,OAAOA,SAAS,GAAG,4DAA4D;MACvF;IACJ;EACJ,CAAC;EAED,oBACIhB,OAAA;IACIY,OAAO,EAAEA,CAAA,KAAMA,OAAO,CAACP,QAAQ,EAAED,MAAM,EAAEO,KAAK,CAAE;IAChDM,SAAS,EAAEF,cAAc,CAAC,CAAE;IAAAG,QAAA,EAE3BP,KAAK,GAAG;EAAC;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEjB,CAAC;AAACC,EAAA,GAvCId,cAAc;AAyCpB,MAAMe,eAAe,GAAGC,KAAA,IAAkD;EAAA,IAAjD;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,YAAY;IAAEC;EAAU,CAAC,GAAAJ,KAAA;EACjEK,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEL,KAAK,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,SAAS,CAAC;EACxE,oBACI7B,OAAA;IAAKiB,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACpClB,OAAA;MACIiB,SAAS,2GAAAe,MAAA,CACbJ,YAAY,GAAG,+BAA+B,GAAGD,QAAQ,GAAG,2BAA2B,GAAG,iBAAiB,eAC7G;MAAAT,QAAA,eAEMlB,OAAA;QAAKiB,SAAS,EAAC;MAA+B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,eAENtB,OAAA;MACIiB,SAAS,mEAAAe,MAAA,CACbJ,YAAY,GAAG,gBAAgB,GAAGD,QAAQ,GAAG,cAAc,GAAG,eAAe,eAC/E;MAAAT,QAAA,EAEOQ;IAAK;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAENK,QAAQ,GACLE,SAAS,gBACL7B,OAAA,CAACtB,KAAK;MAACuD,IAAI,EAAE,EAAG;MAAChB,SAAS,EAAC,gBAAgB;MAACiB,KAAK,EAAC;IAAe;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEpEtB,OAAA,CAACrB,CAAC;MAACsD,IAAI,EAAE,EAAG;MAAChB,SAAS,EAAC,cAAc;MAACiB,KAAK,EAAC;IAAc;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC/D,gBAEDtB,OAAA;MAAKiB,SAAS,EAAC;IAAS;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC7B;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;;AAED;AAAAa,GAAA,GAjCMX,eAAe;AAkCrB,MAAMY,cAAc,GAAGC,KAAA,IAAiC;EAAAC,EAAA;EAAA,IAAhC;IAAEjC,QAAQ;IAAED,MAAM;IAAEO;EAAM,CAAC,GAAA0B,KAAA;EAC/C,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EAEvD,IAAI,CAACyC,QAAQ,EAAE,OAAO,IAAI;EAC1B,MAAMS,MAAM,GAAGX,iBAAiB,CAACC,MAAM,EAAEC,QAAQ,CAAC;EAElD,MAAMoC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,QAAQ3B,MAAM;MACV,KAAK,SAAS;QACV,oBACId,OAAA;UAAKiB,SAAS,EAAC,mFAAmF;UAAAC,QAAA,gBAC9FlB,OAAA,CAAC1B,WAAW;YAAC2C,SAAS,EAAC,gBAAgB;YAACgB,IAAI,EAAE;UAAG;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDtB,OAAA;YAAMiB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAEd,KAAK,WAAW;QACZ,oBACItB,OAAA;UAAKiB,SAAS,EAAC,+EAA+E;UAAAC,QAAA,gBAC1FlB,OAAA,CAACzB,OAAO;YAAC0C,SAAS,EAAC,cAAc;YAACgB,IAAI,EAAE;UAAG;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9CtB,OAAA;YAAMiB,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAEd,KAAK,YAAY;QACb,oBACItB,OAAA;UAAKiB,SAAS,EAAC,qFAAqF;UAAAC,QAAA,gBAChGlB,OAAA,CAACxB,WAAW;YAACyC,SAAS,EAAC,iBAAiB;YAACgB,IAAI,EAAE;UAAG;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDtB,OAAA;YAAMiB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAEd;QACI,OAAO,IAAI;IACnB;EACJ,CAAC;EAID,oBACItB,OAAA;IAAKiB,SAAS,EAAC,sDAAsD;IAAAC,QAAA,gBAEjElB,OAAA;MAAKiB,SAAS,EAAC,wGAAwG;MAAAC,QAAA,gBACnHlB,OAAA;QAAKiB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChClB,OAAA;UAAIiB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,GAAC,SAAI,EAACP,KAAK,GAAG,CAAC,EAAC,GAAC,eAAAX,OAAA;YAAMiB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,OAAK,EAACb,QAAQ,CAACqC,EAAE,EAAC,GAAC;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3ItB,OAAA,CAAC5B,YAAY;UAACuE,UAAU,EAAEtC,QAAQ,CAACqC;QAAG;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,EACLmB,gBAAgB,CAAC,CAAC;IAAA;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGNtB,OAAA;MAAKiB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1BlB,OAAA;QAAKiB,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAC7DlB,OAAA;UAAIiB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/EtB,OAAA;UAAKiB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eAClDlB,OAAA,CAAChC,aAAa;YAAC4E,IAAI,EAAEvC,QAAQ,CAACwC,OAAQ;YAAC5B,SAAS,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,EACLjB,QAAQ,CAACyC,QAAQ,iBACd9C,OAAA;UAAKiB,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eAClElB,OAAA;YACI+C,GAAG,EAAE1C,QAAQ,CAACyC,QAAS;YACvBE,GAAG,EAAC,UAAU;YACd/B,SAAS,EAAC;UAA8E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAGLjB,QAAQ,CAAC4C,cAAc,KAAK,IAAI,IAAI5C,QAAQ,CAAC6C,UAAU,iBACpDlD,OAAA;QAAKiB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtBlB,OAAA;UAAIiB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtEtB,OAAA;UAAKiB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAC/B,CAAC,GAAGb,QAAQ,CAAC6C,UAAU,CAAC,CACpBC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,CAACE,KAAK,IAAI,CAAC,KAAKD,CAAC,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC,CAC/CC,GAAG,CAAC,CAACC,SAAS,EAAE7C,KAAK,KAAK;YACvB,MAAM8C,cAAc,GAAGrD,MAAM,IAAIA,MAAM,CAACE,aAAa,IAAIkD,SAAS,CAACd,EAAE;YACrE,MAAMb,SAAS,GAAG2B,SAAS,CAAC3B,SAAS;YAErC,IAAI6B,cAAc,GAAG,4BAA4B;YACjD,IAAIC,SAAS,GAAG,wBAAwB;YAExC,IAAI9B,SAAS,EAAE;cACX6B,cAAc,GAAG,+BAA+B;cAChDC,SAAS,GAAG,yBAAyB;YACzC,CAAC,MAAM,IAAIF,cAAc,IAAI,CAAC5B,SAAS,EAAE;cACrC6B,cAAc,GAAG,2BAA2B;cAC5CC,SAAS,GAAG,uBAAuB;YACvC,CAAC,MAAM,IAAIF,cAAc,EAAE;cACvBC,cAAc,GAAG,2BAA2B;cAC5CC,SAAS,GAAG,uBAAuB;YACvC;YAEA,oBACI3D,OAAA;cAEIiB,SAAS,0CAAAe,MAAA,CAA0C0B,cAAc,CAAG;cAAAxC,QAAA,eAEpElB,OAAA;gBAAKiB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACnClB,OAAA;kBAAMiB,SAAS,6FAAAe,MAAA,CAA6F2B,SAAS,CAAG;kBAAAzC,QAAA,EACnHW,SAAS,gBACN7B,OAAA,CAACtB,KAAK;oBAACuD,IAAI,EAAE;kBAAG;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,GACnBmC,cAAc,IAAI,CAAC5B,SAAS,gBAC5B7B,OAAA,CAACrB,CAAC;oBAACsD,IAAI,EAAE;kBAAG;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,GAEfsC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGlD,KAAK;gBACjC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACPtB,OAAA;kBAAKiB,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACnBlB,OAAA;oBAAKiB,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,eAClDlB,OAAA,CAAChC,aAAa;sBAAC4E,IAAI,EAAEY,SAAS,CAACX,OAAQ;sBAAC5B,SAAS,EAAC;oBAAe;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,EACLkC,SAAS,CAACV,QAAQ,iBACf9C,OAAA;oBAAKiB,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,eAClElB,OAAA;sBACI+C,GAAG,EAAES,SAAS,CAACV,QAAS;sBACxBE,GAAG,eAAAhB,MAAA,CAAe4B,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGlD,KAAK,CAAC,CAAG;sBACpDM,SAAS,EAAC;oBAA8E;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CACR;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC,GA3BDkC,SAAS,CAACd,EAAE,IAAI/B,KAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4BzB,CAAC;UAEd,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR,EAGAjB,QAAQ,CAAC4C,cAAc,KAAK,IAAI,IAAI5C,QAAQ,CAAC6C,UAAU,iBACpDlD,OAAA;QAAKiB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtBlB,OAAA;UAAIiB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjFtB,OAAA;UAAKiB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAC/B,CAAC,GAAGb,QAAQ,CAAC6C,UAAU,CAAC,CACpBC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,CAACE,KAAK,IAAI,CAAC,KAAKD,CAAC,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC,CAC/CC,GAAG,CAAC,CAACC,SAAS,EAAE7C,KAAK,KAAK;YACvB,MAAMmD,WAAW,GAAG1D,MAAM,GAAG2D,IAAI,CAACC,KAAK,CAAC5D,MAAM,CAACE,aAAa,IAAI,IAAI,CAAC,GAAG,EAAE;YAC1E,MAAM2D,UAAU,GAAGH,WAAW,CAACI,IAAI,CAAEC,EAAE,IAAKX,SAAS,CAACd,EAAE,KAAKyB,EAAE,CAACC,WAAW,CAAC;YAE5E,oBACIpE,OAAA;cAEIiB,SAAS,yCAA0C;cAAAC,QAAA,eAEnDlB,OAAA;gBAAKiB,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAChClB,OAAA;kBAAKiB,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACnClB,OAAA;oBAAMiB,SAAS,EAAC,+GAA+G;oBAAAC,QAAA,EAC1H0C,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGlD,KAAK;kBAAC;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACPtB,OAAA;oBAAKiB,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACnBlB,OAAA;sBAAKiB,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,eAClDlB,OAAA,CAAChC,aAAa;wBAAC4E,IAAI,EAAEY,SAAS,CAACX,OAAQ;wBAAC5B,SAAS,EAAC;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE,CAAC,EACLkC,SAAS,CAACV,QAAQ,iBACf9C,OAAA;sBAAKiB,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,eAClElB,OAAA;wBACI+C,GAAG,EAAES,SAAS,CAACV,QAAS;wBACxBE,GAAG,eAAAhB,MAAA,CAAe4B,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGlD,KAAK,CAAC,CAAG;wBACpDM,SAAS,EAAC;sBAA8E;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3F;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACR;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAGNtB,OAAA;kBAAKiB,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBACzClB,OAAA,CAACwB,eAAe;oBACZE,KAAK,EAAC,cAAM;oBACZC,QAAQ,EAAE,CAAAsC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE7D,MAAM,MAAK,IAAK;oBACtCyB,SAAS,EAAE,CAAAoC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE7D,MAAM,MAAKoD,SAAS,CAAC3B,SAAU;oBACtDD,YAAY,EAAE4B,SAAS,CAAC3B,SAAS,KAAK;kBAAK;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,eACFtB,OAAA,CAACwB,eAAe;oBACZE,KAAK,EAAC,KAAK;oBACXC,QAAQ,EAAE,CAAAsC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE7D,MAAM,MAAK,KAAM;oBACvCyB,SAAS,EAAE,CAAAoC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE7D,MAAM,MAAKoD,SAAS,CAAC3B,SAAU;oBACtDD,YAAY,EAAE4B,SAAS,CAAC3B,SAAS,KAAK;kBAAM;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC,GAvCDkC,SAAS,CAACd,EAAE,IAAI/B,KAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwCzB,CAAC;UAEd,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR,EAGAjB,QAAQ,CAAC4C,cAAc,KAAK,KAAK,iBAC9BjD,OAAA,CAAAE,SAAA;QAAAgB,QAAA,gBACIlB,OAAA;UAAKiB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC7DlB,OAAA;YAAIiB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClFtB,OAAA;YAAKiB,SAAS,EAAC,SAAS;YAAAC,QAAA,EACnBd,MAAM,IAAIA,MAAM,CAACE,aAAa,gBAC3BN,OAAA,CAAChC,aAAa;cAAC4E,IAAI,EAAExC,MAAM,CAACE,aAAc;cAACW,SAAS,EAAC;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEnFtB,OAAA;cAAMiB,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAC5D;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENtB,OAAA;UAAKiB,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBAC/DlB,OAAA;YAAIiB,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3EtB,OAAA;YAAKiB,SAAS,EAAC,SAAS;YAAAC,QAAA,eACpBlB,OAAA,CAAChC,aAAa;cAAC4E,IAAI,EAAEvC,QAAQ,CAACgE,aAAc;cAACpD,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA,eACR,CACL,EAGAjB,QAAQ,CAACiE,QAAQ,iBACdtE,OAAA;QAAKiB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC3DlB,OAAA;UAAKiB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACnDlB,OAAA;YAAIiB,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjEtB,OAAA;YACIY,OAAO,EAAEA,CAAA,KAAM4B,eAAe,CAAC,CAACD,YAAY,CAAE;YAC9CtB,SAAS,EAAC,iHAAiH;YAAAC,QAAA,GAE1HqB,YAAY,gBAAGvC,OAAA,CAACnB,MAAM;cAACoD,IAAI,EAAE;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGtB,OAAA,CAACpB,GAAG;cAACqD,IAAI,EAAE;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACvDiB,YAAY,GAAG,aAAa,GAAG,cAAc;UAAA;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAELiB,YAAY,iBACTvC,OAAA;UAAKiB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBlB,OAAA;YAAKiB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAClClB,OAAA,CAACd,uBAAuB;cAAC2D,OAAO,EAAExC,QAAQ,CAACiE;YAAS;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,EACLjB,QAAQ,CAACkE,gBAAgB,iBACtBvE,OAAA;YAAKiB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAChClB,OAAA;cACI+C,GAAG,EAAE1C,QAAQ,CAACkE,gBAAiB;cAC/BvB,GAAG,EAAC,oBAAU;cACd/B,SAAS,EAAC;YAA2D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EACAjB,QAAQ,CAACmE,WAAW,iBACjBxE,OAAA;YAAKiB,SAAS,EAAC,kCAAkC;YAAAC,QAAA,eAC7ClB,OAAA,CAACb,aAAa;cAACsF,GAAG,EAAEpE,QAAQ,CAACmE,WAAY;cAACE,SAAS,EAAC;YAAwC;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACgB,EAAA,CA1PIF,cAAc;AAAAuC,GAAA,GAAdvC,cAAc;AA4PpB,MAAMwC,iBAAiB,GAAGC,KAAA,IAKpB;EAAA,IALqB;IACvBC,gBAAgB;IAChBC,OAAO;IACPC,mBAAmB;IACnBC;EACJ,CAAC,GAAAJ,KAAA;EACG,MAAMK,WAAW,GAAG,CAChB;IAAEC,KAAK,EAAE,OAAO;IAAEzD,KAAK,EAAE;EAAO,CAAC,EACjC;IAAEyD,KAAK,EAAE,KAAK;IAAEzD,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAEyD,KAAK,EAAE,QAAQ;IAAEzD,KAAK,EAAE;EAAe,CAAC,CAC7C;EACD,oBACI1B,OAAA;IAAKiB,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBACrElB,OAAA;MAAIiB,SAAS,EAAC,0CAA0C;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAG/EtB,OAAA;MAAKiB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,EAC7CgE,WAAW,CAAC3B,GAAG,CAAE6B,IAAI,iBAClBpF,OAAA;QAAKiB,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACpClB,OAAA;UAAKiB,SAAS,gBAAAe,MAAA,CAAgBoD,IAAI,CAACD,KAAK,yBAAAnD,MAAA,CAAsBoD,IAAI,CAACD,KAAK;QAAe;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9FtB,OAAA;UAAMiB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEkE,IAAI,CAAC1D;QAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,GAFT8D,IAAI,CAAC1D,KAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGnD,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNtB,OAAA;MAAKiB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,EAC/B4D,gBAAgB,CAACvB,GAAG,CAAC,CAAC8B,OAAO,EAAEC,YAAY;QAAA,IAAAC,kBAAA;QAAA,oBACxCvF,OAAA;UAAAkB,QAAA,gBACIlB,OAAA;YAAIiB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAEmE,OAAO,CAACnD;UAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7EtB,OAAA;YAAKiB,SAAS,EAAC,sBAAsB;YAAAC,QAAA,GAAAqE,kBAAA,GAChCF,OAAO,CAACG,SAAS,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBhC,GAAG,CAAC,CAAClD,QAAQ,EAAEM,KAAK,KAAK;cACzC,MAAMP,MAAM,GAAG2E,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEb,IAAI,CAAEd,CAAC,IAAKA,CAAC,CAACT,UAAU,KAAKtC,QAAQ,CAACqC,EAAE,CAAC;cACjE,oBACI1C,OAAA,CAACS,cAAc;gBAEXJ,QAAQ,EAAEA,QAAS;gBACnBD,MAAM,EAAEA,MAAO;gBACfO,KAAK,EAAEA,KAAM;gBACbC,OAAO,EAAEoE,mBAAoB;gBAC7BnE,UAAU,EAAE,CAAAoE,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEvC,EAAE,MAAKrC,QAAQ,CAACqC;cAAG,GAL5CrC,QAAQ,CAACqC,EAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMnB,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA,GAhBAgE,YAAY;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiBjB,CAAC;MAAA,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACmE,GAAA,GAlDIb,iBAAiB;AAoDvB,MAAMc,YAAY,GAAGC,KAAA,IAAkB;EAAAC,GAAA;EAAA,IAAjB;IAAEvF;EAAS,CAAC,GAAAsF,KAAA;EAC9B,MAAM;IAAEE;EAAM,CAAC,GAAGtI,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAACD,KAAK,CAAC;EAIrD,oBACI7F,OAAA;IAAKiB,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBACrElB,OAAA;MAAIiB,SAAS,EAAC,0CAA0C;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAE9EjB,QAAQ,gBAAGL,OAAA;MAAKiB,SAAS,EAAC,gDAAgD;MAAAC,QAAA,gBACvElB,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACtCtB,OAAA;QAAAkB,QAAA,EAAMb,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqC;MAAE;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEzBtB,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1CtB,OAAA;QAAAkB,QAAA,EAAMrB,cAAc,CAAC,YAAY,EAAEQ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE0F,UAAU,EAAEF,KAAK;MAAC;QAAA1E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEtEtB,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1CtB,OAAA;QAAAkB,QAAA,EAAMrB,cAAc,CAAC,SAAS,EAAEQ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE2F,OAAO,EAAEH,KAAK;MAAC;QAAA1E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEhEtB,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvCtB,OAAA;QAAAkB,QAAA,EAAMrB,cAAc,CAAC,OAAO,EAAEQ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4F,KAAK,EAAEJ,KAAK;MAAC;QAAA1E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE5DtB,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChDtB,OAAA;QAAAkB,QAAA,EACK,CAAAb,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4C,cAAc,MAAK,IAAI,GAC5B,aAAa,GACb,CAAA5C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4C,cAAc,MAAK,KAAK,GAC9B,cAAc,GACd,CAAA5C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4C,cAAc,MAAK,IAAI,GAC7B,UAAU,GACV;MAAU;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBACFtB,OAAA;MAAKiB,SAAS,EAAC,uBAAuB;MAAAC,QAAA,EAAC;IAAiC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAChF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACsE,GAAA,CArCIF,YAAY;EAAA,QACInI,WAAW;AAAA;AAAA2I,GAAA,GAD3BR,YAAY;AAsClB,MAAMS,UAAU,GAAGC,KAAA;EAAA,IAAC;IAAExF;EAAQ,CAAC,GAAAwF,KAAA;EAAA,oBAC3BpG,OAAA;IACIY,OAAO,EAAEA,OAAQ;IACjBK,SAAS,EAAC,2GAA2G;IAAAC,QAAA,gBAErHlB,OAAA,CAAClB,SAAS;MAACmC,SAAS,EAAC;IAAS;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjCtB,OAAA;MAAAkB,QAAA,EAAM;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CAAC;AAAA,CACZ;;AAED;AAAA+E,GAAA,GAVMF,UAAU;AAWhB,MAAMG,mBAAmB,GAAG,SAAAA,CAACd,SAAS,EAAET,OAAO,EAAEc,KAAK,EAA6B;EAAA,IAA3BU,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAjG,SAAA,GAAAiG,SAAA,MAAG,YAAY;EAC1E,MAAME,KAAK,GAAG,CAAC,CAAC;EAEhBlB,SAAS,CAACmB,OAAO,CAAC,CAACtG,QAAQ,EAAEM,KAAK,KAAK;IACnC,MAAMP,MAAM,GAAG2E,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEb,IAAI,CAAEd,CAAC,IAAKA,CAAC,CAACT,UAAU,KAAKtC,QAAQ,CAACqC,EAAE,CAAC;IACjE,MAAM5B,MAAM,GAAGX,iBAAiB,CAACC,MAAM,EAAEC,QAAQ,CAAC;;IAElD;IACA,IAAIuG,QAAQ;IACZ,IAAIL,OAAO,KAAK,YAAY,EAAE;MAC1BK,QAAQ,GAAGvG,QAAQ,CAAC0F,UAAU,IAAI,MAAM;IAC5C,CAAC,MAAM,IAAIQ,OAAO,KAAK,SAAS,EAAE;MAAA,IAAAM,cAAA;MAC9B,MAAMC,WAAW,GAAGjB,KAAK,aAALA,KAAK,wBAAAgB,cAAA,GAALhB,KAAK,CAAEG,OAAO,cAAAa,cAAA,uBAAdA,cAAA,CAAgB3C,IAAI,CAAC6C,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK3G,QAAQ,CAAC2F,OAAO,CAAC;MAC1EY,QAAQ,GAAG,CAAAE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEG,WAAW,KAAI,MAAM;IACjD;IAEA,IAAI,CAACP,KAAK,CAACE,QAAQ,CAAC,EAAE;MAClBF,KAAK,CAACE,QAAQ,CAAC,GAAG;QACdM,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE,CAAC;QACZC,UAAU,EAAE,CAAC;QACb7B,SAAS,EAAE;MACf,CAAC;IACL;IAEAkB,KAAK,CAACE,QAAQ,CAAC,CAACM,KAAK,EAAE;IACvBR,KAAK,CAACE,QAAQ,CAAC,CAACpB,SAAS,CAAC8B,IAAI,CAAC;MAC3BC,cAAc,EAAE5G,KAAK,GAAG,CAAC;MACzBG,MAAM,EAAEA,MAAM;MACdT,QAAQ,EAAEA;IACd,CAAC,CAAC;IAEF,IAAIS,MAAM,KAAK,SAAS,EAAE;MACtB4F,KAAK,CAACE,QAAQ,CAAC,CAACO,OAAO,EAAE;IAC7B,CAAC,MAAM,IAAIrG,MAAM,KAAK,WAAW,EAAE;MAC/B4F,KAAK,CAACE,QAAQ,CAAC,CAACQ,SAAS,EAAE;IAC/B,CAAC,MAAM;MACHV,KAAK,CAACE,QAAQ,CAAC,CAACS,UAAU,EAAE;IAChC;EACJ,CAAC,CAAC;;EAEF;EACAG,MAAM,CAACC,IAAI,CAACf,KAAK,CAAC,CAACC,OAAO,CAACe,GAAG,IAAI;IAC9B,MAAMC,QAAQ,GAAGjB,KAAK,CAACgB,GAAG,CAAC,CAACP,OAAO,GAAGT,KAAK,CAACgB,GAAG,CAAC,CAACN,SAAS;IAC1DV,KAAK,CAACgB,GAAG,CAAC,CAACE,QAAQ,GAAGD,QAAQ,GAAG,CAAC,GAAGE,IAAI,CAACC,KAAK,CAAEpB,KAAK,CAACgB,GAAG,CAAC,CAACP,OAAO,GAAGQ,QAAQ,GAAI,GAAG,CAAC,GAAG,CAAC;EAC9F,CAAC,CAAC;EAEF,OAAOjB,KAAK;AAChB,CAAC;;AAED;AACA,MAAMqB,iBAAiB,GAAGC,KAAA,IAAmC;EAAAC,GAAA;EAAA,IAAlC;IAAEzC,SAAS;IAAET,OAAO;IAAEc;EAAM,CAAC,GAAAmC,KAAA;EACpD,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAGvK,QAAQ,CAAC,YAAY,CAAC;EAExD,MAAMwK,eAAe,GAAG9B,mBAAmB,CAACd,SAAS,EAAET,OAAO,EAAEc,KAAK,EAAE,YAAY,CAAC;EACpF,MAAMwC,YAAY,GAAG/B,mBAAmB,CAACd,SAAS,EAAET,OAAO,EAAEc,KAAK,EAAE,SAAS,CAAC;EAE9E,MAAMyC,YAAY,GAAGJ,SAAS,KAAK,YAAY,GAAGE,eAAe,GAAGC,YAAY;EAEhF,MAAME,WAAW,GAAGA,CAAA,kBAChBvI,OAAA;IAAKiB,SAAS,EAAC,4BAA4B;IAAAC,QAAA,eACvClB,OAAA;MAAOiB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrClB,OAAA;QAAAkB,QAAA,eACIlB,OAAA;UAAIiB,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC1DlB,OAAA;YAAIiB,SAAS,EAAC,wEAAwE;YAAAC,QAAA,EACjFgH,SAAS,KAAK,YAAY,GAAG,QAAQ,GAAG;UAAQ;YAAA/G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACLtB,OAAA;YAAIiB,SAAS,EAAC,0EAA0E;YAAAC,QAAA,EAAC;UAEzF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtB,OAAA;YAAIiB,SAAS,EAAC,2EAA2E;YAAAC,QAAA,EAAC;UAE1F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtB,OAAA;YAAIiB,SAAS,EAAC,yEAAyE;YAAAC,QAAA,EAAC;UAExF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtB,OAAA;YAAIiB,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EAAC;UAE3F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtB,OAAA;YAAIiB,SAAS,EAAC,yEAAyE;YAAAC,QAAA,EAAC;UAExF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtB,OAAA;YAAIiB,SAAS,EAAC,0EAA0E;YAAAC,QAAA,EAAC;UAEzF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACRtB,OAAA;QAAAkB,QAAA,EACKsG,MAAM,CAACgB,OAAO,CAACF,YAAY,CAAC,CAAC/E,GAAG,CAAC,CAAAkF,KAAA,EAAc9H,KAAK;UAAA,IAAlB,CAAC+G,GAAG,EAAEgB,IAAI,CAAC,GAAAD,KAAA;UAAA,oBAC1CzI,OAAA;YAAciB,SAAS,EAAEN,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,YAAa;YAAAO,QAAA,gBACjElB,OAAA;cAAIiB,SAAS,EAAC,4DAA4D;cAAAC,QAAA,EACrEwG;YAAG;cAAAvG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLtB,OAAA;cAAIiB,SAAS,EAAC,4DAA4D;cAAAC,QAAA,EACrEwH,IAAI,CAACxB;YAAK;cAAA/F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACLtB,OAAA;cAAIiB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,eACxDlB,OAAA;gBAAMiB,SAAS,EAAC,gHAAgH;gBAAAC,QAAA,EAC3HwH,IAAI,CAACvB;cAAO;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACLtB,OAAA;cAAIiB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,eACxDlB,OAAA;gBAAMiB,SAAS,EAAC,4GAA4G;gBAAAC,QAAA,EACvHwH,IAAI,CAACtB;cAAS;gBAAAjG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACLtB,OAAA;cAAIiB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,eACxDlB,OAAA;gBAAMiB,SAAS,EAAC,kHAAkH;gBAAAC,QAAA,EAC7HwH,IAAI,CAACrB;cAAU;gBAAAlG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACLtB,OAAA;cAAIiB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,eACxDlB,OAAA;gBAAMiB,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,GACzHwH,IAAI,CAACd,QAAQ,EAAC,GACnB;cAAA;gBAAAzG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACLtB,OAAA;cAAIiB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,eAC5ClB,OAAA;gBAAKiB,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EACzCwH,IAAI,CAAClD,SAAS,CAACjC,GAAG,CAAEoF,CAAC,iBAClB3I,OAAA;kBAEIiB,SAAS,oLAAAe,MAAA,CAEH2G,CAAC,CAAC7H,MAAM,KAAK,SAAS,GAAG,qDAAqD,GAC5E6H,CAAC,CAAC7H,MAAM,KAAK,WAAW,GAAG,+CAA+C,GACtE,wDAAwD,mDAClE;kBAAAI,QAAA,EAEDyH,CAAC,CAACpB;gBAAc,GARZoB,CAAC,CAACpB,cAAc;kBAAApG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASnB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GA3CAoG,GAAG;YAAAvG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4CR,CAAC;QAAA,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CACR;EAED,IAAIkE,SAAS,CAACiB,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAEvC,oBACIzG,OAAA;IAAKiB,SAAS,EAAC,MAAM;IAAAC,QAAA,eAEjBlB,OAAA;MAAKiB,SAAS,EAAC,mEAAmE;MAAAC,QAAA,gBAC9ElB,OAAA;QAAKiB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChClB,OAAA,CAACF,YAAY;UACTc,OAAO,EAAEA,CAAA,KAAMuH,YAAY,CAAC,YAAY,CAAE;UAC1CjG,KAAK,EAAE,sBAAuB;UAC9B0G,UAAU,EAAC,qBAAQ;UACnBC,QAAQ,EAAEX,SAAS,KAAK,YAAa;UACrCY,IAAI,EAAE9J;QAAO;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACFtB,OAAA,CAACF,YAAY;UACTc,OAAO,EAAEA,CAAA,KAAMuH,YAAY,CAAC,SAAS,CAAE;UACvCjG,KAAK,EAAE,sBAAuB;UAC9B0G,UAAU,EAAC,kBAAQ;UACnBC,QAAQ,EAAEX,SAAS,KAAK,SAAU;UAClCY,IAAI,EAAE7J;QAAK;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGLiH,WAAW,CAAC,CAAC;IAAA;MAAApH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC2G,GAAA,CArHIF,iBAAiB;AAAAgB,GAAA,GAAjBhB,iBAAiB;AAuHvB,MAAMiB,SAAS,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,cAAA;EACpB,MAAM;IAAEC;EAAU,CAAC,GAAG1L,SAAS,CAAC,CAAC;EACjC,MAAM2L,QAAQ,GAAG5L,WAAW,CAAC,CAAC;EAC9B,MAAM6L,QAAQ,GAAGxL,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2H,SAAS;IAAET,OAAO;IAAEuE,IAAI;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGjM,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAAC2D,SAAS,CAAC;EAC9F,MAAM,CAACxE,gBAAgB,EAAEyE,mBAAmB,CAAC,GAAG9L,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC+L,cAAc,EAAEC,iBAAiB,CAAC,GAAGhM,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiM,aAAa,EAAEC,gBAAgB,CAAC,GAAGlM,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM;IAAEmM,UAAU;IAAEC;EAAkB,CAAC,GAAGzM,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAACmE,UAAU,CAAC;EAClF,MAAM;IAAEpE;EAAM,CAAC,GAAGtI,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAACD,KAAK,CAAC;EAErD,MAAM,CAACqE,WAAW,EAAEC,cAAc,CAAC,GAAGvM,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwM,WAAW,EAAEC,cAAc,CAAC,GAAGzM,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0M,YAAY,EAAEC,eAAe,CAAC,GAAG3M,QAAQ,CAAC,EAAE,CAAC;EAEpDD,SAAS,CAAC,MAAM;IACZ,IAAI6H,SAAS,CAACiB,MAAM,GAAG,CAAC,EAAE;MACtB0D,cAAc,CAAC3E,SAAS,CAACgF,MAAM,CAAEnK,QAAQ,IAAKA,QAAQ,CAAC4C,cAAc,KAAK,IAAI,CAAC,CAAC;MAChFoH,cAAc,CAAC7E,SAAS,CAACgF,MAAM,CAAEnK,QAAQ,IAAKA,QAAQ,CAAC4C,cAAc,KAAK,IAAI,CAAC,CAAC;MAChFsH,eAAe,CAAC/E,SAAS,CAACgF,MAAM,CAAEnK,QAAQ,IAAKA,QAAQ,CAAC4C,cAAc,KAAK,KAAK,CAAC,CAAC;IACtF;EACJ,CAAC,EAAE,CAACuC,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMiF,mBAAmB,GAAGA,CAAA,KAAM;IAC9BrB,QAAQ,CAACrL,aAAa,CAACoL,SAAS,CAAC,CAAC,CAC7BuB,MAAM,CAAC,CAAC,CACRC,IAAI,CAAC,MAAM;MACRvB,QAAQ,CAACtL,gCAAgC,CAAC;QAAEqL;MAAU,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC;EACV,CAAC;;EAED;EACA,MAAMnE,mBAAmB,GAAGA,CAAC3E,QAAQ,EAAED,MAAM,EAAEO,KAAK,KAAK;IACrD+I,mBAAmB,CAACrJ,QAAQ,CAAC;IAC7BuJ,iBAAiB,CAACxJ,MAAM,CAAC;IACzB0J,gBAAgB,CAACnJ,KAAK,CAAC;IACvB;IACAyI,QAAQ,CAAC1J,WAAW,CAACW,QAAQ,CAAC,CAAC;EACnC,CAAC;EAED1C,SAAS,CAAC,MAAM;IACZyL,QAAQ,CAAC7J,gBAAgB,CAAC4J,SAAS,CAAC,CAAC;IACrCC,QAAQ,CAACtL,gCAAgC,CAAC;MAAEqL;IAAU,CAAC,CAAC,CAAC;EAC7D,CAAC,EAAE,CAACC,QAAQ,EAAED,SAAS,CAAC,CAAC;EAEzBxL,SAAS,CAAC,MAAM;IACZ,IAAI2L,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE5G,EAAE,EAAE;MACV0G,QAAQ,CAAC3J,iBAAiB,CAAC6J,IAAI,CAAC5G,EAAE,CAAC,CAAC;IACxC;EACJ,CAAC,EAAE,CAAC4G,IAAI,EAAEF,QAAQ,CAAC,CAAC;EAEpBzL,SAAS,CAAC,MAAM;IACZyL,QAAQ,CAACxJ,gBAAgB,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC,CAAC;EACnF,CAAC,EAAE,CAACwJ,QAAQ,CAAC,CAAC;EAEdzL,SAAS,CAAC,MAAM;IACZ,IAAI2L,IAAI,IAAI,CAACA,IAAI,CAACsB,gBAAgB,EAAE;MAChCxB,QAAQ,CAAC/K,eAAe,CAAC,oCAAoC,CAAC,CAAC;MAC/DgL,QAAQ,mBAAArH,MAAA,CAAmBsH,IAAI,CAAC5G,EAAE,CAAE,CAAC;IACzC;EACJ,CAAC,EAAE,CAAC4G,IAAI,EAAEF,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAE9B,MAAM,CAACvE,gBAAgB,EAAE+F,mBAAmB,CAAC,GAAGjN,QAAQ,CAAC,EAAE,CAAC;EAE5DD,SAAS,CAAC,MAAM;IACZ,MAAMmN,QAAQ,GAAG,EAAE;IAEnB,IAAIZ,WAAW,CAACzD,MAAM,GAAG,CAAC,EAAE;MACxBqE,QAAQ,CAACxD,IAAI,CAAC;QAAEpF,KAAK,EAAE,sBAAsB;QAAEsD,SAAS,EAAE0E;MAAY,CAAC,CAAC;IAC5E;IAEA,IAAIE,WAAW,CAAC3D,MAAM,GAAG,CAAC,EAAE;MACxBqE,QAAQ,CAACxD,IAAI,CAAC;QAAEpF,KAAK,EAAE,oBAAoB;QAAEsD,SAAS,EAAE4E;MAAY,CAAC,CAAC;IAC1E;IAEA,IAAIE,YAAY,CAAC7D,MAAM,GAAG,CAAC,EAAE;MACzBqE,QAAQ,CAACxD,IAAI,CAAC;QAAEpF,KAAK,EAAE,yBAAyB;QAAEsD,SAAS,EAAE8E;MAAa,CAAC,CAAC;IAChF;IAEAO,mBAAmB,CAACC,QAAQ,CAAC;EACjC,CAAC,EAAE,CAACZ,WAAW,EAAEE,WAAW,EAAEE,YAAY,CAAC,CAAC;EAG5C,oBACItK,OAAA,CAAC1C,UAAU;IAAA4D,QAAA,eACPlB,OAAA;MAAKiB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACnClB,OAAA,CAACX,kBAAkB;QAAC0L,IAAI,EAAE,KAAM;QAACC,UAAU,EAAE,KAAM;QAAC9I,KAAK,EAAE,MAAO;QAACoH,IAAI,EAAEA;MAAK;QAAAnI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGjFtB,OAAA;QAAKiB,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBAEjDlB,OAAA;UAAKiB,SAAS,EAAC,iEAAiE;UAAAC,QAAA,gBAC5ElB,OAAA;YAAIiB,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAAC;UAEhF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtB,OAAA,CAACZ,WAAW;YACRoK,OAAO,EAAEA,OAAO,IAAIQ,iBAAkB;YAACiB,CAAC,EAAC,MAAM;YAACC,CAAC,EAAC,UAAU;YAAAhK,QAAA,eAC5DlB,OAAA;cAAGiB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,IAAAgI,cAAA,GACzCK,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4B,KAAK,cAAAjC,cAAA,cAAAA,cAAA,GAAI,IAAI,EAAC,KAAG,EAACa,UAAU,aAAVA,UAAU,cAAVA,UAAU,GAAI,EAAE;YAAA;cAAA5I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAINtB,OAAA;UAAKiB,SAAS,EAAC,uEAAuE;UAAAC,QAAA,gBAElFlB,OAAA;YAAIiB,SAAS,EAAC,oEAAoE;YAAAC,QAAA,EAAC;UAEnF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtB,OAAA,CAACZ,WAAW;YACRoK,OAAO,EAAEA,OAAQ;YAACyB,CAAC,EAAC,MAAM;YAACC,CAAC,EAAC,UAAU;YAAAhK,QAAA,eACvClB,OAAA;cAAGiB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAC/C1B,qBAAqB,CAAC+J,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6B,SAAS,EAAE7B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE8B,OAAO;YAAC;cAAAlK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNtB,OAAA,CAAC+H,iBAAiB;QACdvC,SAAS,EAAEA,SAAU;QACrBT,OAAO,EAAEA,OAAQ;QACjBc,KAAK,EAAEA;MAAM;QAAA1E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eAGFtB,OAAA;QAAKiB,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAE5ClB,OAAA;UAAKiB,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBACzDlB,OAAA,CAAC4E,iBAAiB;YACdE,gBAAgB,EAAEA,gBAAiB;YACnCC,OAAO,EAAEA,OAAQ;YACjBC,mBAAmB,EAAEA,mBAAoB;YACzCC,gBAAgB,EAAEA;UAAiB;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACFtB,OAAA,CAAC0F,YAAY;YAACrF,QAAQ,EAAE4E;UAAiB;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAGNtB,OAAA;UAAKiB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC5B+D,gBAAgB,gBACbjF,OAAA,CAACoC,cAAc;YACX/B,QAAQ,EAAE4E,gBAAiB;YAC3B7E,MAAM,EAAEuJ,cAAe;YACvBhJ,KAAK,EAAEkJ;UAAc;YAAA1I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,gBAEFtB,OAAA;YAAKiB,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACjFlB,OAAA;cAAKiB,SAAS,EAAC,oBAAoB;cAAAC,QAAA,eAC/BlB,OAAA,CAACvB,QAAQ;gBAACwD,IAAI,EAAE,EAAG;gBAAChB,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNtB,OAAA;cAAIiB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9FtB,OAAA;cAAGiB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA4D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENtB,OAAA,CAACL,YAAY;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAAC2H,GAAA,CApKID,SAAS;EAAA,QACWvL,SAAS,EACdD,WAAW,EACXK,WAAW,EAC2BN,WAAW,EAIxBA,WAAW,EACnCA,WAAW;AAAA;AAAA+N,GAAA,GAT3BtC,SAAS;AAsKf,eAAeA,SAAS;AAAC,IAAAzH,EAAA,EAAAY,GAAA,EAAAwC,GAAA,EAAAc,GAAA,EAAAS,GAAA,EAAAG,GAAA,EAAA0C,GAAA,EAAAuC,GAAA;AAAAC,YAAA,CAAAhK,EAAA;AAAAgK,YAAA,CAAApJ,GAAA;AAAAoJ,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAArF,GAAA;AAAAqF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}