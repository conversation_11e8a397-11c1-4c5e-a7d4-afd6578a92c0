import { Op, literal } from "sequelize"
import db from "../models/index.js"
import { uploadImage, cleanupUploadedFiles } from "../utils/imageUpload.js"
import UserType from "../constants/UserType.js"
import * as questionService from "../services/question.service.js"

export const getQuestion = async (req, res, next) => {
    const sortOrder = req.query.sortOrder || 'DESC'
    const search = req.query.search || ''
    const page = parseInt(req.query.page, 10) || 1
    const limit = parseInt(req.query.limit, 10) || 10

    const result = await questionService.getQuestionsWithFilter({
        sortOrder,
        search,
        page,
        limit
    })

    return res.status(200).json({
        message: '<PERSON><PERSON>y danh sách câu hỏi thành công',
        ...result,
    })
}

export const getQuestionById = async (req, res) => {
    const { id } = req.params

    const questionDetail = await db.Question.findByPk(id, {
        include: [
            {
                model: db.Statement,
                as: 'statements',
                attributes: ['id', 'content', 'isCorrect', 'imageUrl', 'difficulty'], // 📝 Chọn các trường cần thiết
            },
        ],
    })

    if (!questionDetail) {
        return res.status(404).json({ message: 'Câu hỏi không tồn tại' })
    }

    return res.status(200).json({
        message: 'Chi tiết câu hỏi kèm đáp án',
        data: questionDetail,
    })
}

export const findQuestions = async (req, res) => {
    const { search } = req.query;
    const limit = 5;

    const searchTerm = search?.trim();
    let questions = [];

    if (!searchTerm) {
        // Không có từ khóa => lấy mới nhất
        questions = await db.Question.findAll({
            limit,
            order: [['createdAt', 'DESC']],
        });
    } else if (!isNaN(searchTerm)) {
        // Nếu search là số => tìm theo id
        questions = await db.Question.findAll({
            where: {
                id: Number(searchTerm),
            },
            limit,
        });
    } else {
        // Tìm kiếm gần đúng bằng LIKE
        questions = await db.Question.findAll({
            where: {
                content: {
                    [db.Sequelize.Op.like]: `%${searchTerm}%`,
                },
            },
            order: [['createdAt', 'DESC']],
            limit,
        });
    }

    return res.status(200).json({
        message: 'Danh sách câu hỏi',
        data: questions,
    });
};


export const getQuestionByExamId = async (req, res) => {
    const { examId } = req.params

    if (!examId) {
        return res.status(400).json({ message: "examId không hợp lệ!" })
    }

    const exam = await db.Exam.findByPk(examId, {
        include: [
            {
                model: db.Question,
                as: 'questions',
                through: { attributes: [] },
                include: [
                    {
                        model: db.Statement,
                        as: 'statements',
                        attributes: ['id', 'content', 'imageUrl'],
                    },
                ],
                attributes: ['id', 'content', 'typeOfQuestion', 'imageUrl'],
            },
        ],
    })

    if (!exam) {
        return res.status(404).json({ message: "Không tìm thấy đề thi!" })
    }

    return res.status(200).json({
        message: "Lấy danh sách câu hỏi thành công!",
        data: exam.questions,
    })
}

export const postQuestion = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    const uploadedFiles = []
    try {
        const { questionData, statementOptions, examId } = JSON.parse(req.body.data)
        const questionImage = req.files?.questionImage?.[0]
        const solutionImage = req.files?.solutionImage?.[0]
        const statementImages = req.files?.statementImages || []

        if (!questionData) {
            return res.status(400).json({ message: "Dữ liệu câu hỏi không hợp lệ!" })
        }

        const questionImageUrl = await uploadImage(questionImage, 'questionImage')
        if (questionImageUrl) uploadedFiles.push(questionImageUrl)

        const solutionImageUrl = await uploadImage(solutionImage, 'solutionImage')
        if (solutionImageUrl) uploadedFiles.push(solutionImageUrl)

        const newQuestion = await db.Question.create(
            {
                ...questionData,
                imageUrl: questionImageUrl,
                solutionImageUrl: solutionImageUrl
            },
            { transaction }
        )

        let statements = []
        let imageIndex = 0

        if (Array.isArray(statementOptions) && statementOptions.length) {
            statements = await Promise.all(
                statementOptions.map(async (statement, index) => {
                    let statementImageUrl = null

                    if (statement.needImage && statementImages[imageIndex]) {
                        statementImageUrl = await uploadImage(statementImages[imageIndex], 'statementImage')
                        if (statementImageUrl) uploadedFiles.push(statementImageUrl)
                        imageIndex++
                    }

                    return db.Statement.create(
                        {
                            ...statement,
                            questionId: newQuestion.id,
                            imageUrl: statementImageUrl,
                            order: index + 1
                        },
                        { transaction }
                    )
                })
            )
        }

        if (examId) {
            const exam = await db.Exam.findByPk(examId, { transaction })
            if (!exam) {
                await transaction.rollback()
                await cleanupUploadedFiles(uploadedFiles)
                return res.status(404).json({ message: "Đề thi không tồn tại!" })
            }

            const added = await db.ExamQuestions.create(
                { examId, questionId: newQuestion.id },
                { transaction }
            )

            if (!added) {
                await transaction.rollback()
                await cleanupUploadedFiles(uploadedFiles)
                return res.status(500).json({ message: "Lỗi khi thêm câu hỏi vào đề thi!" })
            }
        }

        await transaction.commit()

        return res.status(201).json({
            message: "Thêm câu hỏi thành công!",
            question: newQuestion,
            statements,
        })

    } catch (error) {
        await transaction.rollback()
        await cleanupUploadedFiles(uploadedFiles)

        console.error("Lỗi khi thêm câu hỏi:", error)
        return res.status(500).json({ message: "Lỗi server", error: error.message })
    }
}

export const putQuestion = async (req, res) => {
    const transaction = await db.sequelize.transaction();
    try {
        const { id } = req.params;
        const { questionData, statements } = req.body;

        // Kiểm tra xem câu hỏi có tồn tại không
        const existingQuestion = await db.Question.findByPk(id, { transaction });

        if (!existingQuestion) {
            await transaction.rollback();
            return res.status(404).json({ message: "Câu hỏi không tồn tại!" });
        }

        // Lọc bỏ các trường không được cập nhật
        const allowedFields = [
            "content",
            "difficulty",
            "chapter",
            "class",
            "description",
            "correctAnswer",
            "solution",
            "solutionUrl",
        ];

        const updateData = {};
        allowedFields.forEach((field) => {
            if (questionData[field] !== undefined) {
                updateData[field] = questionData[field];
            }
        });

        // Cập nhật câu hỏi (Không cập nhật imageUrl, solutionUrl)
        const [updated] = await db.Question.update(updateData, {
            where: { id },
            transaction,
        });

        if (!updated) {
            await transaction.rollback();
            return res.status(500).json({ message: "Lỗi khi cập nhật câu hỏi!" });
        }

        // Cập nhật danh sách mệnh đề (Không cập nhật imageUrl của statement)
        if (Array.isArray(statements) && statements.length > 0) {
            await Promise.all(
                statements.map(async (statement) => {
                    const { id: statementId, content, isCorrect, difficulty } = statement;

                    if (!statementId) return;

                    // Chỉ cập nhật các trường được phép
                    const statementUpdateData = {};
                    if (content !== undefined) statementUpdateData.content = content;
                    if (isCorrect !== undefined) statementUpdateData.isCorrect = isCorrect;
                    if (difficulty !== undefined) statementUpdateData.difficulty = difficulty;

                    await db.Statement.update(statementUpdateData, {
                        where: { id: statementId, questionId: id },
                        transaction,
                    });
                })
            );
        }

        // Commit transaction nếu mọi thứ thành công
        await transaction.commit();

        // Trả về dữ liệu cập nhật mới
        const updatedQuestion = await db.Question.findByPk(id, {
            include: [{ model: db.Statement, as: "statements" }],
        });

        return res.status(200).json({
            message: "Cập nhật câu hỏi và mệnh đề thành công!",
            data: updatedQuestion,
        });
    } catch (error) {
        await transaction.rollback();
        console.error("Lỗi khi cập nhật câu hỏi:", error);
        return res.status(500).json({ message: "Lỗi server!", error: error.message });
    }
};


export const putQuestionImage = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    const { id } = req.params

    try {
        const question = await db.Question.findByPk(id, { transaction })

        if (!question) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Câu hỏi không tồn tại.' })
        }

        const oldImageUrl = question.imageUrl
        const newImageFile = req.file
        let newImageUrl = null
        if (newImageFile) {
            newImageUrl = await uploadImage(newImageFile, 'questionImage')
        }

        const [updated] = await db.Question.update(
            { imageUrl: newImageUrl },
            { where: { id }, transaction }
        )

        if (!updated) {
            await cleanupUploadedFiles([newImageUrl])
            await transaction.rollback()
            return res.status(500).json({ message: 'Lỗi khi cập nhật ảnh câu hỏi.' })
        }

        if (oldImageUrl) {
            try {
                await cleanupUploadedFiles([oldImageUrl])
                console.log(`Đã xóa ảnh cũ: ${oldImageUrl}`)
            } catch (err) {
                console.error(`Lỗi khi xóa ảnh cũ: ${oldImageUrl}`, err)
                await cleanupUploadedFiles([newImageUrl])
                await transaction.rollback()
                return res.status(500).json({ message: 'Lỗi khi xóa ảnh cũ.', error: err.message })
            }
        }

        await transaction.commit()

        return res.status(200).json({
            message: 'Cập nhật ảnh câu hỏi thành công.',
            oldImageUrl,
            newImageUrl,
        })

    } catch (error) {
        console.error('Lỗi khi cập nhật ảnh câu hỏi:', error)
        await transaction.rollback()
        return res.status(500).json({ message: 'Lỗi server.', error: error.message })
    }
}

export const putQuestionSolutionImage = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    const { id } = req.params

    try {
        const question = await db.Question.findByPk(id, { transaction })

        if (!question) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Câu hỏi không tồn tại.' })
        }

        const oldImageUrl = question.solutionImageUrl
        const newImageFile = req.file
        let newImageUrl = null
        if (newImageFile) {
            newImageUrl = await uploadImage(newImageFile, 'solutionImage')
        }

        const [updated] = await db.Question.update(
            { solutionImageUrl: newImageUrl },
            { where: { id }, transaction }
        )

        if (!updated) {
            await cleanupUploadedFiles([newImageUrl])
            await transaction.rollback()
            return res.status(500).json({ message: 'Lỗi khi cập nhật ảnh câu hỏi.' })
        }

        if (oldImageUrl) {
            try {
                await cleanupUploadedFiles([oldImageUrl])
                console.log(`Đã xóa ảnh cũ: ${oldImageUrl}`)
            } catch (err) {
                console.error(`Lỗi khi xóa ảnh cũ: ${oldImageUrl}`, err)
                await cleanupUploadedFiles([newImageUrl])
                await transaction.rollback()
                return res.status(500).json({ message: 'Lỗi khi xóa ảnh cũ.', error: err.message })
            }
        }

        await transaction.commit()

        return res.status(200).json({
            message: 'Cập nhật ảnh câu hỏi lời giải thành công.',
            oldImageUrl,
            newImageUrl,
        })

    } catch (error) {
        console.error('Lỗi khi cập nhật ảnh câu hỏi:', error)
        await transaction.rollback()
        return res.status(500).json({ message: 'Lỗi server.', error: error.message })
    }
}

export const putQuestionsExam = async (req, res) => {
    const { questions } = req.body;
    const { examId } = req.params;
    const transaction = await db.sequelize.transaction();

    const code = await db.AllCode.findAll({
        where: {
            type: 'chapter',
        }
    });

    try {
        const exam = await db.Exam.findByPk(examId, { transaction });
        if (!exam) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Đề thi không tồn tại.' });
        }

        // 1. Xoá câu hỏi không còn trong danh sách
        const questionsIdsExam = await db.ExamQuestions.findAll({
            where: { examId },
            attributes: ['questionId'],
            transaction
        });
        const currentIds = questionsIdsExam.map(q => q.questionId);
        const incomingIds = questions.filter(q => !q.isNewQuestion).map(q => q.id);
        const toDeleteIds = currentIds.filter(id => !incomingIds.includes(id));

        if (toDeleteIds.length > 0) {
            await db.ExamQuestions.destroy({ where: { examId, questionId: toDeleteIds }, transaction });
            // Nếu cần: await db.Question.destroy({ where: { id: toDeleteIds }, transaction });
        }

        // 2. Tạo và cập nhật các câu hỏi
        for (let i = 0; i < questions.length; i++) {
            const q = questions[i];
            const chapter = code.find(c => c.code === q.chapter) ? q.chapter : null
            const baseData = {
                ...q,
                correctAnswer: q.correctAnswer ? q.correctAnswer.trim().replace(',', '.') : null,
                solution: q.solution || null,
                chapter: chapter
            };

            if (q.isNewQuestion) {
                const { id, ...qData } = baseData;
                const newQuestion = await db.Question.create(qData, { transaction });
                await db.ExamQuestions.create({
                    examId,
                    questionId: newQuestion.id,
                    order: q.ExamQuestions?.order || i
                }, { transaction });
            } else {
                await db.Question.update(baseData, { where: { id: q.id }, transaction });
                await db.ExamQuestions.update(
                    { order: q.ExamQuestions?.order || i },
                    { where: { questionId: q.id, examId }, transaction }
                );
            }

            // 3. Xử lý statements nếu có
            if (q.typeOfQuestion === "TN" || q.typeOfQuestion === "DS") {
                const existingStatements = await db.Statement.findAll({
                    where: { questionId: q.id },
                    attributes: ['id'],
                    transaction
                });
                const existingIds = existingStatements.map(s => s.id);
                const incomingStatements = Array.isArray(q.statements) ? q.statements : [];
                const incomingIds = incomingStatements.filter(s => !s.isNewStatement).map(s => s.id);
                const toDeleteIds = existingIds.filter(id => !incomingIds.includes(id));

                // Xoá các statements không còn
                if (toDeleteIds.length > 0) {
                    await db.Statement.destroy({
                        where: { id: toDeleteIds, questionId: q.id },
                        transaction
                    });
                }

                // Tạo/cập nhật lại các statements hiện có
                for (let j = 0; j < incomingStatements.length; j++) {
                    const s = incomingStatements[j];
                    const data = {
                        ...s,
                        questionId: q.id,
                        order: s.order || j
                    };

                    if (s.isNewStatement) {
                        await db.Statement.create(data, { transaction });
                    } else {
                        await db.Statement.update(data, {
                            where: { id: s.id, questionId: q.id },
                            transaction
                        });
                    }
                }
            }


        }

        await transaction.commit();
        return res.status(200).json({ message: 'Cập nhật câu hỏi thành công.' });

    } catch (error) {
        await transaction.rollback();
        console.error('Lỗi khi cập nhật câu hỏi:', error);
        return res.status(500).json({ message: 'Lỗi server.', error: error.message });
    }
};





export const deleteQuestion = async (req, res) => {
    const { id } = req.params
    const transaction = await db.sequelize.transaction()

    try {
        // Tìm câu hỏi và các statement liên quan
        const question = await db.Question.findByPk(id, {
            include: [
                {
                    model: db.Statement,
                    as: 'statements',
                    attributes: ['id', 'imageUrl']
                }
            ],
            transaction
        })

        if (!question) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Câu hỏi không tồn tại' })
        }

        // Thu thập tất cả các URL ảnh cần xóa
        const imagesToDelete = []

        // Thêm ảnh câu hỏi nếu có
        if (question.imageUrl) {
            imagesToDelete.push(question.imageUrl)
        }

        // Thêm ảnh lời giải nếu có
        if (question.solutionImageUrl) {
            imagesToDelete.push(question.solutionImageUrl)
        }

        // Thêm ảnh của các statement nếu có
        if (question.statements && question.statements.length > 0) {
            question.statements.forEach(statement => {
                if (statement.imageUrl) {
                    imagesToDelete.push(statement.imageUrl)
                }
            })
        }

        // Xóa câu hỏi (cascade sẽ tự động xóa statements)
        const deleted = await db.Question.destroy({
            where: { id },
            transaction
        })

        if (!deleted) {
            await transaction.rollback()
            return res.status(500).json({ message: 'Lỗi khi xóa câu hỏi' })
        }

        // Commit transaction trước khi xóa ảnh
        await transaction.commit()

        // Xóa tất cả ảnh liên quan
        if (imagesToDelete.length > 0) {
            try {
                await cleanupUploadedFiles(imagesToDelete)
                // console.log(`Đã xóa ${imagesToDelete.length} ảnh liên quan đến câu hỏi ${id}:`, imagesToDelete)
            } catch (err) {
                // console.error(`Lỗi khi xóa ảnh liên quan đến câu hỏi ${id}:`, err)
                // Không return error vì câu hỏi đã được xóa thành công
            }
        }

        return res.status(200).json({
            message: 'Xóa câu hỏi thành công',
            deletedImages: imagesToDelete.length
        })

    } catch (error) {
        await transaction.rollback()
        console.error('Lỗi khi xóa câu hỏi:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

export const deleteQuestionImage = async (req, res) => {
    const { id } = req.params
    const transaction = await db.sequelize.transaction()
    try {
        const question = await db.Question.findByPk(id, { transaction })
        if (!question) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Câu hỏi không tồn tại.' })
        }
        const oldImageUrl = question.imageUrl
        const [updated] = await db.Question.update(
            { imageUrl: null },
            { where: { id }, transaction }
        )
        if (!updated) {
            await transaction.rollback()
            return res.status(500).json({ message: 'Lỗi khi xóa ảnh câu hỏi.' })
        }

        if (oldImageUrl) {
            try {
                await cleanupUploadedFiles([oldImageUrl])
                console.log(`Đã xóa ảnh cũ: ${oldImageUrl}`)
            } catch (err) {
                console.error(`Lỗi khi xóa ảnh cũ: ${oldImageUrl}`, err)
                await transaction.rollback()
                return res.status(500).json({ message: 'Lỗi khi xóa ảnh cũ.', error: err.message })
            }
        }

        await transaction.commit()
        return res.status(200).json({
            message: 'Xóa ảnh câu hỏi thành công.',
            oldImageUrl
        })
    } catch (error) {
        console.error('Lỗi khi xóa ảnh câu hỏi:', error)
        await transaction.rollback()
        return res.status(500).json({ message: 'Lỗi server.', error: error.message })
    }
}

