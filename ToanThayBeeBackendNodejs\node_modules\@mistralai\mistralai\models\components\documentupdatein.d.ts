import * as z from "zod";
import { Result as SafeParseR<PERSON>ult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type DocumentUpdateIn = {
    name?: string | null | undefined;
};
/** @internal */
export declare const DocumentUpdateIn$inboundSchema: z.ZodType<DocumentUpdateIn, z.ZodTypeDef, unknown>;
/** @internal */
export type DocumentUpdateIn$Outbound = {
    name?: string | null | undefined;
};
/** @internal */
export declare const DocumentUpdateIn$outboundSchema: z.ZodType<DocumentUpdateIn$Outbound, z.ZodTypeDef, DocumentUpdateIn>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace DocumentUpdateIn$ {
    /** @deprecated use `DocumentUpdateIn$inboundSchema` instead. */
    const inboundSchema: z.ZodType<DocumentUpdateIn, z.ZodTypeDef, unknown>;
    /** @deprecated use `DocumentUpdateIn$outboundSchema` instead. */
    const outboundSchema: z.ZodType<DocumentUpdateIn$Outbound, z.ZodTypeDef, DocumentUpdateIn>;
    /** @deprecated use `DocumentUpdateIn$Outbound` instead. */
    type Outbound = DocumentUpdateIn$Outbound;
}
export declare function documentUpdateInToJSON(documentUpdateIn: DocumentUpdateIn): string;
export declare function documentUpdateInFromJSON(jsonString: string): SafeParseResult<DocumentUpdateIn, SDKValidationError>;
//# sourceMappingURL=documentupdatein.d.ts.map